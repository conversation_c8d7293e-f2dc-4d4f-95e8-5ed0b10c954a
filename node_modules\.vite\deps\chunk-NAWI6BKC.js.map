{"version": 3, "sources": ["../../@vueuse/shared/index.mjs", "../../@vueuse/core/index.mjs"], "sourcesContent": ["import { shallowRef, watchEffect, readonly, unref, ref, isVue3, version, watch, customRef, getCurrentScope, onScopeDispose, effectScope, provide, inject, isRef, computed, reactive, toRefs as toRefs$1, toRef, isVue2, set as set$1, getCurrentInstance, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue-demi';\n\nvar __defProp$9 = Object.defineProperty;\nvar __defProps$6 = Object.defineProperties;\nvar __getOwnPropDescs$6 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$b = Object.getOwnPropertySymbols;\nvar __hasOwnProp$b = Object.prototype.hasOwnProperty;\nvar __propIsEnum$b = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$9 = (obj, key, value) => key in obj ? __defProp$9(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$9 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$b.call(b, prop))\n      __defNormalProp$9(a, prop, b[prop]);\n  if (__getOwnPropSymbols$b)\n    for (var prop of __getOwnPropSymbols$b(b)) {\n      if (__propIsEnum$b.call(b, prop))\n        __defNormalProp$9(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$6 = (a, b) => __defProps$6(a, __getOwnPropDescs$6(b));\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, __spreadProps$6(__spreadValues$9({}, options), {\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  }));\n  return readonly(result);\n}\n\nvar _a;\nconst isClient = typeof window !== \"undefined\";\nconst isDef = (val) => typeof val !== \"undefined\";\nconst assert = (condition, ...infos) => {\n  if (!condition)\n    console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isBoolean = (val) => typeof val === \"boolean\";\nconst isFunction = (val) => typeof val === \"function\";\nconst isNumber = (val) => typeof val === \"number\";\nconst isString = (val) => typeof val === \"string\";\nconst isObject = (val) => toString.call(val) === \"[object Object]\";\nconst isWindow = (val) => typeof window !== \"undefined\" && toString.call(val) === \"[object Window]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {\n};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst isIOS = isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);\nconst hasOwn = (val, key) => Object.prototype.hasOwnProperty.call(val, key);\n\nfunction resolveUnref(r) {\n  return typeof r === \"function\" ? r() : unref(r);\n}\n\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    return new Promise((resolve, reject) => {\n      Promise.resolve(filter(() => fn.apply(this, args), { fn, thisArg: this, args })).then(resolve).catch(reject);\n    });\n  }\n  return wrapper;\n}\nconst bypassFilter = (invoke) => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  let lastRejector = noop;\n  const _clearTimeout = (timer2) => {\n    clearTimeout(timer2);\n    lastRejector();\n    lastRejector = noop;\n  };\n  const filter = (invoke) => {\n    const duration = resolveUnref(ms);\n    const maxDuration = resolveUnref(options.maxWait);\n    if (timer)\n      _clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        _clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return Promise.resolve(invoke());\n    }\n    return new Promise((resolve, reject) => {\n      lastRejector = options.rejectOnCancel ? reject : resolve;\n      if (maxDuration && !maxTimer) {\n        maxTimer = setTimeout(() => {\n          if (timer)\n            _clearTimeout(timer);\n          maxTimer = null;\n          resolve(invoke());\n        }, maxDuration);\n      }\n      timer = setTimeout(() => {\n        if (maxTimer)\n          _clearTimeout(maxTimer);\n        maxTimer = null;\n        resolve(invoke());\n      }, duration);\n    });\n  };\n  return filter;\n}\nfunction throttleFilter(ms, trailing = true, leading = true, rejectOnCancel = false) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  let lastRejector = noop;\n  let lastValue;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n      lastRejector();\n      lastRejector = noop;\n    }\n  };\n  const filter = (_invoke) => {\n    const duration = resolveUnref(ms);\n    const elapsed = Date.now() - lastExec;\n    const invoke = () => {\n      return lastValue = _invoke();\n    };\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      lastValue = new Promise((resolve, reject) => {\n        lastRejector = rejectOnCancel ? reject : resolve;\n        timer = setTimeout(() => {\n          lastExec = Date.now();\n          isLeading = true;\n          resolve(invoke());\n          clear();\n        }, Math.max(0, duration - elapsed));\n      });\n    }\n    if (!leading && !timer)\n      timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n    return lastValue;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter) {\n  const isActive = ref(true);\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value)\n      extendFilter(...args);\n  };\n  return { isActive: readonly(isActive), pause, resume, eventFilter };\n}\n\nfunction __onlyVue3(name = \"this function\") {\n  if (isVue3)\n    return;\n  throw new Error(`[VueUse] ${name} is only works on Vue 3.`);\n}\nfunction __onlyVue27Plus(name = \"this function\") {\n  if (isVue3 || version.startsWith(\"2.7.\"))\n    return;\n  throw new Error(`[VueUse] ${name} is only works on Vue 2.7 or above.`);\n}\nconst directiveHooks = {\n  mounted: isVue3 ? \"mounted\" : \"inserted\",\n  updated: isVue3 ? \"updated\" : \"componentUpdated\",\n  unmounted: isVue3 ? \"unmounted\" : \"unbind\"\n};\n\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout)\n      setTimeout(() => reject(reason), ms);\n    else\n      setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise)\n      _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev)\n      await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some((k) => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\")\n    return target + delta;\n  const value = ((_a = target.match(/^-?[0-9]+\\.?[0-9]*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = parseFloat(value) + delta;\n  if (Number.isNaN(result))\n    return target;\n  return result + unit;\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0)\n        n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\n\nfunction computedWithControl(source, fn) {\n  let v = void 0;\n  let track;\n  let trigger;\n  const dirty = ref(true);\n  const update = () => {\n    dirty.value = true;\n    trigger();\n  };\n  watch(source, update, { flush: \"sync\" });\n  const get = isFunction(fn) ? fn : fn.get;\n  const set = isFunction(fn) ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty.value) {\n          v = get();\n          dirty.value = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  if (Object.isExtensible(result))\n    result.trigger = update;\n  return result;\n}\n\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\n\nfunction createEventHook() {\n  const fns = [];\n  const off = (fn) => {\n    const index = fns.indexOf(fn);\n    if (index !== -1)\n      fns.splice(index, 1);\n  };\n  const on = (fn) => {\n    fns.push(fn);\n    const offFn = () => off(fn);\n    tryOnScopeDispose(offFn);\n    return {\n      off: offFn\n    };\n  };\n  const trigger = (param) => {\n    fns.forEach((fn) => fn(param));\n  };\n  return {\n    on,\n    off,\n    trigger\n  };\n}\n\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return () => {\n    if (!initialized) {\n      state = scope.run(stateFactory);\n      initialized = true;\n    }\n    return state;\n  };\n}\n\nfunction createInjectionState(composable) {\n  const key = Symbol(\"InjectionState\");\n  const useProvidingState = (...args) => {\n    const state = composable(...args);\n    provide(key, state);\n    return state;\n  };\n  const useInjectedState = () => inject(key);\n  return [useProvidingState, useInjectedState];\n}\n\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!state) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\n\nfunction extendRef(ref, extend, { enumerable = false, unwrap = true } = {}) {\n  __onlyVue27Plus();\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\")\n      continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, { value, enumerable });\n    }\n  }\n  return ref;\n}\n\nfunction get(obj, key) {\n  if (key == null)\n    return unref(obj);\n  return unref(obj)[key];\n}\n\nfunction isDefined(v) {\n  return unref(v) != null;\n}\n\nvar __defProp$8 = Object.defineProperty;\nvar __getOwnPropSymbols$a = Object.getOwnPropertySymbols;\nvar __hasOwnProp$a = Object.prototype.hasOwnProperty;\nvar __propIsEnum$a = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$8 = (obj, key, value) => key in obj ? __defProp$8(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$8 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$a.call(b, prop))\n      __defNormalProp$8(a, prop, b[prop]);\n  if (__getOwnPropSymbols$a)\n    for (var prop of __getOwnPropSymbols$a(b)) {\n      if (__propIsEnum$a.call(b, prop))\n        __defNormalProp$8(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = __spreadValues$8({}, obj);\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\n\nfunction reactify(fn, options) {\n  const unrefFn = (options == null ? void 0 : options.computedGetter) === false ? unref : resolveUnref;\n  return function(...args) {\n    return computed(() => fn.apply(this, args.map((i) => unrefFn(i))));\n  };\n}\n\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  let options;\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    options = optionsOrKeys;\n    const { includeOwnProperties = true } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties)\n      keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(keys.map((key) => {\n    const value = obj[key];\n    return [\n      key,\n      typeof value === \"function\" ? reactify(value.bind(obj), options) : value\n    ];\n  }));\n}\n\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef))\n    return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value))\n        objectRef.value[p].value = value;\n      else\n        objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\n\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\n\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  return reactiveComputed(() => Object.fromEntries(Object.entries(toRefs$1(obj)).filter((e) => !flatKeys.includes(e[0]))));\n}\n\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  return reactive(Object.fromEntries(flatKeys.map((k) => [k, toRef(obj, k)])));\n}\n\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = defaultValue;\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = defaultValue;\n      trigger();\n    }, resolveUnref(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\n\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(debounceFilter(ms, options), fn);\n}\n\nfunction refDebounced(value, ms = 200, options = {}) {\n  const debounced = ref(value.value);\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return debounced;\n}\n\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\n\nfunction useThrottleFn(fn, ms = 200, trailing = false, leading = true, rejectOnCancel = false) {\n  return createFilterWrapper(throttleFilter(ms, trailing, leading, rejectOnCancel), fn);\n}\n\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0)\n    return value;\n  const throttled = ref(value.value);\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\n\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking)\n      track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source)\n      return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false)\n      return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering)\n      trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = (v) => set(v, false);\n  const peek = () => get(false);\n  const lay = (v) => set(v, false);\n  return extendRef(ref, {\n    get,\n    set,\n    untrackedGet,\n    silentSet,\n    peek,\n    lay\n  }, { enumerable: true });\n}\nconst controlledRef = refWithControl;\n\nfunction resolveRef(r) {\n  return typeof r === \"function\" ? computed(r) : ref(r);\n}\n\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    if (isVue2) {\n      set$1(...args);\n    } else {\n      const [target, key, value] = args;\n      target[key] = value;\n    }\n  }\n}\n\nfunction syncRef(left, right, options = {}) {\n  var _a, _b;\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\",\n    transform = {}\n  } = options;\n  let watchLeft;\n  let watchRight;\n  const transformLTR = (_a = transform.ltr) != null ? _a : (v) => v;\n  const transformRTL = (_b = transform.rtl) != null ? _b : (v) => v;\n  if (direction === \"both\" || direction === \"ltr\") {\n    watchLeft = watch(left, (newValue) => right.value = transformLTR(newValue), { flush, deep, immediate });\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    watchRight = watch(right, (newValue) => left.value = transformRTL(newValue), { flush, deep, immediate });\n  }\n  return () => {\n    watchLeft == null ? void 0 : watchLeft();\n    watchRight == null ? void 0 : watchRight();\n  };\n}\n\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  if (!Array.isArray(targets))\n    targets = [targets];\n  return watch(source, (newValue) => targets.forEach((target) => target.value = newValue), { flush, deep, immediate });\n}\n\nvar __defProp$7 = Object.defineProperty;\nvar __defProps$5 = Object.defineProperties;\nvar __getOwnPropDescs$5 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$9 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$9 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$9 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$7 = (obj, key, value) => key in obj ? __defProp$7(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$7 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$9.call(b, prop))\n      __defNormalProp$7(a, prop, b[prop]);\n  if (__getOwnPropSymbols$9)\n    for (var prop of __getOwnPropSymbols$9(b)) {\n      if (__propIsEnum$9.call(b, prop))\n        __defNormalProp$7(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$5 = (a, b) => __defProps$5(a, __getOwnPropDescs$5(b));\nfunction toRefs(objectRef) {\n  if (!isRef(objectRef))\n    return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? new Array(objectRef.value.length) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        if (Array.isArray(objectRef.value)) {\n          const copy = [...objectRef.value];\n          copy[key] = v;\n          objectRef.value = copy;\n        } else {\n          const newObject = __spreadProps$5(__spreadValues$7({}, objectRef.value), { [key]: v });\n          Object.setPrototypeOf(newObject, objectRef.value);\n          objectRef.value = newObject;\n        }\n      }\n    }));\n  }\n  return result;\n}\n\nfunction tryOnBeforeMount(fn, sync = true) {\n  if (getCurrentInstance())\n    onBeforeMount(fn);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnBeforeUnmount(fn) {\n  if (getCurrentInstance())\n    onBeforeUnmount(fn);\n}\n\nfunction tryOnMounted(fn, sync = true) {\n  if (getCurrentInstance())\n    onMounted(fn);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnUnmounted(fn) {\n  if (getCurrentInstance())\n    onUnmounted(fn);\n}\n\nfunction createUntil(r, isNot = false) {\n  function toMatch(condition, { flush = \"sync\", deep = false, timeout, throwOnTimeout } = {}) {\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(r, (v) => {\n        if (condition(v) !== isNot) {\n          stop == null ? void 0 : stop();\n          resolve(v);\n        }\n      }, {\n        flush,\n        deep,\n        immediate: true\n      });\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(promiseTimeout(timeout, throwOnTimeout).then(() => resolveUnref(r)).finally(() => stop == null ? void 0 : stop()));\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value))\n      return toMatch((v) => v === value, options);\n    const { flush = \"sync\", deep = false, timeout, throwOnTimeout } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch([r, value], ([v1, v2]) => {\n        if (isNot !== (v1 === v2)) {\n          stop == null ? void 0 : stop();\n          resolve(v1);\n        }\n      }, {\n        flush,\n        deep,\n        immediate: true\n      });\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(promiseTimeout(timeout, throwOnTimeout).then(() => resolveUnref(r)).finally(() => {\n        stop == null ? void 0 : stop();\n        return resolveUnref(r);\n      }));\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch((v) => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch((v) => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(resolveUnref(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(resolveUnref(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  }\n}\nfunction until(r) {\n  return createUntil(r);\n}\n\nfunction useArrayEvery(list, fn) {\n  return computed(() => resolveUnref(list).every((element, index, array) => fn(resolveUnref(element), index, array)));\n}\n\nfunction useArrayFilter(list, fn) {\n  return computed(() => resolveUnref(list).map((i) => resolveUnref(i)).filter(fn));\n}\n\nfunction useArrayFind(list, fn) {\n  return computed(() => resolveUnref(resolveUnref(list).find((element, index, array) => fn(resolveUnref(element), index, array))));\n}\n\nfunction useArrayFindIndex(list, fn) {\n  return computed(() => resolveUnref(list).findIndex((element, index, array) => fn(resolveUnref(element), index, array)));\n}\n\nfunction findLast(arr, cb) {\n  let index = arr.length;\n  while (index-- > 0) {\n    if (cb(arr[index], index, arr))\n      return arr[index];\n  }\n  return void 0;\n}\nfunction useArrayFindLast(list, fn) {\n  return computed(() => resolveUnref(!Array.prototype.findLast ? findLast(resolveUnref(list), (element, index, array) => fn(resolveUnref(element), index, array)) : resolveUnref(list).findLast((element, index, array) => fn(resolveUnref(element), index, array))));\n}\n\nfunction useArrayJoin(list, separator) {\n  return computed(() => resolveUnref(list).map((i) => resolveUnref(i)).join(resolveUnref(separator)));\n}\n\nfunction useArrayMap(list, fn) {\n  return computed(() => resolveUnref(list).map((i) => resolveUnref(i)).map(fn));\n}\n\nfunction useArrayReduce(list, reducer, ...args) {\n  const reduceCallback = (sum, value, index) => reducer(resolveUnref(sum), resolveUnref(value), index);\n  return computed(() => {\n    const resolved = resolveUnref(list);\n    return args.length ? resolved.reduce(reduceCallback, resolveUnref(args[0])) : resolved.reduce(reduceCallback);\n  });\n}\n\nfunction useArraySome(list, fn) {\n  return computed(() => resolveUnref(list).some((element, index, array) => fn(resolveUnref(element), index, array)));\n}\n\nfunction useArrayUnique(list) {\n  return computed(() => [...new Set(resolveUnref(list).map((element) => resolveUnref(element)))]);\n}\n\nfunction useCounter(initialValue = 0, options = {}) {\n  const count = ref(initialValue);\n  const {\n    max = Infinity,\n    min = -Infinity\n  } = options;\n  const inc = (delta = 1) => count.value = Math.min(max, count.value + delta);\n  const dec = (delta = 1) => count.value = Math.max(min, count.value - delta);\n  const get = () => count.value;\n  const set = (val) => count.value = Math.max(min, Math.min(max, val));\n  const reset = (val = initialValue) => {\n    initialValue = val;\n    return set(val);\n  };\n  return { count, inc, dec, get, set, reset };\n}\n\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/;\nconst REGEX_FORMAT = /\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|SSS/g;\nconst defaultMeridiem = (hours, minutes, isLowercase, hasPeriod) => {\n  let m = hours < 12 ? \"AM\" : \"PM\";\n  if (hasPeriod)\n    m = m.split(\"\").reduce((acc, curr) => acc += `${curr}.`, \"\");\n  return isLowercase ? m.toLowerCase() : m;\n};\nconst formatDate = (date, formatStr, options = {}) => {\n  var _a;\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const meridiem = (_a = options.customMeridiem) != null ? _a : defaultMeridiem;\n  const matches = {\n    YY: () => String(years).slice(-2),\n    YYYY: () => years,\n    M: () => month + 1,\n    MM: () => `${month + 1}`.padStart(2, \"0\"),\n    MMM: () => date.toLocaleDateString(options.locales, { month: \"short\" }),\n    MMMM: () => date.toLocaleDateString(options.locales, { month: \"long\" }),\n    D: () => String(days),\n    DD: () => `${days}`.padStart(2, \"0\"),\n    H: () => String(hours),\n    HH: () => `${hours}`.padStart(2, \"0\"),\n    h: () => `${hours % 12 || 12}`.padStart(1, \"0\"),\n    hh: () => `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: () => String(minutes),\n    mm: () => `${minutes}`.padStart(2, \"0\"),\n    s: () => String(seconds),\n    ss: () => `${seconds}`.padStart(2, \"0\"),\n    SSS: () => `${milliseconds}`.padStart(3, \"0\"),\n    d: () => day,\n    dd: () => date.toLocaleDateString(options.locales, { weekday: \"narrow\" }),\n    ddd: () => date.toLocaleDateString(options.locales, { weekday: \"short\" }),\n    dddd: () => date.toLocaleDateString(options.locales, { weekday: \"long\" }),\n    A: () => meridiem(hours, minutes),\n    AA: () => meridiem(hours, minutes, false, true),\n    a: () => meridiem(hours, minutes, true),\n    aa: () => meridiem(hours, minutes, true, true)\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => $1 || matches[match]());\n};\nconst normalizeDate = (date) => {\n  if (date === null)\n    return new Date(NaN);\n  if (date === void 0)\n    return new Date();\n  if (date instanceof Date)\n    return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n};\nfunction useDateFormat(date, formatStr = \"HH:mm:ss\", options = {}) {\n  return computed(() => formatDate(normalizeDate(resolveUnref(date)), resolveUnref(formatStr), options));\n}\n\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = ref(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    const intervalValue = resolveUnref(interval);\n    if (intervalValue <= 0)\n      return;\n    isActive.value = true;\n    if (immediateCallback)\n      cb();\n    clean();\n    timer = setInterval(cb, intervalValue);\n  }\n  if (immediate && isClient)\n    resume();\n  if (isRef(interval) || isFunction(interval)) {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient)\n        resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nvar __defProp$6 = Object.defineProperty;\nvar __getOwnPropSymbols$8 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$8 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$8 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$6 = (obj, key, value) => key in obj ? __defProp$6(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$6 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$8.call(b, prop))\n      __defNormalProp$6(a, prop, b[prop]);\n  if (__getOwnPropSymbols$8)\n    for (var prop of __getOwnPropSymbols$8(b)) {\n      if (__propIsEnum$8.call(b, prop))\n        __defNormalProp$6(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true,\n    callback\n  } = options;\n  const counter = ref(0);\n  const update = () => counter.value += 1;\n  const reset = () => {\n    counter.value = 0;\n  };\n  const controls = useIntervalFn(callback ? () => {\n    update();\n    callback(counter.value);\n  } : update, interval, { immediate });\n  if (exposeControls) {\n    return __spreadValues$6({\n      counter,\n      reset\n    }, controls);\n  } else {\n    return counter;\n  }\n}\n\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = ref((_a = options.initialValue) != null ? _a : null);\n  watch(source, () => ms.value = timestamp(), options);\n  return ms;\n}\n\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true\n  } = options;\n  const isPending = ref(false);\n  let timer = null;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = null;\n      cb(...args);\n    }, resolveUnref(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient)\n      start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending: readonly(isPending),\n    start,\n    stop\n  };\n}\n\nvar __defProp$5 = Object.defineProperty;\nvar __getOwnPropSymbols$7 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$7 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$7 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$5 = (obj, key, value) => key in obj ? __defProp$5(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$5 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$7.call(b, prop))\n      __defNormalProp$5(a, prop, b[prop]);\n  if (__getOwnPropSymbols$7)\n    for (var prop of __getOwnPropSymbols$7(b)) {\n      if (__propIsEnum$7.call(b, prop))\n        __defNormalProp$5(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    callback\n  } = options;\n  const controls = useTimeoutFn(callback != null ? callback : noop, interval, options);\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return __spreadValues$5({\n      ready\n    }, controls);\n  } else {\n    return ready;\n  }\n}\n\nfunction useToNumber(value, options = {}) {\n  const {\n    method = \"parseFloat\",\n    radix,\n    nanToZero\n  } = options;\n  return computed(() => {\n    let resolved = resolveUnref(value);\n    if (typeof resolved === \"string\")\n      resolved = Number[method](resolved, radix);\n    if (nanToZero && isNaN(resolved))\n      resolved = 0;\n    return resolved;\n  });\n}\n\nfunction useToString(value) {\n  return computed(() => `${resolveUnref(value)}`);\n}\n\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const _value = ref(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      _value.value = value;\n      return _value.value;\n    } else {\n      const truthy = resolveUnref(truthyValue);\n      _value.value = _value.value === truthy ? resolveUnref(falsyValue) : truthy;\n      return _value.value;\n    }\n  }\n  if (valueIsRef)\n    return toggle;\n  else\n    return [_value, toggle];\n}\n\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [\n    ...source instanceof Function ? source() : Array.isArray(source) ? source : unref(source)\n  ];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = new Array(oldList.length);\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found)\n        added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\n\nvar __getOwnPropSymbols$6 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$6 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$6 = Object.prototype.propertyIsEnumerable;\nvar __objRest$5 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$6.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$6)\n    for (var prop of __getOwnPropSymbols$6(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$6.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchWithFilter(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter = bypassFilter\n  } = _a, watchOptions = __objRest$5(_a, [\n    \"eventFilter\"\n  ]);\n  return watch(source, createFilterWrapper(eventFilter, cb), watchOptions);\n}\n\nvar __getOwnPropSymbols$5 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$5 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$5 = Object.prototype.propertyIsEnumerable;\nvar __objRest$4 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$5.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$5)\n    for (var prop of __getOwnPropSymbols$5(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$5.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchAtMost(source, cb, options) {\n  const _a = options, {\n    count\n  } = _a, watchOptions = __objRest$4(_a, [\n    \"count\"\n  ]);\n  const current = ref(0);\n  const stop = watchWithFilter(source, (...args) => {\n    current.value += 1;\n    if (current.value >= resolveUnref(count))\n      nextTick(() => stop());\n    cb(...args);\n  }, watchOptions);\n  return { count: current, stop };\n}\n\nvar __defProp$4 = Object.defineProperty;\nvar __defProps$4 = Object.defineProperties;\nvar __getOwnPropDescs$4 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$4 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$4 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$4 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$4 = (obj, key, value) => key in obj ? __defProp$4(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$4 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$4.call(b, prop))\n      __defNormalProp$4(a, prop, b[prop]);\n  if (__getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(b)) {\n      if (__propIsEnum$4.call(b, prop))\n        __defNormalProp$4(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$4 = (a, b) => __defProps$4(a, __getOwnPropDescs$4(b));\nvar __objRest$3 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$4.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$4.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchDebounced(source, cb, options = {}) {\n  const _a = options, {\n    debounce = 0,\n    maxWait = void 0\n  } = _a, watchOptions = __objRest$3(_a, [\n    \"debounce\",\n    \"maxWait\"\n  ]);\n  return watchWithFilter(source, cb, __spreadProps$4(__spreadValues$4({}, watchOptions), {\n    eventFilter: debounceFilter(debounce, { maxWait })\n  }));\n}\n\nvar __defProp$3 = Object.defineProperty;\nvar __defProps$3 = Object.defineProperties;\nvar __getOwnPropDescs$3 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$3 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$3 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$3 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$3 = (obj, key, value) => key in obj ? __defProp$3(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$3 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$3.call(b, prop))\n      __defNormalProp$3(a, prop, b[prop]);\n  if (__getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(b)) {\n      if (__propIsEnum$3.call(b, prop))\n        __defNormalProp$3(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$3 = (a, b) => __defProps$3(a, __getOwnPropDescs$3(b));\nvar __objRest$2 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$3.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$3.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchIgnorable(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter = bypassFilter\n  } = _a, watchOptions = __objRest$2(_a, [\n    \"eventFilter\"\n  ]);\n  const filteredCb = createFilterWrapper(eventFilter, cb);\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    const ignore = ref(false);\n    ignorePrevAsyncUpdates = () => {\n    };\n    ignoreUpdates = (updater) => {\n      ignore.value = true;\n      updater();\n      ignore.value = false;\n    };\n    stop = watch(source, (...args) => {\n      if (!ignore.value)\n        filteredCb(...args);\n    }, watchOptions);\n  } else {\n    const disposables = [];\n    const ignoreCounter = ref(0);\n    const syncCounter = ref(0);\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter.value = syncCounter.value;\n    };\n    disposables.push(watch(source, () => {\n      syncCounter.value++;\n    }, __spreadProps$3(__spreadValues$3({}, watchOptions), { flush: \"sync\" })));\n    ignoreUpdates = (updater) => {\n      const syncCounterPrev = syncCounter.value;\n      updater();\n      ignoreCounter.value += syncCounter.value - syncCounterPrev;\n    };\n    disposables.push(watch(source, (...args) => {\n      const ignore = ignoreCounter.value > 0 && ignoreCounter.value === syncCounter.value;\n      ignoreCounter.value = 0;\n      syncCounter.value = 0;\n      if (ignore)\n        return;\n      filteredCb(...args);\n    }, watchOptions));\n    stop = () => {\n      disposables.forEach((fn) => fn());\n    };\n  }\n  return { stop, ignoreUpdates, ignorePrevAsyncUpdates };\n}\n\nfunction watchOnce(source, cb, options) {\n  const stop = watch(source, (...args) => {\n    nextTick(() => stop());\n    return cb(...args);\n  }, options);\n}\n\nvar __defProp$2 = Object.defineProperty;\nvar __defProps$2 = Object.defineProperties;\nvar __getOwnPropDescs$2 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$2.call(b, prop))\n      __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(b)) {\n      if (__propIsEnum$2.call(b, prop))\n        __defNormalProp$2(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$2 = (a, b) => __defProps$2(a, __getOwnPropDescs$2(b));\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$2.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$2.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchPausable(source, cb, options = {}) {\n  const _a = options, {\n    eventFilter: filter\n  } = _a, watchOptions = __objRest$1(_a, [\n    \"eventFilter\"\n  ]);\n  const { eventFilter, pause, resume, isActive } = pausableFilter(filter);\n  const stop = watchWithFilter(source, cb, __spreadProps$2(__spreadValues$2({}, watchOptions), {\n    eventFilter\n  }));\n  return { stop, pause, resume, isActive };\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __defProps$1 = Object.defineProperties;\nvar __getOwnPropDescs$1 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$1 = (a, b) => __defProps$1(a, __getOwnPropDescs$1(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$1.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$1.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction watchThrottled(source, cb, options = {}) {\n  const _a = options, {\n    throttle = 0,\n    trailing = true,\n    leading = true\n  } = _a, watchOptions = __objRest(_a, [\n    \"throttle\",\n    \"trailing\",\n    \"leading\"\n  ]);\n  return watchWithFilter(source, cb, __spreadProps$1(__spreadValues$1({}, watchOptions), {\n    eventFilter: throttleFilter(throttle, trailing, leading)\n  }));\n}\n\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn)\n      return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const { ignoreUpdates } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return __spreadProps(__spreadValues({}, res), {\n    trigger\n  });\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources))\n    return sources;\n  if (Array.isArray(sources))\n    return sources.map((item) => getOneWatchSource(item));\n  return getOneWatchSource(sources);\n}\nfunction getOneWatchSource(source) {\n  return typeof source === \"function\" ? source() : unref(source);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\n\nfunction whenever(source, cb, options) {\n  return watch(source, (v, ov, onInvalidate) => {\n    if (v)\n      cb(v, ov, onInvalidate);\n  }, options);\n}\n\nexport { __onlyVue27Plus, __onlyVue3, assert, refAutoReset as autoResetRef, bypassFilter, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, directiveHooks, computedEager as eagerComputed, extendRef, formatDate, get, hasOwn, identity, watchIgnorable as ignorableWatch, increaseWithUnit, invoke, isBoolean, isClient, isDef, isDefined, isFunction, isIOS, isNumber, isObject, isString, isWindow, makeDestructurable, noop, normalizeDate, now, objectPick, pausableFilter, watchPausable as pausableWatch, promiseTimeout, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toReactive, toRefs, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useCounter, useDateFormat, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToNumber, useToString, useToggle, watchArray, watchAtMost, watchDebounced, watchIgnorable, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };\n", "import { noop, resolveUnref, isClient, isString, tryOnScopeDispose, isIOS, tryOnMounted, computedWithControl, promiseTimeout, isFunction, resolveRef, increaseWithUnit, useTimeoutFn, pausableWatch, createEventHook, timestamp, pausableFilter, watchIgnorable, debounceFilter, createFilterWrapper, bypassFilter, createSingletonPromise, toRefs, containsProp, until, hasOwn, throttleFilter, useDebounceFn, useThrottleFn, isObject, isNumber, useIntervalFn, clamp, syncRef, objectPick, tryOnUnmounted, watchWithFilter, identity, isDef } from '@vueuse/shared';\nexport * from '@vueuse/shared';\nimport { isRef, ref, shallowRef, watchEffect, computed, inject, unref, watch, getCurrentInstance, customRef, onUpdated, reactive, nextTick, onMounted, markRaw, readonly, getCurrentScope, isVue2, set, del, isReadonly, onBeforeUpdate } from 'vue-demi';\n\nfunction computedAsync(evaluationCallback, initialState, optionsOrRef) {\n  let options;\n  if (isRef(optionsOrRef)) {\n    options = {\n      evaluating: optionsOrRef\n    };\n  } else {\n    options = optionsOrRef || {};\n  }\n  const {\n    lazy = false,\n    evaluating = void 0,\n    shallow = false,\n    onError = noop\n  } = options;\n  const started = ref(!lazy);\n  const current = shallow ? shallowRef(initialState) : ref(initialState);\n  let counter = 0;\n  watchEffect(async (onInvalidate) => {\n    if (!started.value)\n      return;\n    counter++;\n    const counterAtBeginning = counter;\n    let hasFinished = false;\n    if (evaluating) {\n      Promise.resolve().then(() => {\n        evaluating.value = true;\n      });\n    }\n    try {\n      const result = await evaluationCallback((cancelCallback) => {\n        onInvalidate(() => {\n          if (evaluating)\n            evaluating.value = false;\n          if (!hasFinished)\n            cancelCallback();\n        });\n      });\n      if (counterAtBeginning === counter)\n        current.value = result;\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (evaluating && counterAtBeginning === counter)\n        evaluating.value = false;\n      hasFinished = true;\n    }\n  });\n  if (lazy) {\n    return computed(() => {\n      started.value = true;\n      return current.value;\n    });\n  } else {\n    return current;\n  }\n}\n\nfunction computedInject(key, options, defaultSource, treatDefaultAsFactory) {\n  let source = inject(key);\n  if (defaultSource)\n    source = inject(key, defaultSource);\n  if (treatDefaultAsFactory)\n    source = inject(key, defaultSource, treatDefaultAsFactory);\n  if (typeof options === \"function\") {\n    return computed((ctx) => options(source, ctx));\n  } else {\n    return computed({\n      get: (ctx) => options.get(source, ctx),\n      set: options.set\n    });\n  }\n}\n\nconst createUnrefFn = (fn) => {\n  return function(...args) {\n    return fn.apply(this, args.map((i) => unref(i)));\n  };\n};\n\nfunction unrefElement(elRef) {\n  var _a;\n  const plain = resolveUnref(elRef);\n  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;\n}\n\nconst defaultWindow = isClient ? window : void 0;\nconst defaultDocument = isClient ? window.document : void 0;\nconst defaultNavigator = isClient ? window.navigator : void 0;\nconst defaultLocation = isClient ? window.location : void 0;\n\nfunction useEventListener(...args) {\n  let target;\n  let events;\n  let listeners;\n  let options;\n  if (isString(args[0]) || Array.isArray(args[0])) {\n    [events, listeners, options] = args;\n    target = defaultWindow;\n  } else {\n    [target, events, listeners, options] = args;\n  }\n  if (!target)\n    return noop;\n  if (!Array.isArray(events))\n    events = [events];\n  if (!Array.isArray(listeners))\n    listeners = [listeners];\n  const cleanups = [];\n  const cleanup = () => {\n    cleanups.forEach((fn) => fn());\n    cleanups.length = 0;\n  };\n  const register = (el, event, listener, options2) => {\n    el.addEventListener(event, listener, options2);\n    return () => el.removeEventListener(event, listener, options2);\n  };\n  const stopWatch = watch(() => [unrefElement(target), resolveUnref(options)], ([el, options2]) => {\n    cleanup();\n    if (!el)\n      return;\n    cleanups.push(...events.flatMap((event) => {\n      return listeners.map((listener) => register(el, event, listener, options2));\n    }));\n  }, { immediate: true, flush: \"post\" });\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return stop;\n}\n\nlet _iOSWorkaround = false;\nfunction onClickOutside(target, handler, options = {}) {\n  const { window = defaultWindow, ignore = [], capture = true, detectIframe = false } = options;\n  if (!window)\n    return;\n  if (isIOS && !_iOSWorkaround) {\n    _iOSWorkaround = true;\n    Array.from(window.document.body.children).forEach((el) => el.addEventListener(\"click\", noop));\n  }\n  let shouldListen = true;\n  const shouldIgnore = (event) => {\n    return ignore.some((target2) => {\n      if (typeof target2 === \"string\") {\n        return Array.from(window.document.querySelectorAll(target2)).some((el) => el === event.target || event.composedPath().includes(el));\n      } else {\n        const el = unrefElement(target2);\n        return el && (event.target === el || event.composedPath().includes(el));\n      }\n    });\n  };\n  const listener = (event) => {\n    const el = unrefElement(target);\n    if (!el || el === event.target || event.composedPath().includes(el))\n      return;\n    if (event.detail === 0)\n      shouldListen = !shouldIgnore(event);\n    if (!shouldListen) {\n      shouldListen = true;\n      return;\n    }\n    handler(event);\n  };\n  const cleanup = [\n    useEventListener(window, \"click\", listener, { passive: true, capture }),\n    useEventListener(window, \"pointerdown\", (e) => {\n      const el = unrefElement(target);\n      if (el)\n        shouldListen = !e.composedPath().includes(el) && !shouldIgnore(e);\n    }, { passive: true }),\n    detectIframe && useEventListener(window, \"blur\", (event) => {\n      var _a;\n      const el = unrefElement(target);\n      if (((_a = window.document.activeElement) == null ? void 0 : _a.tagName) === \"IFRAME\" && !(el == null ? void 0 : el.contains(window.document.activeElement)))\n        handler(event);\n    })\n  ].filter(Boolean);\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nvar __defProp$n = Object.defineProperty;\nvar __defProps$9 = Object.defineProperties;\nvar __getOwnPropDescs$9 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$p = Object.getOwnPropertySymbols;\nvar __hasOwnProp$p = Object.prototype.hasOwnProperty;\nvar __propIsEnum$p = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$n = (obj, key, value) => key in obj ? __defProp$n(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$n = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$p.call(b, prop))\n      __defNormalProp$n(a, prop, b[prop]);\n  if (__getOwnPropSymbols$p)\n    for (var prop of __getOwnPropSymbols$p(b)) {\n      if (__propIsEnum$p.call(b, prop))\n        __defNormalProp$n(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$9 = (a, b) => __defProps$9(a, __getOwnPropDescs$9(b));\nconst createKeyPredicate = (keyFilter) => {\n  if (typeof keyFilter === \"function\")\n    return keyFilter;\n  else if (typeof keyFilter === \"string\")\n    return (event) => event.key === keyFilter;\n  else if (Array.isArray(keyFilter))\n    return (event) => keyFilter.includes(event.key);\n  return () => true;\n};\nfunction onKeyStroke(...args) {\n  let key;\n  let handler;\n  let options = {};\n  if (args.length === 3) {\n    key = args[0];\n    handler = args[1];\n    options = args[2];\n  } else if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      key = true;\n      handler = args[0];\n      options = args[1];\n    } else {\n      key = args[0];\n      handler = args[1];\n    }\n  } else {\n    key = true;\n    handler = args[0];\n  }\n  const { target = defaultWindow, eventName = \"keydown\", passive = false } = options;\n  const predicate = createKeyPredicate(key);\n  const listener = (e) => {\n    if (predicate(e))\n      handler(e);\n  };\n  return useEventListener(target, eventName, listener, passive);\n}\nfunction onKeyDown(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$9(__spreadValues$n({}, options), { eventName: \"keydown\" }));\n}\nfunction onKeyPressed(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$9(__spreadValues$n({}, options), { eventName: \"keypress\" }));\n}\nfunction onKeyUp(key, handler, options = {}) {\n  return onKeyStroke(key, handler, __spreadProps$9(__spreadValues$n({}, options), { eventName: \"keyup\" }));\n}\n\nconst DEFAULT_DELAY = 500;\nfunction onLongPress(target, handler, options) {\n  var _a, _b;\n  const elementRef = computed(() => unrefElement(target));\n  let timeout;\n  function clear() {\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = void 0;\n    }\n  }\n  function onDown(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    clear();\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    timeout = setTimeout(() => handler(ev), (_d = options == null ? void 0 : options.delay) != null ? _d : DEFAULT_DELAY);\n  }\n  const listenerOptions = {\n    capture: (_a = options == null ? void 0 : options.modifiers) == null ? void 0 : _a.capture,\n    once: (_b = options == null ? void 0 : options.modifiers) == null ? void 0 : _b.once\n  };\n  useEventListener(elementRef, \"pointerdown\", onDown, listenerOptions);\n  useEventListener(elementRef, \"pointerup\", clear, listenerOptions);\n  useEventListener(elementRef, \"pointerleave\", clear, listenerOptions);\n}\n\nconst isFocusedElementEditable = () => {\n  const { activeElement, body } = document;\n  if (!activeElement)\n    return false;\n  if (activeElement === body)\n    return false;\n  switch (activeElement.tagName) {\n    case \"INPUT\":\n    case \"TEXTAREA\":\n      return true;\n  }\n  return activeElement.hasAttribute(\"contenteditable\");\n};\nconst isTypedCharValid = ({\n  keyCode,\n  metaKey,\n  ctrlKey,\n  altKey\n}) => {\n  if (metaKey || ctrlKey || altKey)\n    return false;\n  if (keyCode >= 48 && keyCode <= 57 || keyCode >= 96 && keyCode <= 105)\n    return true;\n  if (keyCode >= 65 && keyCode <= 90)\n    return true;\n  return false;\n};\nfunction onStartTyping(callback, options = {}) {\n  const { document: document2 = defaultDocument } = options;\n  const keydown = (event) => {\n    !isFocusedElementEditable() && isTypedCharValid(event) && callback(event);\n  };\n  if (document2)\n    useEventListener(document2, \"keydown\", keydown, { passive: true });\n}\n\nfunction templateRef(key, initialValue = null) {\n  const instance = getCurrentInstance();\n  let _trigger = () => {\n  };\n  const element = customRef((track, trigger) => {\n    _trigger = trigger;\n    return {\n      get() {\n        var _a, _b;\n        track();\n        return (_b = (_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$refs[key]) != null ? _b : initialValue;\n      },\n      set() {\n      }\n    };\n  });\n  tryOnMounted(_trigger);\n  onUpdated(_trigger);\n  return element;\n}\n\nfunction useActiveElement(options = {}) {\n  var _a;\n  const { window = defaultWindow } = options;\n  const document = (_a = options.document) != null ? _a : window == null ? void 0 : window.document;\n  const activeElement = computedWithControl(() => null, () => document == null ? void 0 : document.activeElement);\n  if (window) {\n    useEventListener(window, \"blur\", (event) => {\n      if (event.relatedTarget !== null)\n        return;\n      activeElement.trigger();\n    }, true);\n    useEventListener(window, \"focus\", activeElement.trigger, true);\n  }\n  return activeElement;\n}\n\nfunction useAsyncQueue(tasks, options = {}) {\n  const {\n    interrupt = true,\n    onError = noop,\n    onFinished = noop\n  } = options;\n  const promiseState = {\n    pending: \"pending\",\n    rejected: \"rejected\",\n    fulfilled: \"fulfilled\"\n  };\n  const initialResult = Array.from(new Array(tasks.length), () => ({ state: promiseState.pending, data: null }));\n  const result = reactive(initialResult);\n  const activeIndex = ref(-1);\n  if (!tasks || tasks.length === 0) {\n    onFinished();\n    return {\n      activeIndex,\n      result\n    };\n  }\n  function updateResult(state, res) {\n    activeIndex.value++;\n    result[activeIndex.value].data = res;\n    result[activeIndex.value].state = state;\n  }\n  tasks.reduce((prev, curr) => {\n    return prev.then((prevRes) => {\n      var _a;\n      if (((_a = result[activeIndex.value]) == null ? void 0 : _a.state) === promiseState.rejected && interrupt) {\n        onFinished();\n        return;\n      }\n      return curr(prevRes).then((currentRes) => {\n        updateResult(promiseState.fulfilled, currentRes);\n        activeIndex.value === tasks.length - 1 && onFinished();\n        return currentRes;\n      });\n    }).catch((e) => {\n      updateResult(promiseState.rejected, e);\n      onError();\n      return e;\n    });\n  }, Promise.resolve());\n  return {\n    activeIndex,\n    result\n  };\n}\n\nfunction useAsyncState(promise, initialState, options) {\n  const {\n    immediate = true,\n    delay = 0,\n    onError = noop,\n    onSuccess = noop,\n    resetOnExecute = true,\n    shallow = true,\n    throwError\n  } = options != null ? options : {};\n  const state = shallow ? shallowRef(initialState) : ref(initialState);\n  const isReady = ref(false);\n  const isLoading = ref(false);\n  const error = ref(void 0);\n  async function execute(delay2 = 0, ...args) {\n    if (resetOnExecute)\n      state.value = initialState;\n    error.value = void 0;\n    isReady.value = false;\n    isLoading.value = true;\n    if (delay2 > 0)\n      await promiseTimeout(delay2);\n    const _promise = typeof promise === \"function\" ? promise(...args) : promise;\n    try {\n      const data = await _promise;\n      state.value = data;\n      isReady.value = true;\n      onSuccess(data);\n    } catch (e) {\n      error.value = e;\n      onError(e);\n      if (throwError)\n        throw error;\n    } finally {\n      isLoading.value = false;\n    }\n    return state.value;\n  }\n  if (immediate)\n    execute(delay);\n  return {\n    state,\n    isReady,\n    isLoading,\n    error,\n    execute\n  };\n}\n\nconst defaults = {\n  array: (v) => JSON.stringify(v),\n  object: (v) => JSON.stringify(v),\n  set: (v) => JSON.stringify(Array.from(v)),\n  map: (v) => JSON.stringify(Object.fromEntries(v)),\n  null: () => \"\"\n};\nfunction getDefaultSerialization(target) {\n  if (!target)\n    return defaults.null;\n  if (target instanceof Map)\n    return defaults.map;\n  else if (target instanceof Set)\n    return defaults.set;\n  else if (Array.isArray(target))\n    return defaults.array;\n  else\n    return defaults.object;\n}\n\nfunction useBase64(target, options) {\n  const base64 = ref(\"\");\n  const promise = ref();\n  function execute() {\n    if (!isClient)\n      return;\n    promise.value = new Promise((resolve, reject) => {\n      try {\n        const _target = resolveUnref(target);\n        if (_target == null) {\n          resolve(\"\");\n        } else if (typeof _target === \"string\") {\n          resolve(blobToBase64(new Blob([_target], { type: \"text/plain\" })));\n        } else if (_target instanceof Blob) {\n          resolve(blobToBase64(_target));\n        } else if (_target instanceof ArrayBuffer) {\n          resolve(window.btoa(String.fromCharCode(...new Uint8Array(_target))));\n        } else if (_target instanceof HTMLCanvasElement) {\n          resolve(_target.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n        } else if (_target instanceof HTMLImageElement) {\n          const img = _target.cloneNode(false);\n          img.crossOrigin = \"Anonymous\";\n          imgLoaded(img).then(() => {\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            canvas.width = img.width;\n            canvas.height = img.height;\n            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            resolve(canvas.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n          }).catch(reject);\n        } else if (typeof _target === \"object\") {\n          const _serializeFn = (options == null ? void 0 : options.serializer) || getDefaultSerialization(_target);\n          const serialized = _serializeFn(_target);\n          return resolve(blobToBase64(new Blob([serialized], { type: \"application/json\" })));\n        } else {\n          reject(new Error(\"target is unsupported types\"));\n        }\n      } catch (error) {\n        reject(error);\n      }\n    });\n    promise.value.then((res) => base64.value = res);\n    return promise.value;\n  }\n  if (isRef(target) || isFunction(target))\n    watch(target, execute, { immediate: true });\n  else\n    execute();\n  return {\n    base64,\n    promise,\n    execute\n  };\n}\nfunction imgLoaded(img) {\n  return new Promise((resolve, reject) => {\n    if (!img.complete) {\n      img.onload = () => {\n        resolve();\n      };\n      img.onerror = reject;\n    } else {\n      resolve();\n    }\n  });\n}\nfunction blobToBase64(blob) {\n  return new Promise((resolve, reject) => {\n    const fr = new FileReader();\n    fr.onload = (e) => {\n      resolve(e.target.result);\n    };\n    fr.onerror = reject;\n    fr.readAsDataURL(blob);\n  });\n}\n\nfunction useSupported(callback, sync = false) {\n  const isSupported = ref();\n  const update = () => isSupported.value = Boolean(callback());\n  update();\n  tryOnMounted(update, sync);\n  return isSupported;\n}\n\nfunction useBattery({ navigator = defaultNavigator } = {}) {\n  const events = [\"chargingchange\", \"chargingtimechange\", \"dischargingtimechange\", \"levelchange\"];\n  const isSupported = useSupported(() => navigator && \"getBattery\" in navigator);\n  const charging = ref(false);\n  const chargingTime = ref(0);\n  const dischargingTime = ref(0);\n  const level = ref(1);\n  let battery;\n  function updateBatteryInfo() {\n    charging.value = this.charging;\n    chargingTime.value = this.chargingTime || 0;\n    dischargingTime.value = this.dischargingTime || 0;\n    level.value = this.level;\n  }\n  if (isSupported.value) {\n    navigator.getBattery().then((_battery) => {\n      battery = _battery;\n      updateBatteryInfo.call(battery);\n      for (const event of events)\n        useEventListener(battery, event, updateBatteryInfo, { passive: true });\n    });\n  }\n  return {\n    isSupported,\n    charging,\n    chargingTime,\n    dischargingTime,\n    level\n  };\n}\n\nfunction useBluetooth(options) {\n  let {\n    acceptAllDevices = false\n  } = options || {};\n  const {\n    filters = void 0,\n    optionalServices = void 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => navigator && \"bluetooth\" in navigator);\n  const device = shallowRef(void 0);\n  const error = shallowRef(null);\n  watch(device, () => {\n    connectToBluetoothGATTServer();\n  });\n  async function requestDevice() {\n    if (!isSupported.value)\n      return;\n    error.value = null;\n    if (filters && filters.length > 0)\n      acceptAllDevices = false;\n    try {\n      device.value = await (navigator == null ? void 0 : navigator.bluetooth.requestDevice({\n        acceptAllDevices,\n        filters,\n        optionalServices\n      }));\n    } catch (err) {\n      error.value = err;\n    }\n  }\n  const server = ref();\n  const isConnected = computed(() => {\n    var _a;\n    return ((_a = server.value) == null ? void 0 : _a.connected) || false;\n  });\n  async function connectToBluetoothGATTServer() {\n    error.value = null;\n    if (device.value && device.value.gatt) {\n      device.value.addEventListener(\"gattserverdisconnected\", () => {\n      });\n      try {\n        server.value = await device.value.gatt.connect();\n      } catch (err) {\n        error.value = err;\n      }\n    }\n  }\n  tryOnMounted(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.connect();\n  });\n  tryOnScopeDispose(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.disconnect();\n  });\n  return {\n    isSupported,\n    isConnected,\n    device,\n    requestDevice,\n    server,\n    error\n  };\n}\n\nfunction useMediaQuery(query, options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"matchMedia\" in window && typeof window.matchMedia === \"function\");\n  let mediaQuery;\n  const matches = ref(false);\n  const cleanup = () => {\n    if (!mediaQuery)\n      return;\n    if (\"removeEventListener\" in mediaQuery)\n      mediaQuery.removeEventListener(\"change\", update);\n    else\n      mediaQuery.removeListener(update);\n  };\n  const update = () => {\n    if (!isSupported.value)\n      return;\n    cleanup();\n    mediaQuery = window.matchMedia(resolveRef(query).value);\n    matches.value = mediaQuery.matches;\n    if (\"addEventListener\" in mediaQuery)\n      mediaQuery.addEventListener(\"change\", update);\n    else\n      mediaQuery.addListener(update);\n  };\n  watchEffect(update);\n  tryOnScopeDispose(() => cleanup());\n  return matches;\n}\n\nconst breakpointsTailwind = {\n  \"sm\": 640,\n  \"md\": 768,\n  \"lg\": 1024,\n  \"xl\": 1280,\n  \"2xl\": 1536\n};\nconst breakpointsBootstrapV5 = {\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n};\nconst breakpointsVuetify = {\n  xs: 600,\n  sm: 960,\n  md: 1264,\n  lg: 1904\n};\nconst breakpointsAntDesign = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600\n};\nconst breakpointsQuasar = {\n  xs: 600,\n  sm: 1024,\n  md: 1440,\n  lg: 1920\n};\nconst breakpointsSematic = {\n  mobileS: 320,\n  mobileM: 375,\n  mobileL: 425,\n  tablet: 768,\n  laptop: 1024,\n  laptopL: 1440,\n  desktop4K: 2560\n};\nconst breakpointsMasterCss = {\n  \"3xs\": 360,\n  \"2xs\": 480,\n  \"xs\": 600,\n  \"sm\": 768,\n  \"md\": 1024,\n  \"lg\": 1280,\n  \"xl\": 1440,\n  \"2xl\": 1600,\n  \"3xl\": 1920,\n  \"4xl\": 2560\n};\n\nvar __defProp$m = Object.defineProperty;\nvar __getOwnPropSymbols$o = Object.getOwnPropertySymbols;\nvar __hasOwnProp$o = Object.prototype.hasOwnProperty;\nvar __propIsEnum$o = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$m = (obj, key, value) => key in obj ? __defProp$m(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$m = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$o.call(b, prop))\n      __defNormalProp$m(a, prop, b[prop]);\n  if (__getOwnPropSymbols$o)\n    for (var prop of __getOwnPropSymbols$o(b)) {\n      if (__propIsEnum$o.call(b, prop))\n        __defNormalProp$m(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useBreakpoints(breakpoints, options = {}) {\n  function getValue(k, delta) {\n    let v = breakpoints[k];\n    if (delta != null)\n      v = increaseWithUnit(v, delta);\n    if (typeof v === \"number\")\n      v = `${v}px`;\n    return v;\n  }\n  const { window = defaultWindow } = options;\n  function match(query) {\n    if (!window)\n      return false;\n    return window.matchMedia(query).matches;\n  }\n  const greaterOrEqual = (k) => {\n    return useMediaQuery(`(min-width: ${getValue(k)})`, options);\n  };\n  const shortcutMethods = Object.keys(breakpoints).reduce((shortcuts, k) => {\n    Object.defineProperty(shortcuts, k, {\n      get: () => greaterOrEqual(k),\n      enumerable: true,\n      configurable: true\n    });\n    return shortcuts;\n  }, {});\n  return __spreadValues$m({\n    greater(k) {\n      return useMediaQuery(`(min-width: ${getValue(k, 0.1)})`, options);\n    },\n    greaterOrEqual,\n    smaller(k) {\n      return useMediaQuery(`(max-width: ${getValue(k, -0.1)})`, options);\n    },\n    smallerOrEqual(k) {\n      return useMediaQuery(`(max-width: ${getValue(k)})`, options);\n    },\n    between(a, b) {\n      return useMediaQuery(`(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`, options);\n    },\n    isGreater(k) {\n      return match(`(min-width: ${getValue(k, 0.1)})`);\n    },\n    isGreaterOrEqual(k) {\n      return match(`(min-width: ${getValue(k)})`);\n    },\n    isSmaller(k) {\n      return match(`(max-width: ${getValue(k, -0.1)})`);\n    },\n    isSmallerOrEqual(k) {\n      return match(`(max-width: ${getValue(k)})`);\n    },\n    isInBetween(a, b) {\n      return match(`(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`);\n    }\n  }, shortcutMethods);\n}\n\nconst useBroadcastChannel = (options) => {\n  const {\n    name,\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"BroadcastChannel\" in window);\n  const isClosed = ref(false);\n  const channel = ref();\n  const data = ref();\n  const error = ref(null);\n  const post = (data2) => {\n    if (channel.value)\n      channel.value.postMessage(data2);\n  };\n  const close = () => {\n    if (channel.value)\n      channel.value.close();\n    isClosed.value = true;\n  };\n  if (isSupported.value) {\n    tryOnMounted(() => {\n      error.value = null;\n      channel.value = new BroadcastChannel(name);\n      channel.value.addEventListener(\"message\", (e) => {\n        data.value = e.data;\n      }, { passive: true });\n      channel.value.addEventListener(\"messageerror\", (e) => {\n        error.value = e;\n      }, { passive: true });\n      channel.value.addEventListener(\"close\", () => {\n        isClosed.value = true;\n      });\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    isSupported,\n    channel,\n    data,\n    post,\n    close,\n    error,\n    isClosed\n  };\n};\n\nfunction useBrowserLocation({ window = defaultWindow } = {}) {\n  const buildState = (trigger) => {\n    const { state: state2, length } = (window == null ? void 0 : window.history) || {};\n    const { hash, host, hostname, href, origin, pathname, port, protocol, search } = (window == null ? void 0 : window.location) || {};\n    return {\n      trigger,\n      state: state2,\n      length,\n      hash,\n      host,\n      hostname,\n      href,\n      origin,\n      pathname,\n      port,\n      protocol,\n      search\n    };\n  };\n  const state = ref(buildState(\"load\"));\n  if (window) {\n    useEventListener(window, \"popstate\", () => state.value = buildState(\"popstate\"), { passive: true });\n    useEventListener(window, \"hashchange\", () => state.value = buildState(\"hashchange\"), { passive: true });\n  }\n  return state;\n}\n\nfunction useCached(refValue, comparator = (a, b) => a === b, watchOptions) {\n  const cachedValue = ref(refValue.value);\n  watch(() => refValue.value, (value) => {\n    if (!comparator(value, cachedValue.value))\n      cachedValue.value = value;\n  }, watchOptions);\n  return cachedValue;\n}\n\nfunction useClipboard(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500,\n    legacy = false\n  } = options;\n  const events = [\"copy\", \"cut\"];\n  const isClipboardApiSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const isSupported = computed(() => isClipboardApiSupported.value || legacy);\n  const text = ref(\"\");\n  const copied = ref(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring);\n  function updateText() {\n    if (isClipboardApiSupported.value) {\n      navigator.clipboard.readText().then((value) => {\n        text.value = value;\n      });\n    } else {\n      text.value = legacyRead();\n    }\n  }\n  if (isSupported.value && read) {\n    for (const event of events)\n      useEventListener(event, updateText);\n  }\n  async function copy(value = resolveUnref(source)) {\n    if (isSupported.value && value != null) {\n      if (isClipboardApiSupported.value)\n        await navigator.clipboard.writeText(value);\n      else\n        legacyCopy(value);\n      text.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  function legacyCopy(value) {\n    const ta = document.createElement(\"textarea\");\n    ta.value = value != null ? value : \"\";\n    ta.style.position = \"absolute\";\n    ta.style.opacity = \"0\";\n    document.body.appendChild(ta);\n    ta.select();\n    document.execCommand(\"copy\");\n    ta.remove();\n  }\n  function legacyRead() {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = document == null ? void 0 : document.getSelection) == null ? void 0 : _a.call(document)) == null ? void 0 : _b.toString()) != null ? _c : \"\";\n  }\n  return {\n    isSupported,\n    text,\n    copied,\n    copy\n  };\n}\n\nvar __defProp$l = Object.defineProperty;\nvar __defProps$8 = Object.defineProperties;\nvar __getOwnPropDescs$8 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$n = Object.getOwnPropertySymbols;\nvar __hasOwnProp$n = Object.prototype.hasOwnProperty;\nvar __propIsEnum$n = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$l = (obj, key, value) => key in obj ? __defProp$l(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$l = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$n.call(b, prop))\n      __defNormalProp$l(a, prop, b[prop]);\n  if (__getOwnPropSymbols$n)\n    for (var prop of __getOwnPropSymbols$n(b)) {\n      if (__propIsEnum$n.call(b, prop))\n        __defNormalProp$l(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$8 = (a, b) => __defProps$8(a, __getOwnPropDescs$8(b));\nfunction cloneFnJSON(source) {\n  return JSON.parse(JSON.stringify(source));\n}\nfunction useCloned(source, options = {}) {\n  const cloned = ref({});\n  const {\n    manual,\n    clone = cloneFnJSON,\n    deep = true,\n    immediate = true\n  } = options;\n  function sync() {\n    cloned.value = clone(unref(source));\n  }\n  if (!manual && isRef(source)) {\n    watch(source, sync, __spreadProps$8(__spreadValues$l({}, options), {\n      deep,\n      immediate\n    }));\n  } else {\n    sync();\n  }\n  return { cloned, sync };\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\n_global[globalKey] = _global[globalKey] || {};\nconst handlers = _global[globalKey];\nfunction getSSRHandler(key, fallback) {\n  return handlers[key] || fallback;\n}\nfunction setSSRHandler(key, fn) {\n  handlers[key] = fn;\n}\n\nfunction guessSerializerType(rawInit) {\n  return rawInit == null ? \"any\" : rawInit instanceof Set ? \"set\" : rawInit instanceof Map ? \"map\" : rawInit instanceof Date ? \"date\" : typeof rawInit === \"boolean\" ? \"boolean\" : typeof rawInit === \"string\" ? \"string\" : typeof rawInit === \"object\" ? \"object\" : !Number.isNaN(rawInit) ? \"number\" : \"any\";\n}\n\nvar __defProp$k = Object.defineProperty;\nvar __getOwnPropSymbols$m = Object.getOwnPropertySymbols;\nvar __hasOwnProp$m = Object.prototype.hasOwnProperty;\nvar __propIsEnum$m = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$k = (obj, key, value) => key in obj ? __defProp$k(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$k = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$m.call(b, prop))\n      __defNormalProp$k(a, prop, b[prop]);\n  if (__getOwnPropSymbols$m)\n    for (var prop of __getOwnPropSymbols$m(b)) {\n      if (__propIsEnum$m.call(b, prop))\n        __defNormalProp$k(a, prop, b[prop]);\n    }\n  return a;\n};\nconst StorageSerializers = {\n  boolean: {\n    read: (v) => v === \"true\",\n    write: (v) => String(v)\n  },\n  object: {\n    read: (v) => JSON.parse(v),\n    write: (v) => JSON.stringify(v)\n  },\n  number: {\n    read: (v) => Number.parseFloat(v),\n    write: (v) => String(v)\n  },\n  any: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  string: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  map: {\n    read: (v) => new Map(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v.entries()))\n  },\n  set: {\n    read: (v) => new Set(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v))\n  },\n  date: {\n    read: (v) => new Date(v),\n    write: (v) => v.toISOString()\n  }\n};\nconst customStorageEventName = \"vueuse-storage\";\nfunction useStorage(key, defaults, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const data = (shallow ? shallowRef : ref)(defaults);\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  if (!storage)\n    return data;\n  const rawInit = resolveUnref(defaults);\n  const type = guessSerializerType(rawInit);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(data, () => write(data.value), { flush, deep, eventFilter });\n  if (window && listenToStorageChanges) {\n    useEventListener(window, \"storage\", update);\n    useEventListener(window, customStorageEventName, updateFromCustomEvent);\n  }\n  update();\n  return data;\n  function write(v) {\n    try {\n      if (v == null) {\n        storage.removeItem(key);\n      } else {\n        const serialized = serializer.write(v);\n        const oldValue = storage.getItem(key);\n        if (oldValue !== serialized) {\n          storage.setItem(key, serialized);\n          if (window) {\n            window.dispatchEvent(new CustomEvent(customStorageEventName, {\n              detail: {\n                key,\n                oldValue,\n                newValue: serialized,\n                storageArea: storage\n              }\n            }));\n          }\n        }\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  function read(event) {\n    const rawValue = event ? event.newValue : storage.getItem(key);\n    if (rawValue == null) {\n      if (writeDefaults && rawInit !== null)\n        storage.setItem(key, serializer.write(rawInit));\n      return rawInit;\n    } else if (!event && mergeDefaults) {\n      const value = serializer.read(rawValue);\n      if (isFunction(mergeDefaults))\n        return mergeDefaults(value, rawInit);\n      else if (type === \"object\" && !Array.isArray(value))\n        return __spreadValues$k(__spreadValues$k({}, rawInit), value);\n      return value;\n    } else if (typeof rawValue !== \"string\") {\n      return rawValue;\n    } else {\n      return serializer.read(rawValue);\n    }\n  }\n  function updateFromCustomEvent(event) {\n    update(event.detail);\n  }\n  function update(event) {\n    if (event && event.storageArea !== storage)\n      return;\n    if (event && event.key == null) {\n      data.value = rawInit;\n      return;\n    }\n    if (event && event.key !== key)\n      return;\n    pauseWatch();\n    try {\n      data.value = read(event);\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (event)\n        nextTick(resumeWatch);\n      else\n        resumeWatch();\n    }\n  }\n}\n\nfunction usePreferredDark(options) {\n  return useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n}\n\nvar __defProp$j = Object.defineProperty;\nvar __getOwnPropSymbols$l = Object.getOwnPropertySymbols;\nvar __hasOwnProp$l = Object.prototype.hasOwnProperty;\nvar __propIsEnum$l = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$j = (obj, key, value) => key in obj ? __defProp$j(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$j = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$l.call(b, prop))\n      __defNormalProp$j(a, prop, b[prop]);\n  if (__getOwnPropSymbols$l)\n    for (var prop of __getOwnPropSymbols$l(b)) {\n      if (__propIsEnum$l.call(b, prop))\n        __defNormalProp$j(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useColorMode(options = {}) {\n  const {\n    selector = \"html\",\n    attribute = \"class\",\n    initialValue = \"auto\",\n    window = defaultWindow,\n    storage,\n    storageKey = \"vueuse-color-scheme\",\n    listenToStorageChanges = true,\n    storageRef,\n    emitAuto\n  } = options;\n  const modes = __spreadValues$j({\n    auto: \"\",\n    light: \"light\",\n    dark: \"dark\"\n  }, options.modes || {});\n  const preferredDark = usePreferredDark({ window });\n  const preferredMode = computed(() => preferredDark.value ? \"dark\" : \"light\");\n  const store = storageRef || (storageKey == null ? ref(initialValue) : useStorage(storageKey, initialValue, storage, { window, listenToStorageChanges }));\n  const state = computed({\n    get() {\n      return store.value === \"auto\" && !emitAuto ? preferredMode.value : store.value;\n    },\n    set(v) {\n      store.value = v;\n    }\n  });\n  const updateHTMLAttrs = getSSRHandler(\"updateHTMLAttrs\", (selector2, attribute2, value) => {\n    const el = window == null ? void 0 : window.document.querySelector(selector2);\n    if (!el)\n      return;\n    if (attribute2 === \"class\") {\n      const current = value.split(/\\s/g);\n      Object.values(modes).flatMap((i) => (i || \"\").split(/\\s/g)).filter(Boolean).forEach((v) => {\n        if (current.includes(v))\n          el.classList.add(v);\n        else\n          el.classList.remove(v);\n      });\n    } else {\n      el.setAttribute(attribute2, value);\n    }\n  });\n  function defaultOnChanged(mode) {\n    var _a;\n    const resolvedMode = mode === \"auto\" ? preferredMode.value : mode;\n    updateHTMLAttrs(selector, attribute, (_a = modes[resolvedMode]) != null ? _a : resolvedMode);\n  }\n  function onChanged(mode) {\n    if (options.onChanged)\n      options.onChanged(mode, defaultOnChanged);\n    else\n      defaultOnChanged(mode);\n  }\n  watch(state, onChanged, { flush: \"post\", immediate: true });\n  if (emitAuto)\n    watch(preferredMode, () => onChanged(state.value), { flush: \"post\" });\n  tryOnMounted(() => onChanged(state.value));\n  return state;\n}\n\nfunction useConfirmDialog(revealed = ref(false)) {\n  const confirmHook = createEventHook();\n  const cancelHook = createEventHook();\n  const revealHook = createEventHook();\n  let _resolve = noop;\n  const reveal = (data) => {\n    revealHook.trigger(data);\n    revealed.value = true;\n    return new Promise((resolve) => {\n      _resolve = resolve;\n    });\n  };\n  const confirm = (data) => {\n    revealed.value = false;\n    confirmHook.trigger(data);\n    _resolve({ data, isCanceled: false });\n  };\n  const cancel = (data) => {\n    revealed.value = false;\n    cancelHook.trigger(data);\n    _resolve({ data, isCanceled: true });\n  };\n  return {\n    isRevealed: computed(() => revealed.value),\n    reveal,\n    confirm,\n    cancel,\n    onReveal: revealHook.on,\n    onConfirm: confirmHook.on,\n    onCancel: cancelHook.on\n  };\n}\n\nfunction useCssVar(prop, target, { window = defaultWindow, initialValue = \"\" } = {}) {\n  const variable = ref(initialValue);\n  const elRef = computed(() => {\n    var _a;\n    return unrefElement(target) || ((_a = window == null ? void 0 : window.document) == null ? void 0 : _a.documentElement);\n  });\n  watch([elRef, () => resolveUnref(prop)], ([el, prop2]) => {\n    var _a;\n    if (el && window) {\n      const value = (_a = window.getComputedStyle(el).getPropertyValue(prop2)) == null ? void 0 : _a.trim();\n      variable.value = value || initialValue;\n    }\n  }, { immediate: true });\n  watch(variable, (val) => {\n    var _a;\n    if ((_a = elRef.value) == null ? void 0 : _a.style)\n      elRef.value.style.setProperty(resolveUnref(prop), val);\n  });\n  return variable;\n}\n\nfunction useCurrentElement() {\n  const vm = getCurrentInstance();\n  const currentElement = computedWithControl(() => null, () => vm.proxy.$el);\n  onUpdated(currentElement.trigger);\n  onMounted(currentElement.trigger);\n  return currentElement;\n}\n\nfunction useCycleList(list, options) {\n  var _a;\n  const state = shallowRef((_a = options == null ? void 0 : options.initialValue) != null ? _a : list[0]);\n  const index = computed({\n    get() {\n      var _a2;\n      let index2 = (options == null ? void 0 : options.getIndexOf) ? options.getIndexOf(state.value, list) : list.indexOf(state.value);\n      if (index2 < 0)\n        index2 = (_a2 = options == null ? void 0 : options.fallbackIndex) != null ? _a2 : 0;\n      return index2;\n    },\n    set(v) {\n      set(v);\n    }\n  });\n  function set(i) {\n    const length = list.length;\n    const index2 = (i % length + length) % length;\n    const value = list[index2];\n    state.value = value;\n    return value;\n  }\n  function shift(delta = 1) {\n    return set(index.value + delta);\n  }\n  function next(n = 1) {\n    return shift(n);\n  }\n  function prev(n = 1) {\n    return shift(-n);\n  }\n  return {\n    state,\n    index,\n    next,\n    prev\n  };\n}\n\nvar __defProp$i = Object.defineProperty;\nvar __defProps$7 = Object.defineProperties;\nvar __getOwnPropDescs$7 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$k = Object.getOwnPropertySymbols;\nvar __hasOwnProp$k = Object.prototype.hasOwnProperty;\nvar __propIsEnum$k = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$i = (obj, key, value) => key in obj ? __defProp$i(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$i = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$k.call(b, prop))\n      __defNormalProp$i(a, prop, b[prop]);\n  if (__getOwnPropSymbols$k)\n    for (var prop of __getOwnPropSymbols$k(b)) {\n      if (__propIsEnum$k.call(b, prop))\n        __defNormalProp$i(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$7 = (a, b) => __defProps$7(a, __getOwnPropDescs$7(b));\nfunction useDark(options = {}) {\n  const {\n    valueDark = \"dark\",\n    valueLight = \"\",\n    window = defaultWindow\n  } = options;\n  const mode = useColorMode(__spreadProps$7(__spreadValues$i({}, options), {\n    onChanged: (mode2, defaultHandler) => {\n      var _a;\n      if (options.onChanged)\n        (_a = options.onChanged) == null ? void 0 : _a.call(options, mode2 === \"dark\");\n      else\n        defaultHandler(mode2);\n    },\n    modes: {\n      dark: valueDark,\n      light: valueLight\n    }\n  }));\n  const preferredDark = usePreferredDark({ window });\n  const isDark = computed({\n    get() {\n      return mode.value === \"dark\";\n    },\n    set(v) {\n      if (v === preferredDark.value)\n        mode.value = \"auto\";\n      else\n        mode.value = v ? \"dark\" : \"light\";\n    }\n  });\n  return isDark;\n}\n\nconst fnBypass = (v) => v;\nconst fnSetSource = (source, value) => source.value = value;\nfunction defaultDump(clone) {\n  return clone ? isFunction(clone) ? clone : cloneFnJSON : fnBypass;\n}\nfunction defaultParse(clone) {\n  return clone ? isFunction(clone) ? clone : cloneFnJSON : fnBypass;\n}\nfunction useManualRefHistory(source, options = {}) {\n  const {\n    clone = false,\n    dump = defaultDump(clone),\n    parse = defaultParse(clone),\n    setSource = fnSetSource\n  } = options;\n  function _createHistoryRecord() {\n    return markRaw({\n      snapshot: dump(source.value),\n      timestamp: timestamp()\n    });\n  }\n  const last = ref(_createHistoryRecord());\n  const undoStack = ref([]);\n  const redoStack = ref([]);\n  const _setSource = (record) => {\n    setSource(source, parse(record.snapshot));\n    last.value = record;\n  };\n  const commit = () => {\n    undoStack.value.unshift(last.value);\n    last.value = _createHistoryRecord();\n    if (options.capacity && undoStack.value.length > options.capacity)\n      undoStack.value.splice(options.capacity, Infinity);\n    if (redoStack.value.length)\n      redoStack.value.splice(0, redoStack.value.length);\n  };\n  const clear = () => {\n    undoStack.value.splice(0, undoStack.value.length);\n    redoStack.value.splice(0, redoStack.value.length);\n  };\n  const undo = () => {\n    const state = undoStack.value.shift();\n    if (state) {\n      redoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const redo = () => {\n    const state = redoStack.value.shift();\n    if (state) {\n      undoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const reset = () => {\n    _setSource(last.value);\n  };\n  const history = computed(() => [last.value, ...undoStack.value]);\n  const canUndo = computed(() => undoStack.value.length > 0);\n  const canRedo = computed(() => redoStack.value.length > 0);\n  return {\n    source,\n    undoStack,\n    redoStack,\n    last,\n    history,\n    canUndo,\n    canRedo,\n    clear,\n    commit,\n    reset,\n    undo,\n    redo\n  };\n}\n\nvar __defProp$h = Object.defineProperty;\nvar __defProps$6 = Object.defineProperties;\nvar __getOwnPropDescs$6 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$j = Object.getOwnPropertySymbols;\nvar __hasOwnProp$j = Object.prototype.hasOwnProperty;\nvar __propIsEnum$j = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$h = (obj, key, value) => key in obj ? __defProp$h(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$h = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$j.call(b, prop))\n      __defNormalProp$h(a, prop, b[prop]);\n  if (__getOwnPropSymbols$j)\n    for (var prop of __getOwnPropSymbols$j(b)) {\n      if (__propIsEnum$j.call(b, prop))\n        __defNormalProp$h(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$6 = (a, b) => __defProps$6(a, __getOwnPropDescs$6(b));\nfunction useRefHistory(source, options = {}) {\n  const {\n    deep = false,\n    flush = \"pre\",\n    eventFilter\n  } = options;\n  const {\n    eventFilter: composedFilter,\n    pause,\n    resume: resumeTracking,\n    isActive: isTracking\n  } = pausableFilter(eventFilter);\n  const {\n    ignoreUpdates,\n    ignorePrevAsyncUpdates,\n    stop\n  } = watchIgnorable(source, commit, { deep, flush, eventFilter: composedFilter });\n  function setSource(source2, value) {\n    ignorePrevAsyncUpdates();\n    ignoreUpdates(() => {\n      source2.value = value;\n    });\n  }\n  const manualHistory = useManualRefHistory(source, __spreadProps$6(__spreadValues$h({}, options), { clone: options.clone || deep, setSource }));\n  const { clear, commit: manualCommit } = manualHistory;\n  function commit() {\n    ignorePrevAsyncUpdates();\n    manualCommit();\n  }\n  function resume(commitNow) {\n    resumeTracking();\n    if (commitNow)\n      commit();\n  }\n  function batch(fn) {\n    let canceled = false;\n    const cancel = () => canceled = true;\n    ignoreUpdates(() => {\n      fn(cancel);\n    });\n    if (!canceled)\n      commit();\n  }\n  function dispose() {\n    stop();\n    clear();\n  }\n  return __spreadProps$6(__spreadValues$h({}, manualHistory), {\n    isTracking,\n    pause,\n    resume,\n    commit,\n    batch,\n    dispose\n  });\n}\n\nvar __defProp$g = Object.defineProperty;\nvar __defProps$5 = Object.defineProperties;\nvar __getOwnPropDescs$5 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$i = Object.getOwnPropertySymbols;\nvar __hasOwnProp$i = Object.prototype.hasOwnProperty;\nvar __propIsEnum$i = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$g = (obj, key, value) => key in obj ? __defProp$g(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$g = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$i.call(b, prop))\n      __defNormalProp$g(a, prop, b[prop]);\n  if (__getOwnPropSymbols$i)\n    for (var prop of __getOwnPropSymbols$i(b)) {\n      if (__propIsEnum$i.call(b, prop))\n        __defNormalProp$g(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$5 = (a, b) => __defProps$5(a, __getOwnPropDescs$5(b));\nfunction useDebouncedRefHistory(source, options = {}) {\n  const filter = options.debounce ? debounceFilter(options.debounce) : void 0;\n  const history = useRefHistory(source, __spreadProps$5(__spreadValues$g({}, options), { eventFilter: filter }));\n  return __spreadValues$g({}, history);\n}\n\nfunction useDeviceMotion(options = {}) {\n  const {\n    window = defaultWindow,\n    eventFilter = bypassFilter\n  } = options;\n  const acceleration = ref({ x: null, y: null, z: null });\n  const rotationRate = ref({ alpha: null, beta: null, gamma: null });\n  const interval = ref(0);\n  const accelerationIncludingGravity = ref({\n    x: null,\n    y: null,\n    z: null\n  });\n  if (window) {\n    const onDeviceMotion = createFilterWrapper(eventFilter, (event) => {\n      acceleration.value = event.acceleration;\n      accelerationIncludingGravity.value = event.accelerationIncludingGravity;\n      rotationRate.value = event.rotationRate;\n      interval.value = event.interval;\n    });\n    useEventListener(window, \"devicemotion\", onDeviceMotion);\n  }\n  return {\n    acceleration,\n    accelerationIncludingGravity,\n    rotationRate,\n    interval\n  };\n}\n\nfunction useDeviceOrientation(options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"DeviceOrientationEvent\" in window);\n  const isAbsolute = ref(false);\n  const alpha = ref(null);\n  const beta = ref(null);\n  const gamma = ref(null);\n  if (window && isSupported.value) {\n    useEventListener(window, \"deviceorientation\", (event) => {\n      isAbsolute.value = event.absolute;\n      alpha.value = event.alpha;\n      beta.value = event.beta;\n      gamma.value = event.gamma;\n    });\n  }\n  return {\n    isSupported,\n    isAbsolute,\n    alpha,\n    beta,\n    gamma\n  };\n}\n\nfunction useDevicePixelRatio({\n  window = defaultWindow\n} = {}) {\n  const pixelRatio = ref(1);\n  if (window) {\n    let observe = function() {\n      pixelRatio.value = window.devicePixelRatio;\n      cleanup();\n      media = window.matchMedia(`(resolution: ${pixelRatio.value}dppx)`);\n      media.addEventListener(\"change\", observe, { once: true });\n    }, cleanup = function() {\n      media == null ? void 0 : media.removeEventListener(\"change\", observe);\n    };\n    let media;\n    observe();\n    tryOnScopeDispose(cleanup);\n  }\n  return { pixelRatio };\n}\n\nfunction usePermission(permissionDesc, options = {}) {\n  const {\n    controls = false,\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"permissions\" in navigator);\n  let permissionStatus;\n  const desc = typeof permissionDesc === \"string\" ? { name: permissionDesc } : permissionDesc;\n  const state = ref();\n  const onChange = () => {\n    if (permissionStatus)\n      state.value = permissionStatus.state;\n  };\n  const query = createSingletonPromise(async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionStatus) {\n      try {\n        permissionStatus = await navigator.permissions.query(desc);\n        useEventListener(permissionStatus, \"change\", onChange);\n        onChange();\n      } catch (e) {\n        state.value = \"prompt\";\n      }\n    }\n    return permissionStatus;\n  });\n  query();\n  if (controls) {\n    return {\n      state,\n      isSupported,\n      query\n    };\n  } else {\n    return state;\n  }\n}\n\nfunction useDevicesList(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    requestPermissions = false,\n    constraints = { audio: true, video: true },\n    onUpdated\n  } = options;\n  const devices = ref([]);\n  const videoInputs = computed(() => devices.value.filter((i) => i.kind === \"videoinput\"));\n  const audioInputs = computed(() => devices.value.filter((i) => i.kind === \"audioinput\"));\n  const audioOutputs = computed(() => devices.value.filter((i) => i.kind === \"audiooutput\"));\n  const isSupported = useSupported(() => navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);\n  const permissionGranted = ref(false);\n  async function update() {\n    if (!isSupported.value)\n      return;\n    devices.value = await navigator.mediaDevices.enumerateDevices();\n    onUpdated == null ? void 0 : onUpdated(devices.value);\n  }\n  async function ensurePermissions() {\n    if (!isSupported.value)\n      return false;\n    if (permissionGranted.value)\n      return true;\n    const { state, query } = usePermission(\"camera\", { controls: true });\n    await query();\n    if (state.value !== \"granted\") {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      stream.getTracks().forEach((t) => t.stop());\n      update();\n      permissionGranted.value = true;\n    } else {\n      permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  }\n  if (isSupported.value) {\n    if (requestPermissions)\n      ensurePermissions();\n    useEventListener(navigator.mediaDevices, \"devicechange\", update);\n    update();\n  }\n  return {\n    devices,\n    ensurePermissions,\n    permissionGranted,\n    videoInputs,\n    audioInputs,\n    audioOutputs,\n    isSupported\n  };\n}\n\nfunction useDisplayMedia(options = {}) {\n  var _a;\n  const enabled = ref((_a = options.enabled) != null ? _a : false);\n  const video = options.video;\n  const audio = options.audio;\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getDisplayMedia;\n  });\n  const constraint = { audio, video };\n  const stream = shallowRef();\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getDisplayMedia(constraint);\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  watch(enabled, (v) => {\n    if (v)\n      _start();\n    else\n      _stop();\n  }, { immediate: true });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    enabled\n  };\n}\n\nfunction useDocumentVisibility({ document = defaultDocument } = {}) {\n  if (!document)\n    return ref(\"visible\");\n  const visibility = ref(document.visibilityState);\n  useEventListener(document, \"visibilitychange\", () => {\n    visibility.value = document.visibilityState;\n  });\n  return visibility;\n}\n\nvar __defProp$f = Object.defineProperty;\nvar __defProps$4 = Object.defineProperties;\nvar __getOwnPropDescs$4 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$h = Object.getOwnPropertySymbols;\nvar __hasOwnProp$h = Object.prototype.hasOwnProperty;\nvar __propIsEnum$h = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$f = (obj, key, value) => key in obj ? __defProp$f(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$f = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$h.call(b, prop))\n      __defNormalProp$f(a, prop, b[prop]);\n  if (__getOwnPropSymbols$h)\n    for (var prop of __getOwnPropSymbols$h(b)) {\n      if (__propIsEnum$h.call(b, prop))\n        __defNormalProp$f(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$4 = (a, b) => __defProps$4(a, __getOwnPropDescs$4(b));\nfunction useDraggable(target, options = {}) {\n  var _a, _b, _c;\n  const draggingElement = (_a = options.draggingElement) != null ? _a : defaultWindow;\n  const draggingHandle = (_b = options.handle) != null ? _b : target;\n  const position = ref((_c = resolveUnref(options.initialValue)) != null ? _c : { x: 0, y: 0 });\n  const pressedDelta = ref();\n  const filterEvent = (e) => {\n    if (options.pointerTypes)\n      return options.pointerTypes.includes(e.pointerType);\n    return true;\n  };\n  const handleEvent = (e) => {\n    if (resolveUnref(options.preventDefault))\n      e.preventDefault();\n    if (resolveUnref(options.stopPropagation))\n      e.stopPropagation();\n  };\n  const start = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (resolveUnref(options.exact) && e.target !== resolveUnref(target))\n      return;\n    const rect = resolveUnref(target).getBoundingClientRect();\n    const pos = {\n      x: e.clientX - rect.left,\n      y: e.clientY - rect.top\n    };\n    if (((_a2 = options.onStart) == null ? void 0 : _a2.call(options, pos, e)) === false)\n      return;\n    pressedDelta.value = pos;\n    handleEvent(e);\n  };\n  const move = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    position.value = {\n      x: e.clientX - pressedDelta.value.x,\n      y: e.clientY - pressedDelta.value.y\n    };\n    (_a2 = options.onMove) == null ? void 0 : _a2.call(options, position.value, e);\n    handleEvent(e);\n  };\n  const end = (e) => {\n    var _a2;\n    if (!filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    pressedDelta.value = void 0;\n    (_a2 = options.onEnd) == null ? void 0 : _a2.call(options, position.value, e);\n    handleEvent(e);\n  };\n  if (isClient) {\n    useEventListener(draggingHandle, \"pointerdown\", start, true);\n    useEventListener(draggingElement, \"pointermove\", move, true);\n    useEventListener(draggingElement, \"pointerup\", end, true);\n  }\n  return __spreadProps$4(__spreadValues$f({}, toRefs(position)), {\n    position,\n    isDragging: computed(() => !!pressedDelta.value),\n    style: computed(() => `left:${position.value.x}px;top:${position.value.y}px;`)\n  });\n}\n\nfunction useDropZone(target, onDrop) {\n  const isOverDropZone = ref(false);\n  let counter = 0;\n  if (isClient) {\n    useEventListener(target, \"dragenter\", (event) => {\n      event.preventDefault();\n      counter += 1;\n      isOverDropZone.value = true;\n    });\n    useEventListener(target, \"dragover\", (event) => {\n      event.preventDefault();\n    });\n    useEventListener(target, \"dragleave\", (event) => {\n      event.preventDefault();\n      counter -= 1;\n      if (counter === 0)\n        isOverDropZone.value = false;\n    });\n    useEventListener(target, \"drop\", (event) => {\n      var _a, _b;\n      event.preventDefault();\n      counter = 0;\n      isOverDropZone.value = false;\n      const files = Array.from((_b = (_a = event.dataTransfer) == null ? void 0 : _a.files) != null ? _b : []);\n      onDrop == null ? void 0 : onDrop(files.length === 0 ? null : files);\n    });\n  }\n  return {\n    isOverDropZone\n  };\n}\n\nvar __getOwnPropSymbols$g = Object.getOwnPropertySymbols;\nvar __hasOwnProp$g = Object.prototype.hasOwnProperty;\nvar __propIsEnum$g = Object.prototype.propertyIsEnumerable;\nvar __objRest$2 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$g.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$g)\n    for (var prop of __getOwnPropSymbols$g(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$g.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction useResizeObserver(target, callback, options = {}) {\n  const _a = options, { window = defaultWindow } = _a, observerOptions = __objRest$2(_a, [\"window\"]);\n  let observer;\n  const isSupported = useSupported(() => window && \"ResizeObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const stopWatch = watch(() => unrefElement(target), (el) => {\n    cleanup();\n    if (isSupported.value && window && el) {\n      observer = new ResizeObserver(callback);\n      observer.observe(el, observerOptions);\n    }\n  }, { immediate: true, flush: \"post\" });\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nfunction useElementBounding(target, options = {}) {\n  const {\n    reset = true,\n    windowResize = true,\n    windowScroll = true,\n    immediate = true\n  } = options;\n  const height = ref(0);\n  const bottom = ref(0);\n  const left = ref(0);\n  const right = ref(0);\n  const top = ref(0);\n  const width = ref(0);\n  const x = ref(0);\n  const y = ref(0);\n  function update() {\n    const el = unrefElement(target);\n    if (!el) {\n      if (reset) {\n        height.value = 0;\n        bottom.value = 0;\n        left.value = 0;\n        right.value = 0;\n        top.value = 0;\n        width.value = 0;\n        x.value = 0;\n        y.value = 0;\n      }\n      return;\n    }\n    const rect = el.getBoundingClientRect();\n    height.value = rect.height;\n    bottom.value = rect.bottom;\n    left.value = rect.left;\n    right.value = rect.right;\n    top.value = rect.top;\n    width.value = rect.width;\n    x.value = rect.x;\n    y.value = rect.y;\n  }\n  useResizeObserver(target, update);\n  watch(() => unrefElement(target), (ele) => !ele && update());\n  if (windowScroll)\n    useEventListener(\"scroll\", update, { capture: true, passive: true });\n  if (windowResize)\n    useEventListener(\"resize\", update, { passive: true });\n  tryOnMounted(() => {\n    if (immediate)\n      update();\n  });\n  return {\n    height,\n    bottom,\n    left,\n    right,\n    top,\n    width,\n    x,\n    y,\n    update\n  };\n}\n\nfunction useRafFn(fn, options = {}) {\n  const {\n    immediate = true,\n    window = defaultWindow\n  } = options;\n  const isActive = ref(false);\n  let previousFrameTimestamp = 0;\n  let rafId = null;\n  function loop(timestamp) {\n    if (!isActive.value || !window)\n      return;\n    const delta = timestamp - previousFrameTimestamp;\n    fn({ delta, timestamp });\n    previousFrameTimestamp = timestamp;\n    rafId = window.requestAnimationFrame(loop);\n  }\n  function resume() {\n    if (!isActive.value && window) {\n      isActive.value = true;\n      rafId = window.requestAnimationFrame(loop);\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    if (rafId != null && window) {\n      window.cancelAnimationFrame(rafId);\n      rafId = null;\n    }\n  }\n  if (immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive: readonly(isActive),\n    pause,\n    resume\n  };\n}\n\nvar __defProp$e = Object.defineProperty;\nvar __getOwnPropSymbols$f = Object.getOwnPropertySymbols;\nvar __hasOwnProp$f = Object.prototype.hasOwnProperty;\nvar __propIsEnum$f = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$e = (obj, key, value) => key in obj ? __defProp$e(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$e = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$f.call(b, prop))\n      __defNormalProp$e(a, prop, b[prop]);\n  if (__getOwnPropSymbols$f)\n    for (var prop of __getOwnPropSymbols$f(b)) {\n      if (__propIsEnum$f.call(b, prop))\n        __defNormalProp$e(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useElementByPoint(options) {\n  const element = ref(null);\n  const { x, y, document = defaultDocument } = options;\n  const controls = useRafFn(() => {\n    element.value = (document == null ? void 0 : document.elementFromPoint(resolveUnref(x), resolveUnref(y))) || null;\n  });\n  return __spreadValues$e({\n    element\n  }, controls);\n}\n\nfunction useElementHover(el, options = {}) {\n  const delayEnter = options ? options.delayEnter : 0;\n  const delayLeave = options ? options.delayLeave : 0;\n  const isHovered = ref(false);\n  let timer;\n  const toggle = (entering) => {\n    const delay = entering ? delayEnter : delayLeave;\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n    if (delay)\n      timer = setTimeout(() => isHovered.value = entering, delay);\n    else\n      isHovered.value = entering;\n  };\n  if (!window)\n    return isHovered;\n  useEventListener(el, \"mouseenter\", () => toggle(true), { passive: true });\n  useEventListener(el, \"mouseleave\", () => toggle(false), { passive: true });\n  return isHovered;\n}\n\nfunction useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {\n  const { window = defaultWindow, box = \"content-box\" } = options;\n  const isSVG = computed(() => {\n    var _a, _b;\n    return (_b = (_a = unrefElement(target)) == null ? void 0 : _a.namespaceURI) == null ? void 0 : _b.includes(\"svg\");\n  });\n  const width = ref(initialSize.width);\n  const height = ref(initialSize.height);\n  useResizeObserver(target, ([entry]) => {\n    const boxSize = box === \"border-box\" ? entry.borderBoxSize : box === \"content-box\" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;\n    if (window && isSVG.value) {\n      const $elem = unrefElement(target);\n      if ($elem) {\n        const styles = window.getComputedStyle($elem);\n        width.value = parseFloat(styles.width);\n        height.value = parseFloat(styles.height);\n      }\n    } else {\n      if (boxSize) {\n        const formatBoxSize = Array.isArray(boxSize) ? boxSize : [boxSize];\n        width.value = formatBoxSize.reduce((acc, { inlineSize }) => acc + inlineSize, 0);\n        height.value = formatBoxSize.reduce((acc, { blockSize }) => acc + blockSize, 0);\n      } else {\n        width.value = entry.contentRect.width;\n        height.value = entry.contentRect.height;\n      }\n    }\n  }, options);\n  watch(() => unrefElement(target), (ele) => {\n    width.value = ele ? initialSize.width : 0;\n    height.value = ele ? initialSize.height : 0;\n  });\n  return {\n    width,\n    height\n  };\n}\n\nfunction useElementVisibility(element, { window = defaultWindow, scrollTarget } = {}) {\n  const elementIsVisible = ref(false);\n  const testBounding = () => {\n    if (!window)\n      return;\n    const document = window.document;\n    const el = unrefElement(element);\n    if (!el) {\n      elementIsVisible.value = false;\n    } else {\n      const rect = el.getBoundingClientRect();\n      elementIsVisible.value = rect.top <= (window.innerHeight || document.documentElement.clientHeight) && rect.left <= (window.innerWidth || document.documentElement.clientWidth) && rect.bottom >= 0 && rect.right >= 0;\n    }\n  };\n  watch(() => unrefElement(element), () => testBounding(), { immediate: true, flush: \"post\" });\n  if (window) {\n    useEventListener(scrollTarget || window, \"scroll\", testBounding, {\n      capture: false,\n      passive: true\n    });\n  }\n  return elementIsVisible;\n}\n\nconst events = new Map();\n\nfunction useEventBus(key) {\n  const scope = getCurrentScope();\n  function on(listener) {\n    var _a;\n    const listeners = events.get(key) || [];\n    listeners.push(listener);\n    events.set(key, listeners);\n    const _off = () => off(listener);\n    (_a = scope == null ? void 0 : scope.cleanups) == null ? void 0 : _a.push(_off);\n    return _off;\n  }\n  function once(listener) {\n    function _listener(...args) {\n      off(_listener);\n      listener(...args);\n    }\n    return on(_listener);\n  }\n  function off(listener) {\n    const listeners = events.get(key);\n    if (!listeners)\n      return;\n    const index = listeners.indexOf(listener);\n    if (index > -1)\n      listeners.splice(index, 1);\n    if (!listeners.length)\n      events.delete(key);\n  }\n  function reset() {\n    events.delete(key);\n  }\n  function emit(event, payload) {\n    var _a;\n    (_a = events.get(key)) == null ? void 0 : _a.forEach((v) => v(event, payload));\n  }\n  return { on, once, off, emit, reset };\n}\n\nfunction useEventSource(url, events = [], options = {}) {\n  const event = ref(null);\n  const data = ref(null);\n  const status = ref(\"CONNECTING\");\n  const eventSource = ref(null);\n  const error = ref(null);\n  const {\n    withCredentials = false\n  } = options;\n  const close = () => {\n    if (eventSource.value) {\n      eventSource.value.close();\n      eventSource.value = null;\n      status.value = \"CLOSED\";\n    }\n  };\n  const es = new EventSource(url, { withCredentials });\n  eventSource.value = es;\n  es.onopen = () => {\n    status.value = \"OPEN\";\n    error.value = null;\n  };\n  es.onerror = (e) => {\n    status.value = \"CLOSED\";\n    error.value = e;\n  };\n  es.onmessage = (e) => {\n    event.value = null;\n    data.value = e.data;\n  };\n  for (const event_name of events) {\n    useEventListener(es, event_name, (e) => {\n      event.value = event_name;\n      data.value = e.data || null;\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    eventSource,\n    event,\n    data,\n    status,\n    error,\n    close\n  };\n}\n\nfunction useEyeDropper(options = {}) {\n  const { initialValue = \"\" } = options;\n  const isSupported = useSupported(() => typeof window !== \"undefined\" && \"EyeDropper\" in window);\n  const sRGBHex = ref(initialValue);\n  async function open(openOptions) {\n    if (!isSupported.value)\n      return;\n    const eyeDropper = new window.EyeDropper();\n    const result = await eyeDropper.open(openOptions);\n    sRGBHex.value = result.sRGBHex;\n    return result;\n  }\n  return { isSupported, sRGBHex, open };\n}\n\nfunction useFavicon(newIcon = null, options = {}) {\n  const {\n    baseUrl = \"\",\n    rel = \"icon\",\n    document = defaultDocument\n  } = options;\n  const favicon = resolveRef(newIcon);\n  const applyIcon = (icon) => {\n    document == null ? void 0 : document.head.querySelectorAll(`link[rel*=\"${rel}\"]`).forEach((el) => el.href = `${baseUrl}${icon}`);\n  };\n  watch(favicon, (i, o) => {\n    if (isString(i) && i !== o)\n      applyIcon(i);\n  }, { immediate: true });\n  return favicon;\n}\n\nvar __defProp$d = Object.defineProperty;\nvar __defProps$3 = Object.defineProperties;\nvar __getOwnPropDescs$3 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$e = Object.getOwnPropertySymbols;\nvar __hasOwnProp$e = Object.prototype.hasOwnProperty;\nvar __propIsEnum$e = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$d = (obj, key, value) => key in obj ? __defProp$d(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$d = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$e.call(b, prop))\n      __defNormalProp$d(a, prop, b[prop]);\n  if (__getOwnPropSymbols$e)\n    for (var prop of __getOwnPropSymbols$e(b)) {\n      if (__propIsEnum$e.call(b, prop))\n        __defNormalProp$d(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$3 = (a, b) => __defProps$3(a, __getOwnPropDescs$3(b));\nconst payloadMapping = {\n  json: \"application/json\",\n  text: \"text/plain\"\n};\nfunction isFetchOptions(obj) {\n  return obj && containsProp(obj, \"immediate\", \"refetch\", \"initialData\", \"timeout\", \"beforeFetch\", \"afterFetch\", \"onFetchError\", \"fetch\");\n}\nfunction isAbsoluteURL(url) {\n  return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url);\n}\nfunction headersToObject(headers) {\n  if (typeof Headers !== \"undefined\" && headers instanceof Headers)\n    return Object.fromEntries([...headers.entries()]);\n  return headers;\n}\nfunction combineCallbacks(combination, ...callbacks) {\n  if (combination === \"overwrite\") {\n    return async (ctx) => {\n      const callback = callbacks[callbacks.length - 1];\n      if (callback !== void 0)\n        await callback(ctx);\n      return ctx;\n    };\n  } else {\n    return async (ctx) => {\n      await callbacks.reduce((prevCallback, callback) => prevCallback.then(async () => {\n        if (callback)\n          ctx = __spreadValues$d(__spreadValues$d({}, ctx), await callback(ctx));\n      }), Promise.resolve());\n      return ctx;\n    };\n  }\n}\nfunction createFetch(config = {}) {\n  const _combination = config.combination || \"chain\";\n  const _options = config.options || {};\n  const _fetchOptions = config.fetchOptions || {};\n  function useFactoryFetch(url, ...args) {\n    const computedUrl = computed(() => {\n      const baseUrl = resolveUnref(config.baseUrl);\n      const targetUrl = resolveUnref(url);\n      return baseUrl && !isAbsoluteURL(targetUrl) ? joinPaths(baseUrl, targetUrl) : targetUrl;\n    });\n    let options = _options;\n    let fetchOptions = _fetchOptions;\n    if (args.length > 0) {\n      if (isFetchOptions(args[0])) {\n        options = __spreadProps$3(__spreadValues$d(__spreadValues$d({}, options), args[0]), {\n          beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[0].beforeFetch),\n          afterFetch: combineCallbacks(_combination, _options.afterFetch, args[0].afterFetch),\n          onFetchError: combineCallbacks(_combination, _options.onFetchError, args[0].onFetchError)\n        });\n      } else {\n        fetchOptions = __spreadProps$3(__spreadValues$d(__spreadValues$d({}, fetchOptions), args[0]), {\n          headers: __spreadValues$d(__spreadValues$d({}, headersToObject(fetchOptions.headers) || {}), headersToObject(args[0].headers) || {})\n        });\n      }\n    }\n    if (args.length > 1 && isFetchOptions(args[1])) {\n      options = __spreadProps$3(__spreadValues$d(__spreadValues$d({}, options), args[1]), {\n        beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[1].beforeFetch),\n        afterFetch: combineCallbacks(_combination, _options.afterFetch, args[1].afterFetch),\n        onFetchError: combineCallbacks(_combination, _options.onFetchError, args[1].onFetchError)\n      });\n    }\n    return useFetch(computedUrl, fetchOptions, options);\n  }\n  return useFactoryFetch;\n}\nfunction useFetch(url, ...args) {\n  var _a;\n  const supportsAbort = typeof AbortController === \"function\";\n  let fetchOptions = {};\n  let options = { immediate: true, refetch: false, timeout: 0 };\n  const config = {\n    method: \"GET\",\n    type: \"text\",\n    payload: void 0\n  };\n  if (args.length > 0) {\n    if (isFetchOptions(args[0]))\n      options = __spreadValues$d(__spreadValues$d({}, options), args[0]);\n    else\n      fetchOptions = args[0];\n  }\n  if (args.length > 1) {\n    if (isFetchOptions(args[1]))\n      options = __spreadValues$d(__spreadValues$d({}, options), args[1]);\n  }\n  const {\n    fetch = (_a = defaultWindow) == null ? void 0 : _a.fetch,\n    initialData,\n    timeout\n  } = options;\n  const responseEvent = createEventHook();\n  const errorEvent = createEventHook();\n  const finallyEvent = createEventHook();\n  const isFinished = ref(false);\n  const isFetching = ref(false);\n  const aborted = ref(false);\n  const statusCode = ref(null);\n  const response = shallowRef(null);\n  const error = shallowRef(null);\n  const data = shallowRef(initialData);\n  const canAbort = computed(() => supportsAbort && isFetching.value);\n  let controller;\n  let timer;\n  const abort = () => {\n    if (supportsAbort && controller) {\n      controller.abort();\n      controller = void 0;\n    }\n  };\n  const loading = (isLoading) => {\n    isFetching.value = isLoading;\n    isFinished.value = !isLoading;\n  };\n  if (timeout)\n    timer = useTimeoutFn(abort, timeout, { immediate: false });\n  const execute = async (throwOnFailed = false) => {\n    var _a2;\n    loading(true);\n    error.value = null;\n    statusCode.value = null;\n    aborted.value = false;\n    if (supportsAbort) {\n      abort();\n      controller = new AbortController();\n      controller.signal.onabort = () => aborted.value = true;\n      fetchOptions = __spreadProps$3(__spreadValues$d({}, fetchOptions), {\n        signal: controller.signal\n      });\n    }\n    const defaultFetchOptions = {\n      method: config.method,\n      headers: {}\n    };\n    if (config.payload) {\n      const headers = headersToObject(defaultFetchOptions.headers);\n      if (config.payloadType)\n        headers[\"Content-Type\"] = (_a2 = payloadMapping[config.payloadType]) != null ? _a2 : config.payloadType;\n      const payload = resolveUnref(config.payload);\n      defaultFetchOptions.body = config.payloadType === \"json\" ? JSON.stringify(payload) : payload;\n    }\n    let isCanceled = false;\n    const context = {\n      url: resolveUnref(url),\n      options: __spreadValues$d(__spreadValues$d({}, defaultFetchOptions), fetchOptions),\n      cancel: () => {\n        isCanceled = true;\n      }\n    };\n    if (options.beforeFetch)\n      Object.assign(context, await options.beforeFetch(context));\n    if (isCanceled || !fetch) {\n      loading(false);\n      return Promise.resolve(null);\n    }\n    let responseData = null;\n    if (timer)\n      timer.start();\n    return new Promise((resolve, reject) => {\n      var _a3;\n      fetch(context.url, __spreadProps$3(__spreadValues$d(__spreadValues$d({}, defaultFetchOptions), context.options), {\n        headers: __spreadValues$d(__spreadValues$d({}, headersToObject(defaultFetchOptions.headers)), headersToObject((_a3 = context.options) == null ? void 0 : _a3.headers))\n      })).then(async (fetchResponse) => {\n        response.value = fetchResponse;\n        statusCode.value = fetchResponse.status;\n        responseData = await fetchResponse[config.type]();\n        if (options.afterFetch && statusCode.value >= 200 && statusCode.value < 300)\n          ({ data: responseData } = await options.afterFetch({ data: responseData, response: fetchResponse }));\n        data.value = responseData;\n        if (!fetchResponse.ok)\n          throw new Error(fetchResponse.statusText);\n        responseEvent.trigger(fetchResponse);\n        return resolve(fetchResponse);\n      }).catch(async (fetchError) => {\n        let errorData = fetchError.message || fetchError.name;\n        if (options.onFetchError)\n          ({ data: responseData, error: errorData } = await options.onFetchError({ data: responseData, error: fetchError, response: response.value }));\n        data.value = responseData;\n        error.value = errorData;\n        errorEvent.trigger(fetchError);\n        if (throwOnFailed)\n          return reject(fetchError);\n        return resolve(null);\n      }).finally(() => {\n        loading(false);\n        if (timer)\n          timer.stop();\n        finallyEvent.trigger(null);\n      });\n    });\n  };\n  const refetch = resolveRef(options.refetch);\n  watch([\n    refetch,\n    resolveRef(url)\n  ], ([refetch2]) => refetch2 && execute(), { deep: true });\n  const shell = {\n    isFinished,\n    statusCode,\n    response,\n    error,\n    data,\n    isFetching,\n    canAbort,\n    aborted,\n    abort,\n    execute,\n    onFetchResponse: responseEvent.on,\n    onFetchError: errorEvent.on,\n    onFetchFinally: finallyEvent.on,\n    get: setMethod(\"GET\"),\n    put: setMethod(\"PUT\"),\n    post: setMethod(\"POST\"),\n    delete: setMethod(\"DELETE\"),\n    patch: setMethod(\"PATCH\"),\n    head: setMethod(\"HEAD\"),\n    options: setMethod(\"OPTIONS\"),\n    json: setType(\"json\"),\n    text: setType(\"text\"),\n    blob: setType(\"blob\"),\n    arrayBuffer: setType(\"arrayBuffer\"),\n    formData: setType(\"formData\")\n  };\n  function setMethod(method) {\n    return (payload, payloadType) => {\n      if (!isFetching.value) {\n        config.method = method;\n        config.payload = payload;\n        config.payloadType = payloadType;\n        if (isRef(config.payload)) {\n          watch([\n            refetch,\n            resolveRef(config.payload)\n          ], ([refetch2]) => refetch2 && execute(), { deep: true });\n        }\n        const rawPayload = resolveUnref(config.payload);\n        if (!payloadType && rawPayload && Object.getPrototypeOf(rawPayload) === Object.prototype && !(rawPayload instanceof FormData))\n          config.payloadType = \"json\";\n        return __spreadProps$3(__spreadValues$d({}, shell), {\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        });\n      }\n      return void 0;\n    };\n  }\n  function waitUntilFinished() {\n    return new Promise((resolve, reject) => {\n      until(isFinished).toBe(true).then(() => resolve(shell)).catch((error2) => reject(error2));\n    });\n  }\n  function setType(type) {\n    return () => {\n      if (!isFetching.value) {\n        config.type = type;\n        return __spreadProps$3(__spreadValues$d({}, shell), {\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        });\n      }\n      return void 0;\n    };\n  }\n  if (options.immediate)\n    setTimeout(execute, 0);\n  return __spreadProps$3(__spreadValues$d({}, shell), {\n    then(onFulfilled, onRejected) {\n      return waitUntilFinished().then(onFulfilled, onRejected);\n    }\n  });\n}\nfunction joinPaths(start, end) {\n  if (!start.endsWith(\"/\") && !end.startsWith(\"/\"))\n    return `${start}/${end}`;\n  return `${start}${end}`;\n}\n\nvar __defProp$c = Object.defineProperty;\nvar __getOwnPropSymbols$d = Object.getOwnPropertySymbols;\nvar __hasOwnProp$d = Object.prototype.hasOwnProperty;\nvar __propIsEnum$d = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$c = (obj, key, value) => key in obj ? __defProp$c(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$c = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$d.call(b, prop))\n      __defNormalProp$c(a, prop, b[prop]);\n  if (__getOwnPropSymbols$d)\n    for (var prop of __getOwnPropSymbols$d(b)) {\n      if (__propIsEnum$d.call(b, prop))\n        __defNormalProp$c(a, prop, b[prop]);\n    }\n  return a;\n};\nconst DEFAULT_OPTIONS = {\n  multiple: true,\n  accept: \"*\"\n};\nfunction useFileDialog(options = {}) {\n  const {\n    document = defaultDocument\n  } = options;\n  const files = ref(null);\n  let input;\n  if (document) {\n    input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.onchange = (event) => {\n      const result = event.target;\n      files.value = result.files;\n    };\n  }\n  const open = (localOptions) => {\n    if (!input)\n      return;\n    const _options = __spreadValues$c(__spreadValues$c(__spreadValues$c({}, DEFAULT_OPTIONS), options), localOptions);\n    input.multiple = _options.multiple;\n    input.accept = _options.accept;\n    if (hasOwn(_options, \"capture\"))\n      input.capture = _options.capture;\n    input.click();\n  };\n  const reset = () => {\n    files.value = null;\n    if (input)\n      input.value = \"\";\n  };\n  return {\n    files: readonly(files),\n    open,\n    reset\n  };\n}\n\nvar __defProp$b = Object.defineProperty;\nvar __getOwnPropSymbols$c = Object.getOwnPropertySymbols;\nvar __hasOwnProp$c = Object.prototype.hasOwnProperty;\nvar __propIsEnum$c = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$b = (obj, key, value) => key in obj ? __defProp$b(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$b = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$c.call(b, prop))\n      __defNormalProp$b(a, prop, b[prop]);\n  if (__getOwnPropSymbols$c)\n    for (var prop of __getOwnPropSymbols$c(b)) {\n      if (__propIsEnum$c.call(b, prop))\n        __defNormalProp$b(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useFileSystemAccess(options = {}) {\n  const {\n    window: _window = defaultWindow,\n    dataType = \"Text\"\n  } = unref(options);\n  const window = _window;\n  const isSupported = useSupported(() => window && \"showSaveFilePicker\" in window && \"showOpenFilePicker\" in window);\n  const fileHandle = ref();\n  const data = ref();\n  const file = ref();\n  const fileName = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.name) != null ? _b : \"\";\n  });\n  const fileMIME = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.type) != null ? _b : \"\";\n  });\n  const fileSize = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.size) != null ? _b : 0;\n  });\n  const fileLastModified = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.lastModified) != null ? _b : 0;\n  });\n  async function open(_options = {}) {\n    if (!isSupported.value)\n      return;\n    const [handle] = await window.showOpenFilePicker(__spreadValues$b(__spreadValues$b({}, unref(options)), _options));\n    fileHandle.value = handle;\n    await updateFile();\n    await updateData();\n  }\n  async function create(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker(__spreadValues$b(__spreadValues$b({}, unref(options)), _options));\n    data.value = void 0;\n    await updateFile();\n    await updateData();\n  }\n  async function save(_options = {}) {\n    if (!isSupported.value)\n      return;\n    if (!fileHandle.value)\n      return saveAs(_options);\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function saveAs(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker(__spreadValues$b(__spreadValues$b({}, unref(options)), _options));\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function updateFile() {\n    var _a;\n    file.value = await ((_a = fileHandle.value) == null ? void 0 : _a.getFile());\n  }\n  async function updateData() {\n    var _a, _b;\n    if (unref(dataType) === \"Text\")\n      data.value = await ((_a = file.value) == null ? void 0 : _a.text());\n    if (unref(dataType) === \"ArrayBuffer\")\n      data.value = await ((_b = file.value) == null ? void 0 : _b.arrayBuffer());\n    if (unref(dataType) === \"Blob\")\n      data.value = file.value;\n  }\n  watch(() => unref(dataType), updateData);\n  return {\n    isSupported,\n    data,\n    file,\n    fileName,\n    fileMIME,\n    fileSize,\n    fileLastModified,\n    open,\n    create,\n    save,\n    saveAs,\n    updateData\n  };\n}\n\nfunction useFocus(target, options = {}) {\n  const { initialValue = false } = options;\n  const innerFocused = ref(false);\n  const targetElement = computed(() => unrefElement(target));\n  useEventListener(targetElement, \"focus\", () => innerFocused.value = true);\n  useEventListener(targetElement, \"blur\", () => innerFocused.value = false);\n  const focused = computed({\n    get: () => innerFocused.value,\n    set(value) {\n      var _a, _b;\n      if (!value && innerFocused.value)\n        (_a = targetElement.value) == null ? void 0 : _a.blur();\n      else if (value && !innerFocused.value)\n        (_b = targetElement.value) == null ? void 0 : _b.focus();\n    }\n  });\n  watch(targetElement, () => {\n    focused.value = initialValue;\n  }, { immediate: true, flush: \"post\" });\n  return { focused };\n}\n\nfunction useFocusWithin(target, options = {}) {\n  const activeElement = useActiveElement(options);\n  const targetElement = computed(() => unrefElement(target));\n  const focused = computed(() => targetElement.value && activeElement.value ? targetElement.value.contains(activeElement.value) : false);\n  return { focused };\n}\n\nfunction useFps(options) {\n  var _a;\n  const fps = ref(0);\n  if (typeof performance === \"undefined\")\n    return fps;\n  const every = (_a = options == null ? void 0 : options.every) != null ? _a : 10;\n  let last = performance.now();\n  let ticks = 0;\n  useRafFn(() => {\n    ticks += 1;\n    if (ticks >= every) {\n      const now = performance.now();\n      const diff = now - last;\n      fps.value = Math.round(1e3 / (diff / ticks));\n      last = now;\n      ticks = 0;\n    }\n  });\n  return fps;\n}\n\nconst functionsMap = [\n  [\n    \"requestFullscreen\",\n    \"exitFullscreen\",\n    \"fullscreenElement\",\n    \"fullscreenEnabled\",\n    \"fullscreenchange\",\n    \"fullscreenerror\"\n  ],\n  [\n    \"webkitRequestFullscreen\",\n    \"webkitExitFullscreen\",\n    \"webkitFullscreenElement\",\n    \"webkitFullscreenEnabled\",\n    \"webkitfullscreenchange\",\n    \"webkitfullscreenerror\"\n  ],\n  [\n    \"webkitRequestFullScreen\",\n    \"webkitCancelFullScreen\",\n    \"webkitCurrentFullScreenElement\",\n    \"webkitCancelFullScreen\",\n    \"webkitfullscreenchange\",\n    \"webkitfullscreenerror\"\n  ],\n  [\n    \"mozRequestFullScreen\",\n    \"mozCancelFullScreen\",\n    \"mozFullScreenElement\",\n    \"mozFullScreenEnabled\",\n    \"mozfullscreenchange\",\n    \"mozfullscreenerror\"\n  ],\n  [\n    \"msRequestFullscreen\",\n    \"msExitFullscreen\",\n    \"msFullscreenElement\",\n    \"msFullscreenEnabled\",\n    \"MSFullscreenChange\",\n    \"MSFullscreenError\"\n  ]\n];\nfunction useFullscreen(target, options = {}) {\n  const { document = defaultDocument, autoExit = false } = options;\n  const targetRef = target || (document == null ? void 0 : document.querySelector(\"html\"));\n  const isFullscreen = ref(false);\n  let map = functionsMap[0];\n  const isSupported = useSupported(() => {\n    if (!document) {\n      return false;\n    } else {\n      for (const m of functionsMap) {\n        if (m[1] in document) {\n          map = m;\n          return true;\n        }\n      }\n    }\n    return false;\n  });\n  const [REQUEST, EXIT, ELEMENT, , EVENT] = map;\n  async function exit() {\n    if (!isSupported.value)\n      return;\n    if (document == null ? void 0 : document[ELEMENT])\n      await document[EXIT]();\n    isFullscreen.value = false;\n  }\n  async function enter() {\n    if (!isSupported.value)\n      return;\n    await exit();\n    const target2 = unrefElement(targetRef);\n    if (target2) {\n      await target2[REQUEST]();\n      isFullscreen.value = true;\n    }\n  }\n  async function toggle() {\n    if (isFullscreen.value)\n      await exit();\n    else\n      await enter();\n  }\n  if (document) {\n    useEventListener(document, EVENT, () => {\n      isFullscreen.value = !!(document == null ? void 0 : document[ELEMENT]);\n    }, false);\n  }\n  if (autoExit)\n    tryOnScopeDispose(exit);\n  return {\n    isSupported,\n    isFullscreen,\n    enter,\n    exit,\n    toggle\n  };\n}\n\nfunction mapGamepadToXbox360Controller(gamepad) {\n  return computed(() => {\n    if (gamepad.value) {\n      return {\n        buttons: {\n          a: gamepad.value.buttons[0],\n          b: gamepad.value.buttons[1],\n          x: gamepad.value.buttons[2],\n          y: gamepad.value.buttons[3]\n        },\n        bumper: {\n          left: gamepad.value.buttons[4],\n          right: gamepad.value.buttons[5]\n        },\n        triggers: {\n          left: gamepad.value.buttons[6],\n          right: gamepad.value.buttons[7]\n        },\n        stick: {\n          left: {\n            horizontal: gamepad.value.axes[0],\n            vertical: gamepad.value.axes[1],\n            button: gamepad.value.buttons[10]\n          },\n          right: {\n            horizontal: gamepad.value.axes[2],\n            vertical: gamepad.value.axes[3],\n            button: gamepad.value.buttons[11]\n          }\n        },\n        dpad: {\n          up: gamepad.value.buttons[12],\n          down: gamepad.value.buttons[13],\n          left: gamepad.value.buttons[14],\n          right: gamepad.value.buttons[15]\n        },\n        back: gamepad.value.buttons[8],\n        start: gamepad.value.buttons[9]\n      };\n    }\n    return null;\n  });\n}\nfunction useGamepad(options = {}) {\n  const {\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"getGamepads\" in navigator);\n  const gamepads = ref([]);\n  const onConnectedHook = createEventHook();\n  const onDisconnectedHook = createEventHook();\n  const stateFromGamepad = (gamepad) => {\n    const hapticActuators = [];\n    const vibrationActuator = \"vibrationActuator\" in gamepad ? gamepad.vibrationActuator : null;\n    if (vibrationActuator)\n      hapticActuators.push(vibrationActuator);\n    if (gamepad.hapticActuators)\n      hapticActuators.push(...gamepad.hapticActuators);\n    return {\n      id: gamepad.id,\n      hapticActuators,\n      index: gamepad.index,\n      mapping: gamepad.mapping,\n      connected: gamepad.connected,\n      timestamp: gamepad.timestamp,\n      axes: gamepad.axes.map((axes) => axes),\n      buttons: gamepad.buttons.map((button) => ({ pressed: button.pressed, touched: button.touched, value: button.value }))\n    };\n  };\n  const updateGamepadState = () => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (let i = 0; i < _gamepads.length; ++i) {\n      const gamepad = _gamepads[i];\n      if (gamepad) {\n        const index = gamepads.value.findIndex(({ index: index2 }) => index2 === gamepad.index);\n        if (index > -1)\n          gamepads.value[index] = stateFromGamepad(gamepad);\n      }\n    }\n  };\n  const { isActive, pause, resume } = useRafFn(updateGamepadState);\n  const onGamepadConnected = (gamepad) => {\n    if (!gamepads.value.some(({ index }) => index === gamepad.index)) {\n      gamepads.value.push(stateFromGamepad(gamepad));\n      onConnectedHook.trigger(gamepad.index);\n    }\n    resume();\n  };\n  const onGamepadDisconnected = (gamepad) => {\n    gamepads.value = gamepads.value.filter((x) => x.index !== gamepad.index);\n    onDisconnectedHook.trigger(gamepad.index);\n  };\n  useEventListener(\"gamepadconnected\", (e) => onGamepadConnected(e.gamepad));\n  useEventListener(\"gamepaddisconnected\", (e) => onGamepadDisconnected(e.gamepad));\n  tryOnMounted(() => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    if (_gamepads) {\n      for (let i = 0; i < _gamepads.length; ++i) {\n        const gamepad = _gamepads[i];\n        if (gamepad)\n          onGamepadConnected(gamepad);\n      }\n    }\n  });\n  pause();\n  return {\n    isSupported,\n    onConnected: onConnectedHook.on,\n    onDisconnected: onDisconnectedHook.on,\n    gamepads,\n    pause,\n    resume,\n    isActive\n  };\n}\n\nfunction useGeolocation(options = {}) {\n  const {\n    enableHighAccuracy = true,\n    maximumAge = 3e4,\n    timeout = 27e3,\n    navigator = defaultNavigator,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => navigator && \"geolocation\" in navigator);\n  const locatedAt = ref(null);\n  const error = ref(null);\n  const coords = ref({\n    accuracy: 0,\n    latitude: Infinity,\n    longitude: Infinity,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    speed: null\n  });\n  function updatePosition(position) {\n    locatedAt.value = position.timestamp;\n    coords.value = position.coords;\n    error.value = null;\n  }\n  let watcher;\n  function resume() {\n    if (isSupported.value) {\n      watcher = navigator.geolocation.watchPosition(updatePosition, (err) => error.value = err, {\n        enableHighAccuracy,\n        maximumAge,\n        timeout\n      });\n    }\n  }\n  if (immediate)\n    resume();\n  function pause() {\n    if (watcher && navigator)\n      navigator.geolocation.clearWatch(watcher);\n  }\n  tryOnScopeDispose(() => {\n    pause();\n  });\n  return {\n    isSupported,\n    coords,\n    locatedAt,\n    error,\n    resume,\n    pause\n  };\n}\n\nconst defaultEvents$1 = [\"mousemove\", \"mousedown\", \"resize\", \"keydown\", \"touchstart\", \"wheel\"];\nconst oneMinute = 6e4;\nfunction useIdle(timeout = oneMinute, options = {}) {\n  const {\n    initialState = false,\n    listenForVisibilityChange = true,\n    events = defaultEvents$1,\n    window = defaultWindow,\n    eventFilter = throttleFilter(50)\n  } = options;\n  const idle = ref(initialState);\n  const lastActive = ref(timestamp());\n  let timer;\n  const onEvent = createFilterWrapper(eventFilter, () => {\n    idle.value = false;\n    lastActive.value = timestamp();\n    clearTimeout(timer);\n    timer = setTimeout(() => idle.value = true, timeout);\n  });\n  if (window) {\n    const document = window.document;\n    for (const event of events)\n      useEventListener(window, event, onEvent, { passive: true });\n    if (listenForVisibilityChange) {\n      useEventListener(document, \"visibilitychange\", () => {\n        if (!document.hidden)\n          onEvent();\n      });\n    }\n  }\n  timer = setTimeout(() => idle.value = true, timeout);\n  return { idle, lastActive };\n}\n\nvar __defProp$a = Object.defineProperty;\nvar __getOwnPropSymbols$b = Object.getOwnPropertySymbols;\nvar __hasOwnProp$b = Object.prototype.hasOwnProperty;\nvar __propIsEnum$b = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$a = (obj, key, value) => key in obj ? __defProp$a(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$a = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$b.call(b, prop))\n      __defNormalProp$a(a, prop, b[prop]);\n  if (__getOwnPropSymbols$b)\n    for (var prop of __getOwnPropSymbols$b(b)) {\n      if (__propIsEnum$b.call(b, prop))\n        __defNormalProp$a(a, prop, b[prop]);\n    }\n  return a;\n};\nasync function loadImage(options) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const { src, srcset, sizes } = options;\n    img.src = src;\n    if (srcset)\n      img.srcset = srcset;\n    if (sizes)\n      img.sizes = sizes;\n    img.onload = () => resolve(img);\n    img.onerror = reject;\n  });\n}\nconst useImage = (options, asyncStateOptions = {}) => {\n  const state = useAsyncState(() => loadImage(resolveUnref(options)), void 0, __spreadValues$a({\n    resetOnExecute: true\n  }, asyncStateOptions));\n  watch(() => resolveUnref(options), () => state.execute(asyncStateOptions.delay), { deep: true });\n  return state;\n};\n\nconst ARRIVED_STATE_THRESHOLD_PIXELS = 1;\nfunction useScroll(element, options = {}) {\n  const {\n    throttle = 0,\n    idle = 200,\n    onStop = noop,\n    onScroll = noop,\n    offset = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    },\n    eventListenerOptions = {\n      capture: false,\n      passive: true\n    },\n    behavior = \"auto\"\n  } = options;\n  const internalX = ref(0);\n  const internalY = ref(0);\n  const x = computed({\n    get() {\n      return internalX.value;\n    },\n    set(x2) {\n      scrollTo(x2, void 0);\n    }\n  });\n  const y = computed({\n    get() {\n      return internalY.value;\n    },\n    set(y2) {\n      scrollTo(void 0, y2);\n    }\n  });\n  function scrollTo(_x, _y) {\n    var _a, _b, _c;\n    const _element = resolveUnref(element);\n    if (!_element)\n      return;\n    (_c = _element instanceof Document ? document.body : _element) == null ? void 0 : _c.scrollTo({\n      top: (_a = resolveUnref(_y)) != null ? _a : y.value,\n      left: (_b = resolveUnref(_x)) != null ? _b : x.value,\n      behavior: resolveUnref(behavior)\n    });\n  }\n  const isScrolling = ref(false);\n  const arrivedState = reactive({\n    left: true,\n    right: false,\n    top: true,\n    bottom: false\n  });\n  const directions = reactive({\n    left: false,\n    right: false,\n    top: false,\n    bottom: false\n  });\n  const onScrollEnd = (e) => {\n    if (!isScrolling.value)\n      return;\n    isScrolling.value = false;\n    directions.left = false;\n    directions.right = false;\n    directions.top = false;\n    directions.bottom = false;\n    onStop(e);\n  };\n  const onScrollEndDebounced = useDebounceFn(onScrollEnd, throttle + idle);\n  const onScrollHandler = (e) => {\n    const eventTarget = e.target === document ? e.target.documentElement : e.target;\n    const scrollLeft = eventTarget.scrollLeft;\n    directions.left = scrollLeft < internalX.value;\n    directions.right = scrollLeft > internalY.value;\n    arrivedState.left = scrollLeft <= 0 + (offset.left || 0);\n    arrivedState.right = scrollLeft + eventTarget.clientWidth >= eventTarget.scrollWidth - (offset.right || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    internalX.value = scrollLeft;\n    let scrollTop = eventTarget.scrollTop;\n    if (e.target === document && !scrollTop)\n      scrollTop = document.body.scrollTop;\n    directions.top = scrollTop < internalY.value;\n    directions.bottom = scrollTop > internalY.value;\n    arrivedState.top = scrollTop <= 0 + (offset.top || 0);\n    arrivedState.bottom = scrollTop + eventTarget.clientHeight >= eventTarget.scrollHeight - (offset.bottom || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    internalY.value = scrollTop;\n    isScrolling.value = true;\n    onScrollEndDebounced(e);\n    onScroll(e);\n  };\n  useEventListener(element, \"scroll\", throttle ? useThrottleFn(onScrollHandler, throttle, true, false) : onScrollHandler, eventListenerOptions);\n  useEventListener(element, \"scrollend\", onScrollEnd, eventListenerOptions);\n  return {\n    x,\n    y,\n    isScrolling,\n    arrivedState,\n    directions\n  };\n}\n\nvar __defProp$9 = Object.defineProperty;\nvar __defProps$2 = Object.defineProperties;\nvar __getOwnPropDescs$2 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$a = Object.getOwnPropertySymbols;\nvar __hasOwnProp$a = Object.prototype.hasOwnProperty;\nvar __propIsEnum$a = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$9 = (obj, key, value) => key in obj ? __defProp$9(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$9 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$a.call(b, prop))\n      __defNormalProp$9(a, prop, b[prop]);\n  if (__getOwnPropSymbols$a)\n    for (var prop of __getOwnPropSymbols$a(b)) {\n      if (__propIsEnum$a.call(b, prop))\n        __defNormalProp$9(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$2 = (a, b) => __defProps$2(a, __getOwnPropDescs$2(b));\nfunction useInfiniteScroll(element, onLoadMore, options = {}) {\n  var _a, _b;\n  const direction = (_a = options.direction) != null ? _a : \"bottom\";\n  const state = reactive(useScroll(element, __spreadProps$2(__spreadValues$9({}, options), {\n    offset: __spreadValues$9({\n      [direction]: (_b = options.distance) != null ? _b : 0\n    }, options.offset)\n  })));\n  watch(() => state.arrivedState[direction], async (v) => {\n    var _a2, _b2;\n    if (v) {\n      const elem = resolveUnref(element);\n      const previous = {\n        height: (_a2 = elem == null ? void 0 : elem.scrollHeight) != null ? _a2 : 0,\n        width: (_b2 = elem == null ? void 0 : elem.scrollWidth) != null ? _b2 : 0\n      };\n      await onLoadMore(state);\n      if (options.preserveScrollPosition && elem) {\n        nextTick(() => {\n          elem.scrollTo({\n            top: elem.scrollHeight - previous.height,\n            left: elem.scrollWidth - previous.width\n          });\n        });\n      }\n    }\n  });\n}\n\nfunction useIntersectionObserver(target, callback, options = {}) {\n  const {\n    root,\n    rootMargin = \"0px\",\n    threshold = 0.1,\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"IntersectionObserver\" in window);\n  let cleanup = noop;\n  const stopWatch = isSupported.value ? watch(() => ({\n    el: unrefElement(target),\n    root: unrefElement(root)\n  }), ({ el, root: root2 }) => {\n    cleanup();\n    if (!el)\n      return;\n    const observer = new IntersectionObserver(callback, {\n      root: root2,\n      rootMargin,\n      threshold\n    });\n    observer.observe(el);\n    cleanup = () => {\n      observer.disconnect();\n      cleanup = noop;\n    };\n  }, { immediate: true, flush: \"post\" }) : noop;\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nconst defaultEvents = [\"mousedown\", \"mouseup\", \"keydown\", \"keyup\"];\nfunction useKeyModifier(modifier, options = {}) {\n  const {\n    events = defaultEvents,\n    document = defaultDocument,\n    initial = null\n  } = options;\n  const state = ref(initial);\n  if (document) {\n    events.forEach((listenerEvent) => {\n      useEventListener(document, listenerEvent, (evt) => {\n        if (typeof evt.getModifierState === \"function\")\n          state.value = evt.getModifierState(modifier);\n      });\n    });\n  }\n  return state;\n}\n\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.localStorage, options);\n}\n\nconst DefaultMagicKeysAliasMap = {\n  ctrl: \"control\",\n  command: \"meta\",\n  cmd: \"meta\",\n  option: \"alt\",\n  up: \"arrowup\",\n  down: \"arrowdown\",\n  left: \"arrowleft\",\n  right: \"arrowright\"\n};\n\nfunction useMagicKeys(options = {}) {\n  const {\n    reactive: useReactive = false,\n    target = defaultWindow,\n    aliasMap = DefaultMagicKeysAliasMap,\n    passive = true,\n    onEventFired = noop\n  } = options;\n  const current = reactive(new Set());\n  const obj = {\n    toJSON() {\n      return {};\n    },\n    current\n  };\n  const refs = useReactive ? reactive(obj) : obj;\n  const metaDeps = new Set();\n  const usedKeys = new Set();\n  function setRefs(key, value) {\n    if (key in refs) {\n      if (useReactive)\n        refs[key] = value;\n      else\n        refs[key].value = value;\n    }\n  }\n  function reset() {\n    current.clear();\n    for (const key of usedKeys)\n      setRefs(key, false);\n  }\n  function updateRefs(e, value) {\n    var _a, _b;\n    const key = (_a = e.key) == null ? void 0 : _a.toLowerCase();\n    const code = (_b = e.code) == null ? void 0 : _b.toLowerCase();\n    const values = [code, key].filter(Boolean);\n    if (key) {\n      if (value)\n        current.add(key);\n      else\n        current.delete(key);\n    }\n    for (const key2 of values) {\n      usedKeys.add(key2);\n      setRefs(key2, value);\n    }\n    if (key === \"meta\" && !value) {\n      metaDeps.forEach((key2) => {\n        current.delete(key2);\n        setRefs(key2, false);\n      });\n      metaDeps.clear();\n    } else if (typeof e.getModifierState === \"function\" && e.getModifierState(\"Meta\") && value) {\n      [...current, ...values].forEach((key2) => metaDeps.add(key2));\n    }\n  }\n  useEventListener(target, \"keydown\", (e) => {\n    updateRefs(e, true);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(target, \"keyup\", (e) => {\n    updateRefs(e, false);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(\"blur\", reset, { passive: true });\n  useEventListener(\"focus\", reset, { passive: true });\n  const proxy = new Proxy(refs, {\n    get(target2, prop, rec) {\n      if (typeof prop !== \"string\")\n        return Reflect.get(target2, prop, rec);\n      prop = prop.toLowerCase();\n      if (prop in aliasMap)\n        prop = aliasMap[prop];\n      if (!(prop in refs)) {\n        if (/[+_-]/.test(prop)) {\n          const keys = prop.split(/[+_-]/g).map((i) => i.trim());\n          refs[prop] = computed(() => keys.every((key) => unref(proxy[key])));\n        } else {\n          refs[prop] = ref(false);\n        }\n      }\n      const r = Reflect.get(target2, prop, rec);\n      return useReactive ? unref(r) : r;\n    }\n  });\n  return proxy;\n}\n\nvar __defProp$8 = Object.defineProperty;\nvar __getOwnPropSymbols$9 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$9 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$9 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$8 = (obj, key, value) => key in obj ? __defProp$8(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$8 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$9.call(b, prop))\n      __defNormalProp$8(a, prop, b[prop]);\n  if (__getOwnPropSymbols$9)\n    for (var prop of __getOwnPropSymbols$9(b)) {\n      if (__propIsEnum$9.call(b, prop))\n        __defNormalProp$8(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction usingElRef(source, cb) {\n  if (resolveUnref(source))\n    cb(resolveUnref(source));\n}\nfunction timeRangeToArray(timeRanges) {\n  let ranges = [];\n  for (let i = 0; i < timeRanges.length; ++i)\n    ranges = [...ranges, [timeRanges.start(i), timeRanges.end(i)]];\n  return ranges;\n}\nfunction tracksToArray(tracks) {\n  return Array.from(tracks).map(({ label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }, id) => ({ id, label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }));\n}\nconst defaultOptions = {\n  src: \"\",\n  tracks: []\n};\nfunction useMediaControls(target, options = {}) {\n  options = __spreadValues$8(__spreadValues$8({}, defaultOptions), options);\n  const {\n    document = defaultDocument\n  } = options;\n  const currentTime = ref(0);\n  const duration = ref(0);\n  const seeking = ref(false);\n  const volume = ref(1);\n  const waiting = ref(false);\n  const ended = ref(false);\n  const playing = ref(false);\n  const rate = ref(1);\n  const stalled = ref(false);\n  const buffered = ref([]);\n  const tracks = ref([]);\n  const selectedTrack = ref(-1);\n  const isPictureInPicture = ref(false);\n  const muted = ref(false);\n  const supportsPictureInPicture = document && \"pictureInPictureEnabled\" in document;\n  const sourceErrorEvent = createEventHook();\n  const disableTrack = (track) => {\n    usingElRef(target, (el) => {\n      if (track) {\n        const id = isNumber(track) ? track : track.id;\n        el.textTracks[id].mode = \"disabled\";\n      } else {\n        for (let i = 0; i < el.textTracks.length; ++i)\n          el.textTracks[i].mode = \"disabled\";\n      }\n      selectedTrack.value = -1;\n    });\n  };\n  const enableTrack = (track, disableTracks = true) => {\n    usingElRef(target, (el) => {\n      const id = isNumber(track) ? track : track.id;\n      if (disableTracks)\n        disableTrack();\n      el.textTracks[id].mode = \"showing\";\n      selectedTrack.value = id;\n    });\n  };\n  const togglePictureInPicture = () => {\n    return new Promise((resolve, reject) => {\n      usingElRef(target, async (el) => {\n        if (supportsPictureInPicture) {\n          if (!isPictureInPicture.value) {\n            el.requestPictureInPicture().then(resolve).catch(reject);\n          } else {\n            document.exitPictureInPicture().then(resolve).catch(reject);\n          }\n        }\n      });\n    });\n  };\n  watchEffect(() => {\n    if (!document)\n      return;\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    const src = resolveUnref(options.src);\n    let sources = [];\n    if (!src)\n      return;\n    if (isString(src))\n      sources = [{ src }];\n    else if (Array.isArray(src))\n      sources = src;\n    else if (isObject(src))\n      sources = [src];\n    el.querySelectorAll(\"source\").forEach((e) => {\n      e.removeEventListener(\"error\", sourceErrorEvent.trigger);\n      e.remove();\n    });\n    sources.forEach(({ src: src2, type }) => {\n      const source = document.createElement(\"source\");\n      source.setAttribute(\"src\", src2);\n      source.setAttribute(\"type\", type || \"\");\n      source.addEventListener(\"error\", sourceErrorEvent.trigger);\n      el.appendChild(source);\n    });\n    el.load();\n  });\n  tryOnScopeDispose(() => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.querySelectorAll(\"source\").forEach((e) => e.removeEventListener(\"error\", sourceErrorEvent.trigger));\n  });\n  watch(volume, (vol) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.volume = vol;\n  });\n  watch(muted, (mute) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.muted = mute;\n  });\n  watch(rate, (rate2) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.playbackRate = rate2;\n  });\n  watchEffect(() => {\n    if (!document)\n      return;\n    const textTracks = resolveUnref(options.tracks);\n    const el = resolveUnref(target);\n    if (!textTracks || !textTracks.length || !el)\n      return;\n    el.querySelectorAll(\"track\").forEach((e) => e.remove());\n    textTracks.forEach(({ default: isDefault, kind, label, src, srcLang }, i) => {\n      const track = document.createElement(\"track\");\n      track.default = isDefault || false;\n      track.kind = kind;\n      track.label = label;\n      track.src = src;\n      track.srclang = srcLang;\n      if (track.default)\n        selectedTrack.value = i;\n      el.appendChild(track);\n    });\n  });\n  const { ignoreUpdates: ignoreCurrentTimeUpdates } = watchIgnorable(currentTime, (time) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    el.currentTime = time;\n  });\n  const { ignoreUpdates: ignorePlayingUpdates } = watchIgnorable(playing, (isPlaying) => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    isPlaying ? el.play() : el.pause();\n  });\n  useEventListener(target, \"timeupdate\", () => ignoreCurrentTimeUpdates(() => currentTime.value = resolveUnref(target).currentTime));\n  useEventListener(target, \"durationchange\", () => duration.value = resolveUnref(target).duration);\n  useEventListener(target, \"progress\", () => buffered.value = timeRangeToArray(resolveUnref(target).buffered));\n  useEventListener(target, \"seeking\", () => seeking.value = true);\n  useEventListener(target, \"seeked\", () => seeking.value = false);\n  useEventListener(target, \"waiting\", () => waiting.value = true);\n  useEventListener(target, \"playing\", () => {\n    waiting.value = false;\n    ended.value = false;\n  });\n  useEventListener(target, \"ratechange\", () => rate.value = resolveUnref(target).playbackRate);\n  useEventListener(target, \"stalled\", () => stalled.value = true);\n  useEventListener(target, \"ended\", () => ended.value = true);\n  useEventListener(target, \"pause\", () => ignorePlayingUpdates(() => playing.value = false));\n  useEventListener(target, \"play\", () => ignorePlayingUpdates(() => playing.value = true));\n  useEventListener(target, \"enterpictureinpicture\", () => isPictureInPicture.value = true);\n  useEventListener(target, \"leavepictureinpicture\", () => isPictureInPicture.value = false);\n  useEventListener(target, \"volumechange\", () => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    volume.value = el.volume;\n    muted.value = el.muted;\n  });\n  const listeners = [];\n  const stop = watch([target], () => {\n    const el = resolveUnref(target);\n    if (!el)\n      return;\n    stop();\n    listeners[0] = useEventListener(el.textTracks, \"addtrack\", () => tracks.value = tracksToArray(el.textTracks));\n    listeners[1] = useEventListener(el.textTracks, \"removetrack\", () => tracks.value = tracksToArray(el.textTracks));\n    listeners[2] = useEventListener(el.textTracks, \"change\", () => tracks.value = tracksToArray(el.textTracks));\n  });\n  tryOnScopeDispose(() => listeners.forEach((listener) => listener()));\n  return {\n    currentTime,\n    duration,\n    waiting,\n    seeking,\n    ended,\n    stalled,\n    buffered,\n    playing,\n    rate,\n    volume,\n    muted,\n    tracks,\n    selectedTrack,\n    enableTrack,\n    disableTrack,\n    supportsPictureInPicture,\n    togglePictureInPicture,\n    isPictureInPicture,\n    onSourceError: sourceErrorEvent.on\n  };\n}\n\nconst getMapVue2Compat = () => {\n  const data = reactive({});\n  return {\n    get: (key) => data[key],\n    set: (key, value) => set(data, key, value),\n    has: (key) => hasOwn(data, key),\n    delete: (key) => del(data, key),\n    clear: () => {\n      Object.keys(data).forEach((key) => {\n        del(data, key);\n      });\n    }\n  };\n};\nfunction useMemoize(resolver, options) {\n  const initCache = () => {\n    if (options == null ? void 0 : options.cache)\n      return reactive(options.cache);\n    if (isVue2)\n      return getMapVue2Compat();\n    return reactive(new Map());\n  };\n  const cache = initCache();\n  const generateKey = (...args) => (options == null ? void 0 : options.getKey) ? options.getKey(...args) : JSON.stringify(args);\n  const _loadData = (key, ...args) => {\n    cache.set(key, resolver(...args));\n    return cache.get(key);\n  };\n  const loadData = (...args) => _loadData(generateKey(...args), ...args);\n  const deleteData = (...args) => {\n    cache.delete(generateKey(...args));\n  };\n  const clearData = () => {\n    cache.clear();\n  };\n  const memoized = (...args) => {\n    const key = generateKey(...args);\n    if (cache.has(key))\n      return cache.get(key);\n    return _loadData(key, ...args);\n  };\n  memoized.load = loadData;\n  memoized.delete = deleteData;\n  memoized.clear = clearData;\n  memoized.generateKey = generateKey;\n  memoized.cache = cache;\n  return memoized;\n}\n\nfunction useMemory(options = {}) {\n  const memory = ref();\n  const isSupported = useSupported(() => typeof performance !== \"undefined\" && \"memory\" in performance);\n  if (isSupported.value) {\n    const { interval = 1e3 } = options;\n    useIntervalFn(() => {\n      memory.value = performance.memory;\n    }, interval, { immediate: options.immediate, immediateCallback: options.immediateCallback });\n  }\n  return { isSupported, memory };\n}\n\nfunction useMounted() {\n  const isMounted = ref(false);\n  onMounted(() => {\n    isMounted.value = true;\n  });\n  return isMounted;\n}\n\nfunction useMouse(options = {}) {\n  const {\n    type = \"page\",\n    touch = true,\n    resetOnTouchEnds = false,\n    initialValue = { x: 0, y: 0 },\n    window = defaultWindow,\n    eventFilter\n  } = options;\n  const x = ref(initialValue.x);\n  const y = ref(initialValue.y);\n  const sourceType = ref(null);\n  const mouseHandler = (event) => {\n    if (type === \"page\") {\n      x.value = event.pageX;\n      y.value = event.pageY;\n    } else if (type === \"client\") {\n      x.value = event.clientX;\n      y.value = event.clientY;\n    } else if (type === \"movement\") {\n      x.value = event.movementX;\n      y.value = event.movementY;\n    }\n    sourceType.value = \"mouse\";\n  };\n  const reset = () => {\n    x.value = initialValue.x;\n    y.value = initialValue.y;\n  };\n  const touchHandler = (event) => {\n    if (event.touches.length > 0) {\n      const touch2 = event.touches[0];\n      if (type === \"page\") {\n        x.value = touch2.pageX;\n        y.value = touch2.pageY;\n      } else if (type === \"client\") {\n        x.value = touch2.clientX;\n        y.value = touch2.clientY;\n      }\n      sourceType.value = \"touch\";\n    }\n  };\n  const mouseHandlerWrapper = (event) => {\n    return eventFilter === void 0 ? mouseHandler(event) : eventFilter(() => mouseHandler(event), {});\n  };\n  const touchHandlerWrapper = (event) => {\n    return eventFilter === void 0 ? touchHandler(event) : eventFilter(() => touchHandler(event), {});\n  };\n  if (window) {\n    useEventListener(window, \"mousemove\", mouseHandlerWrapper, { passive: true });\n    useEventListener(window, \"dragover\", mouseHandlerWrapper, { passive: true });\n    if (touch && type !== \"movement\") {\n      useEventListener(window, \"touchstart\", touchHandlerWrapper, { passive: true });\n      useEventListener(window, \"touchmove\", touchHandlerWrapper, { passive: true });\n      if (resetOnTouchEnds)\n        useEventListener(window, \"touchend\", reset, { passive: true });\n    }\n  }\n  return {\n    x,\n    y,\n    sourceType\n  };\n}\n\nfunction useMouseInElement(target, options = {}) {\n  const {\n    handleOutside = true,\n    window = defaultWindow\n  } = options;\n  const { x, y, sourceType } = useMouse(options);\n  const targetRef = ref(target != null ? target : window == null ? void 0 : window.document.body);\n  const elementX = ref(0);\n  const elementY = ref(0);\n  const elementPositionX = ref(0);\n  const elementPositionY = ref(0);\n  const elementHeight = ref(0);\n  const elementWidth = ref(0);\n  const isOutside = ref(true);\n  let stop = () => {\n  };\n  if (window) {\n    stop = watch([targetRef, x, y], () => {\n      const el = unrefElement(targetRef);\n      if (!el)\n        return;\n      const {\n        left,\n        top,\n        width,\n        height\n      } = el.getBoundingClientRect();\n      elementPositionX.value = left + window.pageXOffset;\n      elementPositionY.value = top + window.pageYOffset;\n      elementHeight.value = height;\n      elementWidth.value = width;\n      const elX = x.value - elementPositionX.value;\n      const elY = y.value - elementPositionY.value;\n      isOutside.value = width === 0 || height === 0 || elX < 0 || elY < 0 || elX > width || elY > height;\n      if (handleOutside || !isOutside.value) {\n        elementX.value = elX;\n        elementY.value = elY;\n      }\n    }, { immediate: true });\n    useEventListener(document, \"mouseleave\", () => {\n      isOutside.value = true;\n    });\n  }\n  return {\n    x,\n    y,\n    sourceType,\n    elementX,\n    elementY,\n    elementPositionX,\n    elementPositionY,\n    elementHeight,\n    elementWidth,\n    isOutside,\n    stop\n  };\n}\n\nfunction useMousePressed(options = {}) {\n  const {\n    touch = true,\n    drag = true,\n    initialValue = false,\n    window = defaultWindow\n  } = options;\n  const pressed = ref(initialValue);\n  const sourceType = ref(null);\n  if (!window) {\n    return {\n      pressed,\n      sourceType\n    };\n  }\n  const onPressed = (srcType) => () => {\n    pressed.value = true;\n    sourceType.value = srcType;\n  };\n  const onReleased = () => {\n    pressed.value = false;\n    sourceType.value = null;\n  };\n  const target = computed(() => unrefElement(options.target) || window);\n  useEventListener(target, \"mousedown\", onPressed(\"mouse\"), { passive: true });\n  useEventListener(window, \"mouseleave\", onReleased, { passive: true });\n  useEventListener(window, \"mouseup\", onReleased, { passive: true });\n  if (drag) {\n    useEventListener(target, \"dragstart\", onPressed(\"mouse\"), { passive: true });\n    useEventListener(window, \"drop\", onReleased, { passive: true });\n    useEventListener(window, \"dragend\", onReleased, { passive: true });\n  }\n  if (touch) {\n    useEventListener(target, \"touchstart\", onPressed(\"touch\"), { passive: true });\n    useEventListener(window, \"touchend\", onReleased, { passive: true });\n    useEventListener(window, \"touchcancel\", onReleased, { passive: true });\n  }\n  return {\n    pressed,\n    sourceType\n  };\n}\n\nvar __getOwnPropSymbols$8 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$8 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$8 = Object.prototype.propertyIsEnumerable;\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$8.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$8)\n    for (var prop of __getOwnPropSymbols$8(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$8.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nfunction useMutationObserver(target, callback, options = {}) {\n  const _a = options, { window = defaultWindow } = _a, mutationOptions = __objRest$1(_a, [\"window\"]);\n  let observer;\n  const isSupported = useSupported(() => window && \"MutationObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const stopWatch = watch(() => unrefElement(target), (el) => {\n    cleanup();\n    if (isSupported.value && window && el) {\n      observer = new MutationObserver(callback);\n      observer.observe(el, mutationOptions);\n    }\n  }, { immediate: true });\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nconst useNavigatorLanguage = (options = {}) => {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"language\" in navigator);\n  const language = ref(navigator == null ? void 0 : navigator.language);\n  useEventListener(window, \"languagechange\", () => {\n    if (navigator)\n      language.value = navigator.language;\n  });\n  return {\n    isSupported,\n    language\n  };\n};\n\nfunction useNetwork(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"connection\" in navigator);\n  const isOnline = ref(true);\n  const saveData = ref(false);\n  const offlineAt = ref(void 0);\n  const onlineAt = ref(void 0);\n  const downlink = ref(void 0);\n  const downlinkMax = ref(void 0);\n  const rtt = ref(void 0);\n  const effectiveType = ref(void 0);\n  const type = ref(\"unknown\");\n  const connection = isSupported.value && navigator.connection;\n  function updateNetworkInformation() {\n    if (!navigator)\n      return;\n    isOnline.value = navigator.onLine;\n    offlineAt.value = isOnline.value ? void 0 : Date.now();\n    onlineAt.value = isOnline.value ? Date.now() : void 0;\n    if (connection) {\n      downlink.value = connection.downlink;\n      downlinkMax.value = connection.downlinkMax;\n      effectiveType.value = connection.effectiveType;\n      rtt.value = connection.rtt;\n      saveData.value = connection.saveData;\n      type.value = connection.type;\n    }\n  }\n  if (window) {\n    useEventListener(window, \"offline\", () => {\n      isOnline.value = false;\n      offlineAt.value = Date.now();\n    });\n    useEventListener(window, \"online\", () => {\n      isOnline.value = true;\n      onlineAt.value = Date.now();\n    });\n  }\n  if (connection)\n    useEventListener(connection, \"change\", updateNetworkInformation, false);\n  updateNetworkInformation();\n  return {\n    isSupported,\n    isOnline,\n    saveData,\n    offlineAt,\n    onlineAt,\n    downlink,\n    downlinkMax,\n    effectiveType,\n    rtt,\n    type\n  };\n}\n\nvar __defProp$7 = Object.defineProperty;\nvar __getOwnPropSymbols$7 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$7 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$7 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$7 = (obj, key, value) => key in obj ? __defProp$7(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$7 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$7.call(b, prop))\n      __defNormalProp$7(a, prop, b[prop]);\n  if (__getOwnPropSymbols$7)\n    for (var prop of __getOwnPropSymbols$7(b)) {\n      if (__propIsEnum$7.call(b, prop))\n        __defNormalProp$7(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useNow(options = {}) {\n  const {\n    controls: exposeControls = false,\n    interval = \"requestAnimationFrame\"\n  } = options;\n  const now = ref(new Date());\n  const update = () => now.value = new Date();\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(update, { immediate: true }) : useIntervalFn(update, interval, { immediate: true });\n  if (exposeControls) {\n    return __spreadValues$7({\n      now\n    }, controls);\n  } else {\n    return now;\n  }\n}\n\nfunction useObjectUrl(object) {\n  const url = ref();\n  const release = () => {\n    if (url.value)\n      URL.revokeObjectURL(url.value);\n    url.value = void 0;\n  };\n  watch(() => unref(object), (newObject) => {\n    release();\n    if (newObject)\n      url.value = URL.createObjectURL(newObject);\n  }, { immediate: true });\n  tryOnScopeDispose(release);\n  return readonly(url);\n}\n\nfunction useClamp(value, min, max) {\n  if (isFunction(value) || isReadonly(value))\n    return computed(() => clamp(resolveUnref(value), resolveUnref(min), resolveUnref(max)));\n  const _value = ref(value);\n  return computed({\n    get() {\n      return _value.value = clamp(_value.value, resolveUnref(min), resolveUnref(max));\n    },\n    set(value2) {\n      _value.value = clamp(value2, resolveUnref(min), resolveUnref(max));\n    }\n  });\n}\n\nfunction useOffsetPagination(options) {\n  const {\n    total = Infinity,\n    pageSize = 10,\n    page = 1,\n    onPageChange = noop,\n    onPageSizeChange = noop,\n    onPageCountChange = noop\n  } = options;\n  const currentPageSize = useClamp(pageSize, 1, Infinity);\n  const pageCount = computed(() => Math.max(1, Math.ceil(unref(total) / unref(currentPageSize))));\n  const currentPage = useClamp(page, 1, pageCount);\n  const isFirstPage = computed(() => currentPage.value === 1);\n  const isLastPage = computed(() => currentPage.value === pageCount.value);\n  if (isRef(page))\n    syncRef(page, currentPage);\n  if (isRef(pageSize))\n    syncRef(pageSize, currentPageSize);\n  function prev() {\n    currentPage.value--;\n  }\n  function next() {\n    currentPage.value++;\n  }\n  const returnValue = {\n    currentPage,\n    currentPageSize,\n    pageCount,\n    isFirstPage,\n    isLastPage,\n    prev,\n    next\n  };\n  watch(currentPage, () => {\n    onPageChange(reactive(returnValue));\n  });\n  watch(currentPageSize, () => {\n    onPageSizeChange(reactive(returnValue));\n  });\n  watch(pageCount, () => {\n    onPageCountChange(reactive(returnValue));\n  });\n  return returnValue;\n}\n\nfunction useOnline(options = {}) {\n  const { isOnline } = useNetwork(options);\n  return isOnline;\n}\n\nfunction usePageLeave(options = {}) {\n  const { window = defaultWindow } = options;\n  const isLeft = ref(false);\n  const handler = (event) => {\n    if (!window)\n      return;\n    event = event || window.event;\n    const from = event.relatedTarget || event.toElement;\n    isLeft.value = !from;\n  };\n  if (window) {\n    useEventListener(window, \"mouseout\", handler, { passive: true });\n    useEventListener(window.document, \"mouseleave\", handler, { passive: true });\n    useEventListener(window.document, \"mouseenter\", handler, { passive: true });\n  }\n  return isLeft;\n}\n\nfunction useParallax(target, options = {}) {\n  const {\n    deviceOrientationTiltAdjust = (i) => i,\n    deviceOrientationRollAdjust = (i) => i,\n    mouseTiltAdjust = (i) => i,\n    mouseRollAdjust = (i) => i,\n    window = defaultWindow\n  } = options;\n  const orientation = reactive(useDeviceOrientation({ window }));\n  const {\n    elementX: x,\n    elementY: y,\n    elementWidth: width,\n    elementHeight: height\n  } = useMouseInElement(target, { handleOutside: false, window });\n  const source = computed(() => {\n    if (orientation.isSupported && (orientation.alpha != null && orientation.alpha !== 0 || orientation.gamma != null && orientation.gamma !== 0))\n      return \"deviceOrientation\";\n    return \"mouse\";\n  });\n  const roll = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      const value = -orientation.beta / 90;\n      return deviceOrientationRollAdjust(value);\n    } else {\n      const value = -(y.value - height.value / 2) / height.value;\n      return mouseRollAdjust(value);\n    }\n  });\n  const tilt = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      const value = orientation.gamma / 90;\n      return deviceOrientationTiltAdjust(value);\n    } else {\n      const value = (x.value - width.value / 2) / width.value;\n      return mouseTiltAdjust(value);\n    }\n  });\n  return { roll, tilt, source };\n}\n\nvar __defProp$6 = Object.defineProperty;\nvar __defProps$1 = Object.defineProperties;\nvar __getOwnPropDescs$1 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$6 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$6 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$6 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$6 = (obj, key, value) => key in obj ? __defProp$6(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$6 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$6.call(b, prop))\n      __defNormalProp$6(a, prop, b[prop]);\n  if (__getOwnPropSymbols$6)\n    for (var prop of __getOwnPropSymbols$6(b)) {\n      if (__propIsEnum$6.call(b, prop))\n        __defNormalProp$6(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps$1 = (a, b) => __defProps$1(a, __getOwnPropDescs$1(b));\nconst defaultState = {\n  x: 0,\n  y: 0,\n  pointerId: 0,\n  pressure: 0,\n  tiltX: 0,\n  tiltY: 0,\n  width: 0,\n  height: 0,\n  twist: 0,\n  pointerType: null\n};\nconst keys = /* @__PURE__ */ Object.keys(defaultState);\nfunction usePointer(options = {}) {\n  const {\n    target = defaultWindow\n  } = options;\n  const isInside = ref(false);\n  const state = ref(options.initialValue || {});\n  Object.assign(state.value, defaultState, state.value);\n  const handler = (event) => {\n    isInside.value = true;\n    if (options.pointerTypes && !options.pointerTypes.includes(event.pointerType))\n      return;\n    state.value = objectPick(event, keys, false);\n  };\n  if (target) {\n    useEventListener(target, \"pointerdown\", handler, { passive: true });\n    useEventListener(target, \"pointermove\", handler, { passive: true });\n    useEventListener(target, \"pointerleave\", () => isInside.value = false, { passive: true });\n  }\n  return __spreadProps$1(__spreadValues$6({}, toRefs(state)), {\n    isInside\n  });\n}\n\nfunction usePointerLock(target, options = {}) {\n  const { document = defaultDocument, pointerLockOptions } = options;\n  const isSupported = useSupported(() => document && \"pointerLockElement\" in document);\n  const element = ref();\n  const triggerElement = ref();\n  let targetElement;\n  if (isSupported.value) {\n    useEventListener(document, \"pointerlockchange\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        element.value = document.pointerLockElement;\n        if (!element.value)\n          targetElement = triggerElement.value = null;\n      }\n    });\n    useEventListener(document, \"pointerlockerror\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        const action = document.pointerLockElement ? \"release\" : \"acquire\";\n        throw new Error(`Failed to ${action} pointer lock.`);\n      }\n    });\n  }\n  async function lock(e, options2) {\n    var _a;\n    if (!isSupported.value)\n      throw new Error(\"Pointer Lock API is not supported by your browser.\");\n    triggerElement.value = e instanceof Event ? e.currentTarget : null;\n    targetElement = e instanceof Event ? (_a = unrefElement(target)) != null ? _a : triggerElement.value : unrefElement(e);\n    if (!targetElement)\n      throw new Error(\"Target element undefined.\");\n    targetElement.requestPointerLock(options2 != null ? options2 : pointerLockOptions);\n    return await until(element).toBe(targetElement);\n  }\n  async function unlock() {\n    if (!element.value)\n      return false;\n    document.exitPointerLock();\n    await until(element).toBeNull();\n    return true;\n  }\n  return {\n    isSupported,\n    element,\n    triggerElement,\n    lock,\n    unlock\n  };\n}\n\nvar SwipeDirection;\n(function(SwipeDirection2) {\n  SwipeDirection2[\"UP\"] = \"UP\";\n  SwipeDirection2[\"RIGHT\"] = \"RIGHT\";\n  SwipeDirection2[\"DOWN\"] = \"DOWN\";\n  SwipeDirection2[\"LEFT\"] = \"LEFT\";\n  SwipeDirection2[\"NONE\"] = \"NONE\";\n})(SwipeDirection || (SwipeDirection = {}));\nfunction useSwipe(target, options = {}) {\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    passive = true,\n    window = defaultWindow\n  } = options;\n  const coordsStart = reactive({ x: 0, y: 0 });\n  const coordsEnd = reactive({ x: 0, y: 0 });\n  const diffX = computed(() => coordsStart.x - coordsEnd.x);\n  const diffY = computed(() => coordsStart.y - coordsEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(diffX.value), abs(diffY.value)) >= threshold);\n  const isSwiping = ref(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return SwipeDirection.NONE;\n    if (abs(diffX.value) > abs(diffY.value)) {\n      return diffX.value > 0 ? SwipeDirection.LEFT : SwipeDirection.RIGHT;\n    } else {\n      return diffY.value > 0 ? SwipeDirection.UP : SwipeDirection.DOWN;\n    }\n  });\n  const getTouchEventCoords = (e) => [e.touches[0].clientX, e.touches[0].clientY];\n  const updateCoordsStart = (x, y) => {\n    coordsStart.x = x;\n    coordsStart.y = y;\n  };\n  const updateCoordsEnd = (x, y) => {\n    coordsEnd.x = x;\n    coordsEnd.y = y;\n  };\n  let listenerOptions;\n  const isPassiveEventSupported = checkPassiveEventSupport(window == null ? void 0 : window.document);\n  if (!passive)\n    listenerOptions = isPassiveEventSupported ? { passive: false, capture: true } : { capture: true };\n  else\n    listenerOptions = isPassiveEventSupported ? { passive: true } : { capture: false };\n  const onTouchEnd = (e) => {\n    if (isSwiping.value)\n      onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n    isSwiping.value = false;\n  };\n  const stops = [\n    useEventListener(target, \"touchstart\", (e) => {\n      if (listenerOptions.capture && !listenerOptions.passive)\n        e.preventDefault();\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsStart(x, y);\n      updateCoordsEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchmove\", (e) => {\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchend\", onTouchEnd, listenerOptions),\n    useEventListener(target, \"touchcancel\", onTouchEnd, listenerOptions)\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isPassiveEventSupported,\n    isSwiping,\n    direction,\n    coordsStart,\n    coordsEnd,\n    lengthX: diffX,\n    lengthY: diffY,\n    stop\n  };\n}\nfunction checkPassiveEventSupport(document) {\n  if (!document)\n    return false;\n  let supportsPassive = false;\n  const optionsBlock = {\n    get passive() {\n      supportsPassive = true;\n      return false;\n    }\n  };\n  document.addEventListener(\"x\", noop, optionsBlock);\n  document.removeEventListener(\"x\", noop);\n  return supportsPassive;\n}\n\nfunction usePointerSwipe(target, options = {}) {\n  const targetRef = resolveRef(target);\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart\n  } = options;\n  const posStart = reactive({ x: 0, y: 0 });\n  const updatePosStart = (x, y) => {\n    posStart.x = x;\n    posStart.y = y;\n  };\n  const posEnd = reactive({ x: 0, y: 0 });\n  const updatePosEnd = (x, y) => {\n    posEnd.x = x;\n    posEnd.y = y;\n  };\n  const distanceX = computed(() => posStart.x - posEnd.x);\n  const distanceY = computed(() => posStart.y - posEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(distanceX.value), abs(distanceY.value)) >= threshold);\n  const isSwiping = ref(false);\n  const isPointerDown = ref(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return SwipeDirection.NONE;\n    if (abs(distanceX.value) > abs(distanceY.value)) {\n      return distanceX.value > 0 ? SwipeDirection.LEFT : SwipeDirection.RIGHT;\n    } else {\n      return distanceY.value > 0 ? SwipeDirection.UP : SwipeDirection.DOWN;\n    }\n  });\n  const eventIsAllowed = (e) => {\n    var _a, _b, _c;\n    const isReleasingButton = e.buttons === 0;\n    const isPrimaryButton = e.buttons === 1;\n    return (_c = (_b = (_a = options.pointerTypes) == null ? void 0 : _a.includes(e.pointerType)) != null ? _b : isReleasingButton || isPrimaryButton) != null ? _c : true;\n  };\n  const stops = [\n    useEventListener(target, \"pointerdown\", (e) => {\n      var _a, _b;\n      if (!eventIsAllowed(e))\n        return;\n      isPointerDown.value = true;\n      (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"none\");\n      const eventTarget = e.target;\n      eventTarget == null ? void 0 : eventTarget.setPointerCapture(e.pointerId);\n      const { clientX: x, clientY: y } = e;\n      updatePosStart(x, y);\n      updatePosEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }),\n    useEventListener(target, \"pointermove\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (!isPointerDown.value)\n        return;\n      const { clientX: x, clientY: y } = e;\n      updatePosEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }),\n    useEventListener(target, \"pointerup\", (e) => {\n      var _a, _b;\n      if (!eventIsAllowed(e))\n        return;\n      if (isSwiping.value)\n        onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n      isPointerDown.value = false;\n      isSwiping.value = false;\n      (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"initial\");\n    })\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping: readonly(isSwiping),\n    direction: readonly(direction),\n    posStart: readonly(posStart),\n    posEnd: readonly(posEnd),\n    distanceX,\n    distanceY,\n    stop\n  };\n}\n\nfunction usePreferredColorScheme(options) {\n  const isLight = useMediaQuery(\"(prefers-color-scheme: light)\", options);\n  const isDark = useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n  return computed(() => {\n    if (isDark.value)\n      return \"dark\";\n    if (isLight.value)\n      return \"light\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredContrast(options) {\n  const isMore = useMediaQuery(\"(prefers-contrast: more)\", options);\n  const isLess = useMediaQuery(\"(prefers-contrast: less)\", options);\n  const isCustom = useMediaQuery(\"(prefers-contrast: custom)\", options);\n  return computed(() => {\n    if (isMore.value)\n      return \"more\";\n    if (isLess.value)\n      return \"less\";\n    if (isCustom.value)\n      return \"custom\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredLanguages(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return ref([\"en\"]);\n  const navigator = window.navigator;\n  const value = ref(navigator.languages);\n  useEventListener(window, \"languagechange\", () => {\n    value.value = navigator.languages;\n  });\n  return value;\n}\n\nfunction usePreferredReducedMotion(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-motion: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePrevious(value, initialValue) {\n  const previous = shallowRef(initialValue);\n  watch(resolveRef(value), (_, oldValue) => {\n    previous.value = oldValue;\n  }, { flush: \"sync\" });\n  return readonly(previous);\n}\n\nconst useScreenOrientation = (options = {}) => {\n  const {\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"screen\" in window && \"orientation\" in window.screen);\n  const screenOrientation = isSupported.value ? window.screen.orientation : {};\n  const orientation = ref(screenOrientation.type);\n  const angle = ref(screenOrientation.angle || 0);\n  if (isSupported.value) {\n    useEventListener(window, \"orientationchange\", () => {\n      orientation.value = screenOrientation.type;\n      angle.value = screenOrientation.angle;\n    });\n  }\n  const lockOrientation = (type) => {\n    if (!isSupported.value)\n      return Promise.reject(new Error(\"Not supported\"));\n    return screenOrientation.lock(type);\n  };\n  const unlockOrientation = () => {\n    if (isSupported.value)\n      screenOrientation.unlock();\n  };\n  return {\n    isSupported,\n    orientation,\n    angle,\n    lockOrientation,\n    unlockOrientation\n  };\n};\n\nconst topVarName = \"--vueuse-safe-area-top\";\nconst rightVarName = \"--vueuse-safe-area-right\";\nconst bottomVarName = \"--vueuse-safe-area-bottom\";\nconst leftVarName = \"--vueuse-safe-area-left\";\nfunction useScreenSafeArea() {\n  const top = ref(\"\");\n  const right = ref(\"\");\n  const bottom = ref(\"\");\n  const left = ref(\"\");\n  if (isClient) {\n    const topCssVar = useCssVar(topVarName);\n    const rightCssVar = useCssVar(rightVarName);\n    const bottomCssVar = useCssVar(bottomVarName);\n    const leftCssVar = useCssVar(leftVarName);\n    topCssVar.value = \"env(safe-area-inset-top, 0px)\";\n    rightCssVar.value = \"env(safe-area-inset-right, 0px)\";\n    bottomCssVar.value = \"env(safe-area-inset-bottom, 0px)\";\n    leftCssVar.value = \"env(safe-area-inset-left, 0px)\";\n    update();\n    useEventListener(\"resize\", useDebounceFn(update));\n  }\n  function update() {\n    top.value = getValue(topVarName);\n    right.value = getValue(rightVarName);\n    bottom.value = getValue(bottomVarName);\n    left.value = getValue(leftVarName);\n  }\n  return {\n    top,\n    right,\n    bottom,\n    left,\n    update\n  };\n}\nfunction getValue(position) {\n  return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nfunction useScriptTag(src, onLoaded = noop, options = {}) {\n  const {\n    immediate = true,\n    manual = false,\n    type = \"text/javascript\",\n    async = true,\n    crossOrigin,\n    referrerPolicy,\n    noModule,\n    defer,\n    document = defaultDocument,\n    attrs = {}\n  } = options;\n  const scriptTag = ref(null);\n  let _promise = null;\n  const loadScript = (waitForScriptLoad) => new Promise((resolve, reject) => {\n    const resolveWithElement = (el2) => {\n      scriptTag.value = el2;\n      resolve(el2);\n      return el2;\n    };\n    if (!document) {\n      resolve(false);\n      return;\n    }\n    let shouldAppend = false;\n    let el = document.querySelector(`script[src=\"${resolveUnref(src)}\"]`);\n    if (!el) {\n      el = document.createElement(\"script\");\n      el.type = type;\n      el.async = async;\n      el.src = resolveUnref(src);\n      if (defer)\n        el.defer = defer;\n      if (crossOrigin)\n        el.crossOrigin = crossOrigin;\n      if (noModule)\n        el.noModule = noModule;\n      if (referrerPolicy)\n        el.referrerPolicy = referrerPolicy;\n      Object.entries(attrs).forEach(([name, value]) => el == null ? void 0 : el.setAttribute(name, value));\n      shouldAppend = true;\n    } else if (el.hasAttribute(\"data-loaded\")) {\n      resolveWithElement(el);\n    }\n    el.addEventListener(\"error\", (event) => reject(event));\n    el.addEventListener(\"abort\", (event) => reject(event));\n    el.addEventListener(\"load\", () => {\n      el.setAttribute(\"data-loaded\", \"true\");\n      onLoaded(el);\n      resolveWithElement(el);\n    });\n    if (shouldAppend)\n      el = document.head.appendChild(el);\n    if (!waitForScriptLoad)\n      resolveWithElement(el);\n  });\n  const load = (waitForScriptLoad = true) => {\n    if (!_promise)\n      _promise = loadScript(waitForScriptLoad);\n    return _promise;\n  };\n  const unload = () => {\n    if (!document)\n      return;\n    _promise = null;\n    if (scriptTag.value)\n      scriptTag.value = null;\n    const el = document.querySelector(`script[src=\"${resolveUnref(src)}\"]`);\n    if (el)\n      document.head.removeChild(el);\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnUnmounted(unload);\n  return { scriptTag, load, unload };\n}\n\nfunction checkOverflowScroll(ele) {\n  const style = window.getComputedStyle(ele);\n  if (style.overflowX === \"scroll\" || style.overflowY === \"scroll\" || style.overflowX === \"auto\" && ele.clientHeight < ele.scrollHeight || style.overflowY === \"auto\" && ele.clientWidth < ele.scrollWidth) {\n    return true;\n  } else {\n    const parent = ele.parentNode;\n    if (!parent || parent.tagName === \"BODY\")\n      return false;\n    return checkOverflowScroll(parent);\n  }\n}\nfunction preventDefault(rawEvent) {\n  const e = rawEvent || window.event;\n  const _target = e.target;\n  if (checkOverflowScroll(_target))\n    return false;\n  if (e.touches.length > 1)\n    return true;\n  if (e.preventDefault)\n    e.preventDefault();\n  return false;\n}\nfunction useScrollLock(element, initialState = false) {\n  const isLocked = ref(initialState);\n  let stopTouchMoveListener = null;\n  let initialOverflow;\n  watch(resolveRef(element), (el) => {\n    if (el) {\n      const ele = el;\n      initialOverflow = ele.style.overflow;\n      if (isLocked.value)\n        ele.style.overflow = \"hidden\";\n    }\n  }, {\n    immediate: true\n  });\n  const lock = () => {\n    const ele = resolveUnref(element);\n    if (!ele || isLocked.value)\n      return;\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(ele, \"touchmove\", (e) => {\n        preventDefault(e);\n      }, { passive: false });\n    }\n    ele.style.overflow = \"hidden\";\n    isLocked.value = true;\n  };\n  const unlock = () => {\n    const ele = resolveUnref(element);\n    if (!ele || !isLocked.value)\n      return;\n    isIOS && (stopTouchMoveListener == null ? void 0 : stopTouchMoveListener());\n    ele.style.overflow = initialOverflow;\n    isLocked.value = false;\n  };\n  tryOnScopeDispose(unlock);\n  return computed({\n    get() {\n      return isLocked.value;\n    },\n    set(v) {\n      if (v)\n        lock();\n      else\n        unlock();\n    }\n  });\n}\n\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.sessionStorage, options);\n}\n\nvar __defProp$5 = Object.defineProperty;\nvar __getOwnPropSymbols$5 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$5 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$5 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$5 = (obj, key, value) => key in obj ? __defProp$5(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$5 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$5.call(b, prop))\n      __defNormalProp$5(a, prop, b[prop]);\n  if (__getOwnPropSymbols$5)\n    for (var prop of __getOwnPropSymbols$5(b)) {\n      if (__propIsEnum$5.call(b, prop))\n        __defNormalProp$5(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useShare(shareOptions = {}, options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const _navigator = navigator;\n  const isSupported = useSupported(() => _navigator && \"canShare\" in _navigator);\n  const share = async (overrideOptions = {}) => {\n    if (isSupported.value) {\n      const data = __spreadValues$5(__spreadValues$5({}, resolveUnref(shareOptions)), resolveUnref(overrideOptions));\n      let granted = true;\n      if (data.files && _navigator.canShare)\n        granted = _navigator.canShare({ files: data.files });\n      if (granted)\n        return _navigator.share(data);\n    }\n  };\n  return {\n    isSupported,\n    share\n  };\n}\n\nconst defaultSortFn = (source, compareFn) => source.sort(compareFn);\nconst defaultCompare = (a, b) => a - b;\nfunction useSorted(...args) {\n  var _a, _b, _c, _d;\n  const [source] = args;\n  let compareFn = defaultCompare;\n  let options = {};\n  if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      options = args[1];\n      compareFn = (_a = options.compareFn) != null ? _a : defaultCompare;\n    } else {\n      compareFn = (_b = args[1]) != null ? _b : defaultCompare;\n    }\n  } else if (args.length > 2) {\n    compareFn = (_c = args[1]) != null ? _c : defaultCompare;\n    options = (_d = args[2]) != null ? _d : {};\n  }\n  const {\n    dirty = false,\n    sortFn = defaultSortFn\n  } = options;\n  if (!dirty)\n    return computed(() => sortFn([...unref(source)], compareFn));\n  watchEffect(() => {\n    const result = sortFn(unref(source), compareFn);\n    if (isRef(source))\n      source.value = result;\n    else\n      source.splice(0, source.length, ...result);\n  });\n  return source;\n}\n\nfunction useSpeechRecognition(options = {}) {\n  const {\n    interimResults = true,\n    continuous = true,\n    window = defaultWindow\n  } = options;\n  const lang = resolveRef(options.lang || \"en-US\");\n  const isListening = ref(false);\n  const isFinal = ref(false);\n  const result = ref(\"\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isListening.value) => {\n    isListening.value = value;\n  };\n  const start = () => {\n    isListening.value = true;\n  };\n  const stop = () => {\n    isListening.value = false;\n  };\n  const SpeechRecognition = window && (window.SpeechRecognition || window.webkitSpeechRecognition);\n  const isSupported = useSupported(() => SpeechRecognition);\n  let recognition;\n  if (isSupported.value) {\n    recognition = new SpeechRecognition();\n    recognition.continuous = continuous;\n    recognition.interimResults = interimResults;\n    recognition.lang = unref(lang);\n    recognition.onstart = () => {\n      isFinal.value = false;\n    };\n    watch(lang, (lang2) => {\n      if (recognition && !isListening.value)\n        recognition.lang = lang2;\n    });\n    recognition.onresult = (event) => {\n      const transcript = Array.from(event.results).map((result2) => {\n        isFinal.value = result2.isFinal;\n        return result2[0];\n      }).map((result2) => result2.transcript).join(\"\");\n      result.value = transcript;\n      error.value = void 0;\n    };\n    recognition.onerror = (event) => {\n      error.value = event;\n    };\n    recognition.onend = () => {\n      isListening.value = false;\n      recognition.lang = unref(lang);\n    };\n    watch(isListening, () => {\n      if (isListening.value)\n        recognition.start();\n      else\n        recognition.stop();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isListening.value = false;\n  });\n  return {\n    isSupported,\n    isListening,\n    isFinal,\n    recognition,\n    result,\n    error,\n    toggle,\n    start,\n    stop\n  };\n}\n\nfunction useSpeechSynthesis(text, options = {}) {\n  const {\n    pitch = 1,\n    rate = 1,\n    volume = 1,\n    window = defaultWindow\n  } = options;\n  const synth = window && window.speechSynthesis;\n  const isSupported = useSupported(() => synth);\n  const isPlaying = ref(false);\n  const status = ref(\"init\");\n  const spokenText = resolveRef(text || \"\");\n  const lang = resolveRef(options.lang || \"en-US\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isPlaying.value) => {\n    isPlaying.value = value;\n  };\n  const bindEventsForUtterance = (utterance2) => {\n    utterance2.lang = unref(lang);\n    utterance2.voice = unref(options.voice) || null;\n    utterance2.pitch = pitch;\n    utterance2.rate = rate;\n    utterance2.volume = volume;\n    utterance2.onstart = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onpause = () => {\n      isPlaying.value = false;\n      status.value = \"pause\";\n    };\n    utterance2.onresume = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onend = () => {\n      isPlaying.value = false;\n      status.value = \"end\";\n    };\n    utterance2.onerror = (event) => {\n      error.value = event;\n    };\n  };\n  const utterance = computed(() => {\n    isPlaying.value = false;\n    status.value = \"init\";\n    const newUtterance = new SpeechSynthesisUtterance(spokenText.value);\n    bindEventsForUtterance(newUtterance);\n    return newUtterance;\n  });\n  const speak = () => {\n    synth.cancel();\n    utterance && synth.speak(utterance.value);\n  };\n  const stop = () => {\n    synth.cancel();\n    isPlaying.value = false;\n  };\n  if (isSupported.value) {\n    bindEventsForUtterance(utterance.value);\n    watch(lang, (lang2) => {\n      if (utterance.value && !isPlaying.value)\n        utterance.value.lang = lang2;\n    });\n    if (options.voice) {\n      watch(options.voice, () => {\n        synth.cancel();\n      });\n    }\n    watch(isPlaying, () => {\n      if (isPlaying.value)\n        synth.resume();\n      else\n        synth.pause();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isPlaying.value = false;\n  });\n  return {\n    isSupported,\n    isPlaying,\n    status,\n    utterance,\n    error,\n    stop,\n    toggle,\n    speak\n  };\n}\n\nfunction useStepper(steps, initialStep) {\n  const stepsRef = ref(steps);\n  const stepNames = computed(() => Array.isArray(stepsRef.value) ? stepsRef.value : Object.keys(stepsRef.value));\n  const index = ref(stepNames.value.indexOf(initialStep != null ? initialStep : stepNames.value[0]));\n  const current = computed(() => at(index.value));\n  const isFirst = computed(() => index.value === 0);\n  const isLast = computed(() => index.value === stepNames.value.length - 1);\n  const next = computed(() => stepNames.value[index.value + 1]);\n  const previous = computed(() => stepNames.value[index.value - 1]);\n  function at(index2) {\n    if (Array.isArray(stepsRef.value))\n      return stepsRef.value[index2];\n    return stepsRef.value[stepNames.value[index2]];\n  }\n  function get(step) {\n    if (!stepNames.value.includes(step))\n      return;\n    return at(stepNames.value.indexOf(step));\n  }\n  function goTo(step) {\n    if (stepNames.value.includes(step))\n      index.value = stepNames.value.indexOf(step);\n  }\n  function goToNext() {\n    if (isLast.value)\n      return;\n    index.value++;\n  }\n  function goToPrevious() {\n    if (isFirst.value)\n      return;\n    index.value--;\n  }\n  function goBackTo(step) {\n    if (isAfter(step))\n      goTo(step);\n  }\n  function isNext(step) {\n    return stepNames.value.indexOf(step) === index.value + 1;\n  }\n  function isPrevious(step) {\n    return stepNames.value.indexOf(step) === index.value - 1;\n  }\n  function isCurrent(step) {\n    return stepNames.value.indexOf(step) === index.value;\n  }\n  function isBefore(step) {\n    return index.value < stepNames.value.indexOf(step);\n  }\n  function isAfter(step) {\n    return index.value > stepNames.value.indexOf(step);\n  }\n  return {\n    steps: stepsRef,\n    stepNames,\n    index,\n    current,\n    next,\n    previous,\n    isFirst,\n    isLast,\n    at,\n    get,\n    goTo,\n    goToNext,\n    goToPrevious,\n    goBackTo,\n    isNext,\n    isPrevious,\n    isCurrent,\n    isBefore,\n    isAfter\n  };\n}\n\nvar __defProp$4 = Object.defineProperty;\nvar __getOwnPropSymbols$4 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$4 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$4 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$4 = (obj, key, value) => key in obj ? __defProp$4(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$4 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$4.call(b, prop))\n      __defNormalProp$4(a, prop, b[prop]);\n  if (__getOwnPropSymbols$4)\n    for (var prop of __getOwnPropSymbols$4(b)) {\n      if (__propIsEnum$4.call(b, prop))\n        __defNormalProp$4(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useStorageAsync(key, initialValue, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const rawInit = resolveUnref(initialValue);\n  const type = guessSerializerType(rawInit);\n  const data = (shallow ? shallowRef : ref)(initialValue);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  async function read(event) {\n    if (!storage || event && event.key !== key)\n      return;\n    try {\n      const rawValue = event ? event.newValue : await storage.getItem(key);\n      if (rawValue == null) {\n        data.value = rawInit;\n        if (writeDefaults && rawInit !== null)\n          await storage.setItem(key, await serializer.write(rawInit));\n      } else if (mergeDefaults) {\n        const value = await serializer.read(rawValue);\n        if (isFunction(mergeDefaults))\n          data.value = mergeDefaults(value, rawInit);\n        else if (type === \"object\" && !Array.isArray(value))\n          data.value = __spreadValues$4(__spreadValues$4({}, rawInit), value);\n        else\n          data.value = value;\n      } else {\n        data.value = await serializer.read(rawValue);\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  read();\n  if (window && listenToStorageChanges)\n    useEventListener(window, \"storage\", (e) => setTimeout(() => read(e), 0));\n  if (storage) {\n    watchWithFilter(data, async () => {\n      try {\n        if (data.value == null)\n          await storage.removeItem(key);\n        else\n          await storage.setItem(key, await serializer.write(data.value));\n      } catch (e) {\n        onError(e);\n      }\n    }, {\n      flush,\n      deep,\n      eventFilter\n    });\n  }\n  return data;\n}\n\nlet _id = 0;\nfunction useStyleTag(css, options = {}) {\n  const isLoaded = ref(false);\n  const {\n    document = defaultDocument,\n    immediate = true,\n    manual = false,\n    id = `vueuse_styletag_${++_id}`\n  } = options;\n  const cssRef = ref(css);\n  let stop = () => {\n  };\n  const load = () => {\n    if (!document)\n      return;\n    const el = document.getElementById(id) || document.createElement(\"style\");\n    if (!el.isConnected) {\n      el.type = \"text/css\";\n      el.id = id;\n      if (options.media)\n        el.media = options.media;\n      document.head.appendChild(el);\n    }\n    if (isLoaded.value)\n      return;\n    stop = watch(cssRef, (value) => {\n      el.textContent = value;\n    }, { immediate: true });\n    isLoaded.value = true;\n  };\n  const unload = () => {\n    if (!document || !isLoaded.value)\n      return;\n    stop();\n    document.head.removeChild(document.getElementById(id));\n    isLoaded.value = false;\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnScopeDispose(unload);\n  return {\n    id,\n    css: cssRef,\n    unload,\n    load,\n    isLoaded: readonly(isLoaded)\n  };\n}\n\nfunction useTemplateRefsList() {\n  const refs = ref([]);\n  refs.value.set = (el) => {\n    if (el)\n      refs.value.push(el);\n  };\n  onBeforeUpdate(() => {\n    refs.value.length = 0;\n  });\n  return refs;\n}\n\nfunction useTextDirection(options = {}) {\n  const {\n    document = defaultDocument,\n    selector = \"html\",\n    observe = false,\n    initialValue = \"ltr\"\n  } = options;\n  function getValue() {\n    var _a, _b;\n    return (_b = (_a = document == null ? void 0 : document.querySelector(selector)) == null ? void 0 : _a.getAttribute(\"dir\")) != null ? _b : initialValue;\n  }\n  const dir = ref(getValue());\n  tryOnMounted(() => dir.value = getValue());\n  if (observe && document) {\n    useMutationObserver(document.querySelector(selector), () => dir.value = getValue(), { attributes: true });\n  }\n  return computed({\n    get() {\n      return dir.value;\n    },\n    set(v) {\n      var _a, _b;\n      dir.value = v;\n      if (!document)\n        return;\n      if (dir.value)\n        (_a = document.querySelector(selector)) == null ? void 0 : _a.setAttribute(\"dir\", dir.value);\n      else\n        (_b = document.querySelector(selector)) == null ? void 0 : _b.removeAttribute(\"dir\");\n    }\n  });\n}\n\nfunction getRangesFromSelection(selection) {\n  var _a;\n  const rangeCount = (_a = selection.rangeCount) != null ? _a : 0;\n  const ranges = new Array(rangeCount);\n  for (let i = 0; i < rangeCount; i++) {\n    const range = selection.getRangeAt(i);\n    ranges[i] = range;\n  }\n  return ranges;\n}\nfunction useTextSelection(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const selection = ref(null);\n  const text = computed(() => {\n    var _a, _b;\n    return (_b = (_a = selection.value) == null ? void 0 : _a.toString()) != null ? _b : \"\";\n  });\n  const ranges = computed(() => selection.value ? getRangesFromSelection(selection.value) : []);\n  const rects = computed(() => ranges.value.map((range) => range.getBoundingClientRect()));\n  function onSelectionChange() {\n    selection.value = null;\n    if (window)\n      selection.value = window.getSelection();\n  }\n  if (window)\n    useEventListener(window.document, \"selectionchange\", onSelectionChange);\n  return {\n    text,\n    rects,\n    ranges,\n    selection\n  };\n}\n\nfunction useTextareaAutosize(options) {\n  const textarea = ref(options == null ? void 0 : options.element);\n  const input = ref(options == null ? void 0 : options.input);\n  function triggerResize() {\n    var _a, _b;\n    if (!textarea.value)\n      return;\n    textarea.value.style.height = \"1px\";\n    textarea.value.style.height = `${(_a = textarea.value) == null ? void 0 : _a.scrollHeight}px`;\n    (_b = options == null ? void 0 : options.onResize) == null ? void 0 : _b.call(options);\n  }\n  watch([input, textarea], triggerResize, { immediate: true });\n  useResizeObserver(textarea, () => triggerResize());\n  if (options == null ? void 0 : options.watch)\n    watch(options.watch, triggerResize, { immediate: true, deep: true });\n  return {\n    textarea,\n    input,\n    triggerResize\n  };\n}\n\nvar __defProp$3 = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$3 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$3 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$3 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$3 = (obj, key, value) => key in obj ? __defProp$3(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$3 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$3.call(b, prop))\n      __defNormalProp$3(a, prop, b[prop]);\n  if (__getOwnPropSymbols$3)\n    for (var prop of __getOwnPropSymbols$3(b)) {\n      if (__propIsEnum$3.call(b, prop))\n        __defNormalProp$3(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nfunction useThrottledRefHistory(source, options = {}) {\n  const { throttle = 200, trailing = true } = options;\n  const filter = throttleFilter(throttle, trailing);\n  const history = useRefHistory(source, __spreadProps(__spreadValues$3({}, options), { eventFilter: filter }));\n  return __spreadValues$3({}, history);\n}\n\nvar __defProp$2 = Object.defineProperty;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$2.call(b, prop))\n      __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(b)) {\n      if (__propIsEnum$2.call(b, prop))\n        __defNormalProp$2(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$2.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$2.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nconst DEFAULT_UNITS = [\n  { max: 6e4, value: 1e3, name: \"second\" },\n  { max: 276e4, value: 6e4, name: \"minute\" },\n  { max: 72e6, value: 36e5, name: \"hour\" },\n  { max: 5184e5, value: 864e5, name: \"day\" },\n  { max: 24192e5, value: 6048e5, name: \"week\" },\n  { max: 28512e6, value: 2592e6, name: \"month\" },\n  { max: Infinity, value: 31536e6, name: \"year\" }\n];\nconst DEFAULT_MESSAGES = {\n  justNow: \"just now\",\n  past: (n) => n.match(/\\d/) ? `${n} ago` : n,\n  future: (n) => n.match(/\\d/) ? `in ${n}` : n,\n  month: (n, past) => n === 1 ? past ? \"last month\" : \"next month\" : `${n} month${n > 1 ? \"s\" : \"\"}`,\n  year: (n, past) => n === 1 ? past ? \"last year\" : \"next year\" : `${n} year${n > 1 ? \"s\" : \"\"}`,\n  day: (n, past) => n === 1 ? past ? \"yesterday\" : \"tomorrow\" : `${n} day${n > 1 ? \"s\" : \"\"}`,\n  week: (n, past) => n === 1 ? past ? \"last week\" : \"next week\" : `${n} week${n > 1 ? \"s\" : \"\"}`,\n  hour: (n) => `${n} hour${n > 1 ? \"s\" : \"\"}`,\n  minute: (n) => `${n} minute${n > 1 ? \"s\" : \"\"}`,\n  second: (n) => `${n} second${n > 1 ? \"s\" : \"\"}`,\n  invalid: \"\"\n};\nconst DEFAULT_FORMATTER = (date) => date.toISOString().slice(0, 10);\nfunction useTimeAgo(time, options = {}) {\n  const {\n    controls: exposeControls = false,\n    updateInterval = 3e4\n  } = options;\n  const _a = useNow({ interval: updateInterval, controls: true }), { now } = _a, controls = __objRest(_a, [\"now\"]);\n  const timeAgo = computed(() => formatTimeAgo(new Date(resolveUnref(time)), options, unref(now.value)));\n  if (exposeControls) {\n    return __spreadValues$2({\n      timeAgo\n    }, controls);\n  } else {\n    return timeAgo;\n  }\n}\nfunction formatTimeAgo(from, options = {}, now = Date.now()) {\n  var _a;\n  const {\n    max,\n    messages = DEFAULT_MESSAGES,\n    fullDateFormatter = DEFAULT_FORMATTER,\n    units = DEFAULT_UNITS,\n    showSecond = false,\n    rounding = \"round\"\n  } = options;\n  const roundFn = typeof rounding === \"number\" ? (n) => +n.toFixed(rounding) : Math[rounding];\n  const diff = +now - +from;\n  const absDiff = Math.abs(diff);\n  function getValue(diff2, unit) {\n    return roundFn(Math.abs(diff2) / unit.value);\n  }\n  function format(diff2, unit) {\n    const val = getValue(diff2, unit);\n    const past = diff2 > 0;\n    const str = applyFormat(unit.name, val, past);\n    return applyFormat(past ? \"past\" : \"future\", str, past);\n  }\n  function applyFormat(name, val, isPast) {\n    const formatter = messages[name];\n    if (typeof formatter === \"function\")\n      return formatter(val, isPast);\n    return formatter.replace(\"{0}\", val.toString());\n  }\n  if (absDiff < 6e4 && !showSecond)\n    return messages.justNow;\n  if (typeof max === \"number\" && absDiff > max)\n    return fullDateFormatter(new Date(from));\n  if (typeof max === \"string\") {\n    const unitMax = (_a = units.find((i) => i.name === max)) == null ? void 0 : _a.max;\n    if (unitMax && absDiff > unitMax)\n      return fullDateFormatter(new Date(from));\n  }\n  for (const [idx, unit] of units.entries()) {\n    const val = getValue(diff, unit);\n    if (val <= 0 && units[idx - 1])\n      return format(diff, units[idx - 1]);\n    if (absDiff < unit.max)\n      return format(diff, unit);\n  }\n  return messages.invalid;\n}\n\nfunction useTimeoutPoll(fn, interval, timeoutPollOptions) {\n  const { start } = useTimeoutFn(loop, interval);\n  const isActive = ref(false);\n  async function loop() {\n    if (!isActive.value)\n      return;\n    await fn();\n    start();\n  }\n  function resume() {\n    if (!isActive.value) {\n      isActive.value = true;\n      loop();\n    }\n  }\n  function pause() {\n    isActive.value = false;\n  }\n  if (timeoutPollOptions == null ? void 0 : timeoutPollOptions.immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction useTimestamp(options = {}) {\n  const {\n    controls: exposeControls = false,\n    offset = 0,\n    immediate = true,\n    interval = \"requestAnimationFrame\",\n    callback\n  } = options;\n  const ts = ref(timestamp() + offset);\n  const update = () => ts.value = timestamp() + offset;\n  const cb = callback ? () => {\n    update();\n    callback(ts.value);\n  } : update;\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  if (exposeControls) {\n    return __spreadValues$1({\n      timestamp: ts\n    }, controls);\n  } else {\n    return ts;\n  }\n}\n\nfunction useTitle(newTitle = null, options = {}) {\n  var _a, _b;\n  const {\n    document = defaultDocument\n  } = options;\n  const title = resolveRef((_a = newTitle != null ? newTitle : document == null ? void 0 : document.title) != null ? _a : null);\n  const isReadonly = newTitle && isFunction(newTitle);\n  function format(t) {\n    if (!(\"titleTemplate\" in options))\n      return t;\n    const template = options.titleTemplate || \"%s\";\n    return isFunction(template) ? template(t) : unref(template).replace(/%s/g, t);\n  }\n  watch(title, (t, o) => {\n    if (t !== o && document)\n      document.title = format(isString(t) ? t : \"\");\n  }, { immediate: true });\n  if (options.observe && !options.titleTemplate && document && !isReadonly) {\n    useMutationObserver((_b = document.head) == null ? void 0 : _b.querySelector(\"title\"), () => {\n      if (document && document.title !== title.value)\n        title.value = format(document.title);\n    }, { childList: true });\n  }\n  return title;\n}\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nconst _TransitionPresets = {\n  easeInSine: [0.12, 0, 0.39, 0],\n  easeOutSine: [0.61, 1, 0.88, 1],\n  easeInOutSine: [0.37, 0, 0.63, 1],\n  easeInQuad: [0.11, 0, 0.5, 0],\n  easeOutQuad: [0.5, 1, 0.89, 1],\n  easeInOutQuad: [0.45, 0, 0.55, 1],\n  easeInCubic: [0.32, 0, 0.67, 0],\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInOutCubic: [0.65, 0, 0.35, 1],\n  easeInQuart: [0.5, 0, 0.75, 0],\n  easeOutQuart: [0.25, 1, 0.5, 1],\n  easeInOutQuart: [0.76, 0, 0.24, 1],\n  easeInQuint: [0.64, 0, 0.78, 0],\n  easeOutQuint: [0.22, 1, 0.36, 1],\n  easeInOutQuint: [0.83, 0, 0.17, 1],\n  easeInExpo: [0.7, 0, 0.84, 0],\n  easeOutExpo: [0.16, 1, 0.3, 1],\n  easeInOutExpo: [0.87, 0, 0.13, 1],\n  easeInCirc: [0.55, 0, 1, 0.45],\n  easeOutCirc: [0, 0.55, 0.45, 1],\n  easeInOutCirc: [0.85, 0, 0.15, 1],\n  easeInBack: [0.36, 0, 0.66, -0.56],\n  easeOutBack: [0.34, 1.56, 0.64, 1],\n  easeInOutBack: [0.68, -0.6, 0.32, 1.6]\n};\nconst TransitionPresets = __spreadValues({\n  linear: identity\n}, _TransitionPresets);\nfunction createEasingFunction([p0, p1, p2, p3]) {\n  const a = (a1, a2) => 1 - 3 * a2 + 3 * a1;\n  const b = (a1, a2) => 3 * a2 - 6 * a1;\n  const c = (a1) => 3 * a1;\n  const calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\n  const getSlope = (t, a1, a2) => 3 * a(a1, a2) * t * t + 2 * b(a1, a2) * t + c(a1);\n  const getTforX = (x) => {\n    let aGuessT = x;\n    for (let i = 0; i < 4; ++i) {\n      const currentSlope = getSlope(aGuessT, p0, p2);\n      if (currentSlope === 0)\n        return aGuessT;\n      const currentX = calcBezier(aGuessT, p0, p2) - x;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  };\n  return (x) => p0 === p1 && p2 === p3 ? x : calcBezier(getTforX(x), p1, p3);\n}\nfunction useTransition(source, options = {}) {\n  const {\n    delay = 0,\n    disabled = false,\n    duration = 1e3,\n    onFinished = noop,\n    onStarted = noop,\n    transition = identity\n  } = options;\n  const currentTransition = computed(() => {\n    const t = unref(transition);\n    return isFunction(t) ? t : createEasingFunction(t);\n  });\n  const sourceValue = computed(() => {\n    const s = unref(source);\n    return isNumber(s) ? s : s.map(unref);\n  });\n  const sourceVector = computed(() => isNumber(sourceValue.value) ? [sourceValue.value] : sourceValue.value);\n  const outputVector = ref(sourceVector.value.slice(0));\n  let currentDuration;\n  let diffVector;\n  let endAt;\n  let startAt;\n  let startVector;\n  const { resume, pause } = useRafFn(() => {\n    const now = Date.now();\n    const progress = clamp(1 - (endAt - now) / currentDuration, 0, 1);\n    outputVector.value = startVector.map((val, i) => {\n      var _a;\n      return val + ((_a = diffVector[i]) != null ? _a : 0) * currentTransition.value(progress);\n    });\n    if (progress >= 1) {\n      pause();\n      onFinished();\n    }\n  }, { immediate: false });\n  const start = () => {\n    pause();\n    currentDuration = unref(duration);\n    diffVector = outputVector.value.map((n, i) => {\n      var _a, _b;\n      return ((_a = sourceVector.value[i]) != null ? _a : 0) - ((_b = outputVector.value[i]) != null ? _b : 0);\n    });\n    startVector = outputVector.value.slice(0);\n    startAt = Date.now();\n    endAt = startAt + currentDuration;\n    resume();\n    onStarted();\n  };\n  const timeout = useTimeoutFn(start, delay, { immediate: false });\n  watch(sourceVector, () => {\n    if (unref(disabled))\n      return;\n    if (unref(delay) <= 0)\n      start();\n    else\n      timeout.start();\n  }, { deep: true });\n  watch(() => unref(disabled), (v) => {\n    if (v) {\n      outputVector.value = sourceVector.value.slice(0);\n      pause();\n    }\n  });\n  return computed(() => {\n    const targetVector = unref(disabled) ? sourceVector : outputVector;\n    return isNumber(sourceValue.value) ? targetVector.value[0] : targetVector.value;\n  });\n}\n\nfunction useUrlSearchParams(mode = \"history\", options = {}) {\n  const {\n    initialValue = {},\n    removeNullishValues = true,\n    removeFalsyValues = false,\n    write: enableWrite = true,\n    window = defaultWindow\n  } = options;\n  if (!window)\n    return reactive(initialValue);\n  const state = reactive({});\n  function getRawParams() {\n    if (mode === \"history\") {\n      return window.location.search || \"\";\n    } else if (mode === \"hash\") {\n      const hash = window.location.hash || \"\";\n      const index = hash.indexOf(\"?\");\n      return index > 0 ? hash.slice(index) : \"\";\n    } else {\n      return (window.location.hash || \"\").replace(/^#/, \"\");\n    }\n  }\n  function constructQuery(params) {\n    const stringified = params.toString();\n    if (mode === \"history\")\n      return `${stringified ? `?${stringified}` : \"\"}${window.location.hash || \"\"}`;\n    if (mode === \"hash-params\")\n      return `${window.location.search || \"\"}${stringified ? `#${stringified}` : \"\"}`;\n    const hash = window.location.hash || \"#\";\n    const index = hash.indexOf(\"?\");\n    if (index > 0)\n      return `${hash.slice(0, index)}${stringified ? `?${stringified}` : \"\"}`;\n    return `${hash}${stringified ? `?${stringified}` : \"\"}`;\n  }\n  function read() {\n    return new URLSearchParams(getRawParams());\n  }\n  function updateState(params) {\n    const unusedKeys = new Set(Object.keys(state));\n    for (const key of params.keys()) {\n      const paramsForKey = params.getAll(key);\n      state[key] = paramsForKey.length > 1 ? paramsForKey : params.get(key) || \"\";\n      unusedKeys.delete(key);\n    }\n    Array.from(unusedKeys).forEach((key) => delete state[key]);\n  }\n  const { pause, resume } = pausableWatch(state, () => {\n    const params = new URLSearchParams(\"\");\n    Object.keys(state).forEach((key) => {\n      const mapEntry = state[key];\n      if (Array.isArray(mapEntry))\n        mapEntry.forEach((value) => params.append(key, value));\n      else if (removeNullishValues && mapEntry == null)\n        params.delete(key);\n      else if (removeFalsyValues && !mapEntry)\n        params.delete(key);\n      else\n        params.set(key, mapEntry);\n    });\n    write(params);\n  }, { deep: true });\n  function write(params, shouldUpdate) {\n    pause();\n    if (shouldUpdate)\n      updateState(params);\n    window.history.replaceState(window.history.state, window.document.title, window.location.pathname + constructQuery(params));\n    resume();\n  }\n  function onChanged() {\n    if (!enableWrite)\n      return;\n    write(read(), true);\n  }\n  useEventListener(window, \"popstate\", onChanged, false);\n  if (mode !== \"history\")\n    useEventListener(window, \"hashchange\", onChanged, false);\n  const initial = read();\n  if (initial.keys().next().value)\n    updateState(initial);\n  else\n    Object.assign(state, initialValue);\n  return state;\n}\n\nfunction useUserMedia(options = {}) {\n  var _a, _b;\n  const enabled = ref((_a = options.enabled) != null ? _a : false);\n  const autoSwitch = ref((_b = options.autoSwitch) != null ? _b : true);\n  const videoDeviceId = ref(options.videoDeviceId);\n  const audioDeviceId = ref(options.audioDeviceId);\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getUserMedia;\n  });\n  const stream = shallowRef();\n  function getDeviceOptions(device) {\n    if (device.value === \"none\" || device.value === false)\n      return false;\n    if (device.value == null)\n      return true;\n    return {\n      deviceId: device.value\n    };\n  }\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getUserMedia({\n      video: getDeviceOptions(videoDeviceId),\n      audio: getDeviceOptions(audioDeviceId)\n    });\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  async function restart() {\n    _stop();\n    return await start();\n  }\n  watch(enabled, (v) => {\n    if (v)\n      _start();\n    else\n      _stop();\n  }, { immediate: true });\n  watch([videoDeviceId, audioDeviceId], () => {\n    if (autoSwitch.value && stream.value)\n      restart();\n  }, { immediate: true });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    restart,\n    videoDeviceId,\n    audioDeviceId,\n    enabled,\n    autoSwitch\n  };\n}\n\nfunction useVModel(props, key, emit, options = {}) {\n  var _a, _b, _c, _d, _e;\n  const {\n    clone = false,\n    passive = false,\n    eventName,\n    deep = false,\n    defaultValue\n  } = options;\n  const vm = getCurrentInstance();\n  const _emit = emit || (vm == null ? void 0 : vm.emit) || ((_a = vm == null ? void 0 : vm.$emit) == null ? void 0 : _a.bind(vm)) || ((_c = (_b = vm == null ? void 0 : vm.proxy) == null ? void 0 : _b.$emit) == null ? void 0 : _c.bind(vm == null ? void 0 : vm.proxy));\n  let event = eventName;\n  if (!key) {\n    if (isVue2) {\n      const modelOptions = (_e = (_d = vm == null ? void 0 : vm.proxy) == null ? void 0 : _d.$options) == null ? void 0 : _e.model;\n      key = (modelOptions == null ? void 0 : modelOptions.value) || \"value\";\n      if (!eventName)\n        event = (modelOptions == null ? void 0 : modelOptions.event) || \"input\";\n    } else {\n      key = \"modelValue\";\n    }\n  }\n  event = eventName || event || `update:${key.toString()}`;\n  const cloneFn = (val) => !clone ? val : isFunction(clone) ? clone(val) : cloneFnJSON(val);\n  const getValue = () => isDef(props[key]) ? cloneFn(props[key]) : defaultValue;\n  if (passive) {\n    const initialValue = getValue();\n    const proxy = ref(initialValue);\n    watch(() => props[key], (v) => proxy.value = cloneFn(v));\n    watch(proxy, (v) => {\n      if (v !== props[key] || deep)\n        _emit(event, v);\n    }, { deep });\n    return proxy;\n  } else {\n    return computed({\n      get() {\n        return getValue();\n      },\n      set(value) {\n        _emit(event, value);\n      }\n    });\n  }\n}\n\nfunction useVModels(props, emit, options = {}) {\n  const ret = {};\n  for (const key in props)\n    ret[key] = useVModel(props, key, emit, options);\n  return ret;\n}\n\nfunction useVibrate(options) {\n  const {\n    pattern = [],\n    interval = 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => typeof navigator !== \"undefined\" && \"vibrate\" in navigator);\n  const patternRef = resolveRef(pattern);\n  let intervalControls;\n  const vibrate = (pattern2 = patternRef.value) => {\n    if (isSupported.value)\n      navigator.vibrate(pattern2);\n  };\n  const stop = () => {\n    if (isSupported.value)\n      navigator.vibrate(0);\n    intervalControls == null ? void 0 : intervalControls.pause();\n  };\n  if (interval > 0) {\n    intervalControls = useIntervalFn(vibrate, interval, {\n      immediate: false,\n      immediateCallback: false\n    });\n  }\n  return {\n    isSupported,\n    pattern,\n    intervalControls,\n    vibrate,\n    stop\n  };\n}\n\nfunction useVirtualList(list, options) {\n  const { containerStyle, wrapperProps, scrollTo, calculateRange, currentList, containerRef } = \"itemHeight\" in options ? useVerticalVirtualList(options, list) : useHorizontalVirtualList(options, list);\n  return {\n    list: currentList,\n    scrollTo,\n    containerProps: {\n      ref: containerRef,\n      onScroll: () => {\n        calculateRange();\n      },\n      style: containerStyle\n    },\n    wrapperProps\n  };\n}\nfunction useVirtualListResources(list) {\n  const containerRef = ref(null);\n  const size = useElementSize(containerRef);\n  const currentList = ref([]);\n  const source = shallowRef(list);\n  const state = ref({ start: 0, end: 10 });\n  return { state, source, currentList, size, containerRef };\n}\nfunction createGetViewCapacity(state, source, itemSize) {\n  return (containerSize) => {\n    if (typeof itemSize === \"number\")\n      return Math.ceil(containerSize / itemSize);\n    const { start = 0 } = state.value;\n    let sum = 0;\n    let capacity = 0;\n    for (let i = start; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      capacity = i;\n      if (sum > containerSize)\n        break;\n    }\n    return capacity - start;\n  };\n}\nfunction createGetOffset(source, itemSize) {\n  return (scrollDirection) => {\n    if (typeof itemSize === \"number\")\n      return Math.floor(scrollDirection / itemSize) + 1;\n    let sum = 0;\n    let offset = 0;\n    for (let i = 0; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      if (sum >= scrollDirection) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n}\nfunction createCalculateRange(type, overscan, getOffset, getViewCapacity, { containerRef, state, currentList, source }) {\n  return () => {\n    const element = containerRef.value;\n    if (element) {\n      const offset = getOffset(type === \"vertical\" ? element.scrollTop : element.scrollLeft);\n      const viewCapacity = getViewCapacity(type === \"vertical\" ? element.clientHeight : element.clientWidth);\n      const from = offset - overscan;\n      const to = offset + viewCapacity + overscan;\n      state.value = {\n        start: from < 0 ? 0 : from,\n        end: to > source.value.length ? source.value.length : to\n      };\n      currentList.value = source.value.slice(state.value.start, state.value.end).map((ele, index) => ({\n        data: ele,\n        index: index + state.value.start\n      }));\n    }\n  };\n}\nfunction createGetDistance(itemSize, source) {\n  return (index) => {\n    if (typeof itemSize === \"number\") {\n      const size2 = index * itemSize;\n      return size2;\n    }\n    const size = source.value.slice(0, index).reduce((sum, _, i) => sum + itemSize(i), 0);\n    return size;\n  };\n}\nfunction useWatchForSizes(size, list, calculateRange) {\n  watch([size.width, size.height, list], () => {\n    calculateRange();\n  });\n}\nfunction createComputedTotalSize(itemSize, source) {\n  return computed(() => {\n    if (typeof itemSize === \"number\")\n      return source.value.length * itemSize;\n    return source.value.reduce((sum, _, index) => sum + itemSize(index), 0);\n  });\n}\nconst scrollToDictionaryForElementScrollKey = {\n  horizontal: \"scrollLeft\",\n  vertical: \"scrollTop\"\n};\nfunction createScrollTo(type, calculateRange, getDistance, containerRef) {\n  return (index) => {\n    if (containerRef.value) {\n      containerRef.value[scrollToDictionaryForElementScrollKey[type]] = getDistance(index);\n      calculateRange();\n    }\n  };\n}\nfunction useHorizontalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowX: \"auto\" };\n  const { itemWidth, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemWidth);\n  const getOffset = createGetOffset(source, itemWidth);\n  const calculateRange = createCalculateRange(\"horizontal\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceLeft = createGetDistance(itemWidth, source);\n  const offsetLeft = computed(() => getDistanceLeft(state.value.start));\n  const totalWidth = createComputedTotalSize(itemWidth, source);\n  useWatchForSizes(size, list, calculateRange);\n  const scrollTo = createScrollTo(\"horizontal\", calculateRange, getDistanceLeft, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        height: \"100%\",\n        width: `${totalWidth.value - offsetLeft.value}px`,\n        marginLeft: `${offsetLeft.value}px`,\n        display: \"flex\"\n      }\n    };\n  });\n  return {\n    scrollTo,\n    calculateRange,\n    wrapperProps,\n    containerStyle,\n    currentList,\n    containerRef\n  };\n}\nfunction useVerticalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowY: \"auto\" };\n  const { itemHeight, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemHeight);\n  const getOffset = createGetOffset(source, itemHeight);\n  const calculateRange = createCalculateRange(\"vertical\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceTop = createGetDistance(itemHeight, source);\n  const offsetTop = computed(() => getDistanceTop(state.value.start));\n  const totalHeight = createComputedTotalSize(itemHeight, source);\n  useWatchForSizes(size, list, calculateRange);\n  const scrollTo = createScrollTo(\"vertical\", calculateRange, getDistanceTop, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        width: \"100%\",\n        height: `${totalHeight.value - offsetTop.value}px`,\n        marginTop: `${offsetTop.value}px`\n      }\n    };\n  });\n  return {\n    calculateRange,\n    scrollTo,\n    containerStyle,\n    wrapperProps,\n    currentList,\n    containerRef\n  };\n}\n\nconst useWakeLock = (options = {}) => {\n  const {\n    navigator = defaultNavigator,\n    document = defaultDocument\n  } = options;\n  let wakeLock;\n  const isSupported = useSupported(() => navigator && \"wakeLock\" in navigator);\n  const isActive = ref(false);\n  async function onVisibilityChange() {\n    if (!isSupported.value || !wakeLock)\n      return;\n    if (document && document.visibilityState === \"visible\")\n      wakeLock = await navigator.wakeLock.request(\"screen\");\n    isActive.value = !wakeLock.released;\n  }\n  if (document)\n    useEventListener(document, \"visibilitychange\", onVisibilityChange, { passive: true });\n  async function request(type) {\n    if (!isSupported.value)\n      return;\n    wakeLock = await navigator.wakeLock.request(type);\n    isActive.value = !wakeLock.released;\n  }\n  async function release() {\n    if (!isSupported.value || !wakeLock)\n      return;\n    await wakeLock.release();\n    isActive.value = !wakeLock.released;\n    wakeLock = null;\n  }\n  return {\n    isSupported,\n    isActive,\n    request,\n    release\n  };\n};\n\nconst useWebNotification = (defaultOptions = {}) => {\n  const {\n    window = defaultWindow\n  } = defaultOptions;\n  const isSupported = useSupported(() => !!window && \"Notification\" in window);\n  const notification = ref(null);\n  const requestPermission = async () => {\n    if (!isSupported.value)\n      return;\n    if (\"permission\" in Notification && Notification.permission !== \"denied\")\n      await Notification.requestPermission();\n  };\n  const onClick = createEventHook();\n  const onShow = createEventHook();\n  const onError = createEventHook();\n  const onClose = createEventHook();\n  const show = async (overrides) => {\n    if (!isSupported.value)\n      return;\n    await requestPermission();\n    const options = Object.assign({}, defaultOptions, overrides);\n    notification.value = new Notification(options.title || \"\", options);\n    notification.value.onclick = (event) => onClick.trigger(event);\n    notification.value.onshow = (event) => onShow.trigger(event);\n    notification.value.onerror = (event) => onError.trigger(event);\n    notification.value.onclose = (event) => onClose.trigger(event);\n    return notification.value;\n  };\n  const close = () => {\n    if (notification.value)\n      notification.value.close();\n    notification.value = null;\n  };\n  tryOnMounted(async () => {\n    if (isSupported.value)\n      await requestPermission();\n  });\n  tryOnScopeDispose(close);\n  if (isSupported.value && window) {\n    const document = window.document;\n    useEventListener(document, \"visibilitychange\", (e) => {\n      e.preventDefault();\n      if (document.visibilityState === \"visible\") {\n        close();\n      }\n    });\n  }\n  return {\n    isSupported,\n    notification,\n    show,\n    close,\n    onClick,\n    onShow,\n    onError,\n    onClose\n  };\n};\n\nconst DEFAULT_PING_MESSAGE = \"ping\";\nfunction resolveNestedOptions(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useWebSocket(url, options = {}) {\n  const {\n    onConnected,\n    onDisconnected,\n    onError,\n    onMessage,\n    immediate = true,\n    autoClose = true,\n    protocols = []\n  } = options;\n  const data = ref(null);\n  const status = ref(\"CLOSED\");\n  const wsRef = ref();\n  const urlRef = resolveRef(url);\n  let heartbeatPause;\n  let heartbeatResume;\n  let explicitlyClosed = false;\n  let retried = 0;\n  let bufferedData = [];\n  let pongTimeoutWait;\n  const close = (code = 1e3, reason) => {\n    if (!wsRef.value)\n      return;\n    explicitlyClosed = true;\n    heartbeatPause == null ? void 0 : heartbeatPause();\n    wsRef.value.close(code, reason);\n  };\n  const _sendBuffer = () => {\n    if (bufferedData.length && wsRef.value && status.value === \"OPEN\") {\n      for (const buffer of bufferedData)\n        wsRef.value.send(buffer);\n      bufferedData = [];\n    }\n  };\n  const resetHeartbeat = () => {\n    clearTimeout(pongTimeoutWait);\n    pongTimeoutWait = void 0;\n  };\n  const send = (data2, useBuffer = true) => {\n    if (!wsRef.value || status.value !== \"OPEN\") {\n      if (useBuffer)\n        bufferedData.push(data2);\n      return false;\n    }\n    _sendBuffer();\n    wsRef.value.send(data2);\n    return true;\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const ws = new WebSocket(urlRef.value, protocols);\n    wsRef.value = ws;\n    status.value = \"CONNECTING\";\n    ws.onopen = () => {\n      status.value = \"OPEN\";\n      onConnected == null ? void 0 : onConnected(ws);\n      heartbeatResume == null ? void 0 : heartbeatResume();\n      _sendBuffer();\n    };\n    ws.onclose = (ev) => {\n      status.value = \"CLOSED\";\n      wsRef.value = void 0;\n      onDisconnected == null ? void 0 : onDisconnected(ws, ev);\n      if (!explicitlyClosed && options.autoReconnect) {\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions(options.autoReconnect);\n        retried += 1;\n        if (typeof retries === \"number\" && (retries < 0 || retried < retries))\n          setTimeout(_init, delay);\n        else if (typeof retries === \"function\" && retries())\n          setTimeout(_init, delay);\n        else\n          onFailed == null ? void 0 : onFailed();\n      }\n    };\n    ws.onerror = (e) => {\n      onError == null ? void 0 : onError(ws, e);\n    };\n    ws.onmessage = (e) => {\n      if (options.heartbeat) {\n        resetHeartbeat();\n        const {\n          message = DEFAULT_PING_MESSAGE\n        } = resolveNestedOptions(options.heartbeat);\n        if (e.data === message)\n          return;\n      }\n      data.value = e.data;\n      onMessage == null ? void 0 : onMessage(ws, e);\n    };\n  };\n  if (options.heartbeat) {\n    const {\n      message = DEFAULT_PING_MESSAGE,\n      interval = 1e3,\n      pongTimeout = 1e3\n    } = resolveNestedOptions(options.heartbeat);\n    const { pause, resume } = useIntervalFn(() => {\n      send(message, false);\n      if (pongTimeoutWait != null)\n        return;\n      pongTimeoutWait = setTimeout(() => {\n        close();\n      }, pongTimeout);\n    }, interval, { immediate: false });\n    heartbeatPause = pause;\n    heartbeatResume = resume;\n  }\n  if (autoClose) {\n    useEventListener(window, \"beforeunload\", () => close());\n    tryOnScopeDispose(close);\n  }\n  const open = () => {\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    watch(urlRef, open, { immediate: true });\n  return {\n    data,\n    status,\n    close,\n    send,\n    open,\n    ws: wsRef\n  };\n}\n\nfunction useWebWorker(arg0, workerOptions, options) {\n  const {\n    window = defaultWindow\n  } = options != null ? options : {};\n  const data = ref(null);\n  const worker = shallowRef();\n  const post = function post2(val) {\n    if (!worker.value)\n      return;\n    worker.value.postMessage(val);\n  };\n  const terminate = function terminate2() {\n    if (!worker.value)\n      return;\n    worker.value.terminate();\n  };\n  if (window) {\n    if (isString(arg0))\n      worker.value = new Worker(arg0, workerOptions);\n    else if (isFunction(arg0))\n      worker.value = arg0();\n    else\n      worker.value = arg0;\n    worker.value.onmessage = (e) => {\n      data.value = e.data;\n    };\n    tryOnScopeDispose(() => {\n      if (worker.value)\n        worker.value.terminate();\n    });\n  }\n  return {\n    data,\n    post,\n    terminate,\n    worker\n  };\n}\n\nconst jobRunner = (userFunc) => (e) => {\n  const userFuncArgs = e.data[0];\n  return Promise.resolve(userFunc.apply(void 0, userFuncArgs)).then((result) => {\n    postMessage([\"SUCCESS\", result]);\n  }).catch((error) => {\n    postMessage([\"ERROR\", error]);\n  });\n};\n\nconst depsParser = (deps) => {\n  if (deps.length === 0)\n    return \"\";\n  const depsString = deps.map((dep) => `'${dep}'`).toString();\n  return `importScripts(${depsString})`;\n};\n\nconst createWorkerBlobUrl = (fn, deps) => {\n  const blobCode = `${depsParser(deps)}; onmessage=(${jobRunner})(${fn})`;\n  const blob = new Blob([blobCode], { type: \"text/javascript\" });\n  const url = URL.createObjectURL(blob);\n  return url;\n};\n\nconst useWebWorkerFn = (fn, options = {}) => {\n  const {\n    dependencies = [],\n    timeout,\n    window = defaultWindow\n  } = options;\n  const worker = ref();\n  const workerStatus = ref(\"PENDING\");\n  const promise = ref({});\n  const timeoutId = ref();\n  const workerTerminate = (status = \"PENDING\") => {\n    if (worker.value && worker.value._url && window) {\n      worker.value.terminate();\n      URL.revokeObjectURL(worker.value._url);\n      promise.value = {};\n      worker.value = void 0;\n      window.clearTimeout(timeoutId.value);\n      workerStatus.value = status;\n    }\n  };\n  workerTerminate();\n  tryOnScopeDispose(workerTerminate);\n  const generateWorker = () => {\n    const blobUrl = createWorkerBlobUrl(fn, dependencies);\n    const newWorker = new Worker(blobUrl);\n    newWorker._url = blobUrl;\n    newWorker.onmessage = (e) => {\n      const { resolve = () => {\n      }, reject = () => {\n      } } = promise.value;\n      const [status, result] = e.data;\n      switch (status) {\n        case \"SUCCESS\":\n          resolve(result);\n          workerTerminate(status);\n          break;\n        default:\n          reject(result);\n          workerTerminate(\"ERROR\");\n          break;\n      }\n    };\n    newWorker.onerror = (e) => {\n      const { reject = () => {\n      } } = promise.value;\n      reject(e);\n      workerTerminate(\"ERROR\");\n    };\n    if (timeout) {\n      timeoutId.value = setTimeout(() => workerTerminate(\"TIMEOUT_EXPIRED\"), timeout);\n    }\n    return newWorker;\n  };\n  const callWorker = (...fnArgs) => new Promise((resolve, reject) => {\n    promise.value = {\n      resolve,\n      reject\n    };\n    worker.value && worker.value.postMessage([[...fnArgs]]);\n    workerStatus.value = \"RUNNING\";\n  });\n  const workerFn = (...fnArgs) => {\n    if (workerStatus.value === \"RUNNING\") {\n      console.error(\"[useWebWorkerFn] You can only run one instance of the worker at a time.\");\n      return Promise.reject();\n    }\n    worker.value = generateWorker();\n    return callWorker(...fnArgs);\n  };\n  return {\n    workerFn,\n    workerStatus,\n    workerTerminate\n  };\n};\n\nfunction useWindowFocus({ window = defaultWindow } = {}) {\n  if (!window)\n    return ref(false);\n  const focused = ref(window.document.hasFocus());\n  useEventListener(window, \"blur\", () => {\n    focused.value = false;\n  });\n  useEventListener(window, \"focus\", () => {\n    focused.value = true;\n  });\n  return focused;\n}\n\nfunction useWindowScroll({ window = defaultWindow } = {}) {\n  if (!window) {\n    return {\n      x: ref(0),\n      y: ref(0)\n    };\n  }\n  const x = ref(window.scrollX);\n  const y = ref(window.scrollY);\n  useEventListener(window, \"scroll\", () => {\n    x.value = window.scrollX;\n    y.value = window.scrollY;\n  }, {\n    capture: false,\n    passive: true\n  });\n  return { x, y };\n}\n\nfunction useWindowSize(options = {}) {\n  const {\n    window = defaultWindow,\n    initialWidth = Infinity,\n    initialHeight = Infinity,\n    listenOrientation = true,\n    includeScrollbar = true\n  } = options;\n  const width = ref(initialWidth);\n  const height = ref(initialHeight);\n  const update = () => {\n    if (window) {\n      if (includeScrollbar) {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      } else {\n        width.value = window.document.documentElement.clientWidth;\n        height.value = window.document.documentElement.clientHeight;\n      }\n    }\n  };\n  update();\n  tryOnMounted(update);\n  useEventListener(\"resize\", update, { passive: true });\n  if (listenOrientation)\n    useEventListener(\"orientationchange\", update, { passive: true });\n  return { width, height };\n}\n\nexport { DefaultMagicKeysAliasMap, StorageSerializers, SwipeDirection, TransitionPresets, computedAsync as asyncComputed, breakpointsAntDesign, breakpointsBootstrapV5, breakpointsMasterCss, breakpointsQuasar, breakpointsSematic, breakpointsTailwind, breakpointsVuetify, cloneFnJSON, computedAsync, computedInject, createFetch, createUnrefFn, customStorageEventName, defaultDocument, defaultLocation, defaultNavigator, defaultWindow, formatTimeAgo, getSSRHandler, mapGamepadToXbox360Controller, onClickOutside, onKeyDown, onKeyPressed, onKeyStroke, onKeyUp, onLongPress, onStartTyping, setSSRHandler, templateRef, unrefElement, useActiveElement, useAsyncQueue, useAsyncState, useBase64, useBattery, useBluetooth, useBreakpoints, useBroadcastChannel, useBrowserLocation, useCached, useClipboard, useCloned, useColorMode, useConfirmDialog, useCssVar, useCurrentElement, useCycleList, useDark, useDebouncedRefHistory, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDisplayMedia, useDocumentVisibility, useDraggable, useDropZone, useElementBounding, useElementByPoint, useElementHover, useElementSize, useElementVisibility, useEventBus, useEventListener, useEventSource, useEyeDropper, useFavicon, useFetch, useFileDialog, useFileSystemAccess, useFocus, useFocusWithin, useFps, useFullscreen, useGamepad, useGeolocation, useIdle, useImage, useInfiniteScroll, useIntersectionObserver, useKeyModifier, useLocalStorage, useMagicKeys, useManualRefHistory, useMediaControls, useMediaQuery, useMemoize, useMemory, useMounted, useMouse, useMouseInElement, useMousePressed, useMutationObserver, useNavigatorLanguage, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, useParallax, usePermission, usePointer, usePointerLock, usePointerSwipe, usePreferredColorScheme, usePreferredContrast, usePreferredDark, usePreferredLanguages, usePreferredReducedMotion, usePrevious, useRafFn, useRefHistory, useResizeObserver, useScreenOrientation, useScreenSafeArea, useScriptTag, useScroll, useScrollLock, useSessionStorage, useShare, useSorted, useSpeechRecognition, useSpeechSynthesis, useStepper, useStorage, useStorageAsync, useStyleTag, useSupported, useSwipe, useTemplateRefsList, useTextDirection, useTextSelection, useTextareaAutosize, useThrottledRefHistory, useTimeAgo, useTimeoutPoll, useTimestamp, useTitle, useTransition, useUrlSearchParams, useUserMedia, useVModel, useVModels, useVibrate, useVirtualList, useWakeLock, useWebNotification, useWebSocket, useWebWorker, useWebWorkerFn, useWindowFocus, useWindowScroll, useWindowSize };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,SAAS,cAAc,IAAI,SAAS;AAClC,MAAIA;AACJ,QAAM,SAAS,WAAW;AAC1B,cAAY,MAAM;AAChB,WAAO,QAAQ,GAAG;AAAA,EACpB,GAAG,gBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG;AAAA,IAChD,QAAQA,MAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAOA,MAAK;AAAA,EACxE,CAAC,CAAC;AACF,SAAO,SAAS,MAAM;AACxB;AAEA,IAAI;AACJ,IAAM,WAAW,OAAO,WAAW;AACnC,IAAM,QAAQ,CAAC,QAAQ,OAAO,QAAQ;AACtC,IAAM,SAAS,CAAC,cAAc,UAAU;AACtC,MAAI,CAAC;AACH,YAAQ,KAAK,GAAG,KAAK;AACzB;AACA,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,YAAY,CAAC,QAAQ,OAAO,QAAQ;AAC1C,IAAM,aAAa,CAAC,QAAQ,OAAO,QAAQ;AAC3C,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,WAAW,CAAC,QAAQ,SAAS,KAAK,GAAG,MAAM;AACjD,IAAM,WAAW,CAAC,QAAQ,OAAO,WAAW,eAAe,SAAS,KAAK,GAAG,MAAM;AAClF,IAAM,MAAM,MAAM,KAAK,IAAI;AAC3B,IAAM,YAAY,MAAM,CAAC,KAAK,IAAI;AAClC,IAAM,QAAQ,CAAC,GAAG,KAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC;AAC7D,IAAM,OAAO,MAAM;AACnB;AACA,IAAM,OAAO,CAAC,KAAK,QAAQ;AACzB,QAAM,KAAK,KAAK,GAAG;AACnB,QAAM,KAAK,MAAM,GAAG;AACpB,SAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,MAAM,EAAE,IAAI;AACvD;AACA,IAAM,QAAQ,cAAc,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,cAAc,iBAAiB,KAAK,OAAO,UAAU,SAAS;AACjK,IAAM,SAAS,CAAC,KAAK,QAAQ,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AAE1E,SAAS,aAAa,GAAG;AACvB,SAAO,OAAO,MAAM,aAAa,EAAE,IAAI,MAAM,CAAC;AAChD;AAEA,SAAS,oBAAoB,QAAQ,IAAI;AACvC,WAAS,WAAW,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ,QAAQ,OAAO,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,IAC7G,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAM,eAAe,CAACC,YAAW;AAC/B,SAAOA,QAAO;AAChB;AACA,SAAS,eAAe,IAAI,UAAU,CAAC,GAAG;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe;AACnB,QAAM,gBAAgB,CAAC,WAAW;AAChC,iBAAa,MAAM;AACnB,iBAAa;AACb,mBAAe;AAAA,EACjB;AACA,QAAM,SAAS,CAACA,YAAW;AACzB,UAAM,WAAW,aAAa,EAAE;AAChC,UAAM,cAAc,aAAa,QAAQ,OAAO;AAChD,QAAI;AACF,oBAAc,KAAK;AACrB,QAAI,YAAY,KAAK,gBAAgB,UAAU,eAAe,GAAG;AAC/D,UAAI,UAAU;AACZ,sBAAc,QAAQ;AACtB,mBAAW;AAAA,MACb;AACA,aAAO,QAAQ,QAAQA,QAAO,CAAC;AAAA,IACjC;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,qBAAe,QAAQ,iBAAiB,SAAS;AACjD,UAAI,eAAe,CAAC,UAAU;AAC5B,mBAAW,WAAW,MAAM;AAC1B,cAAI;AACF,0BAAc,KAAK;AACrB,qBAAW;AACX,kBAAQA,QAAO,CAAC;AAAA,QAClB,GAAG,WAAW;AAAA,MAChB;AACA,cAAQ,WAAW,MAAM;AACvB,YAAI;AACF,wBAAc,QAAQ;AACxB,mBAAW;AACX,gBAAQA,QAAO,CAAC;AAAA,MAClB,GAAG,QAAQ;AAAA,IACb,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,eAAe,IAAI,WAAW,MAAM,UAAU,MAAM,iBAAiB,OAAO;AACnF,MAAI,WAAW;AACf,MAAI;AACJ,MAAI,YAAY;AAChB,MAAI,eAAe;AACnB,MAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AACR,mBAAa;AACb,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,SAAS,CAAC,YAAY;AAC1B,UAAM,WAAW,aAAa,EAAE;AAChC,UAAM,UAAU,KAAK,IAAI,IAAI;AAC7B,UAAMA,UAAS,MAAM;AACnB,aAAO,YAAY,QAAQ;AAAA,IAC7B;AACA,UAAM;AACN,QAAI,YAAY,GAAG;AACjB,iBAAW,KAAK,IAAI;AACpB,aAAOA,QAAO;AAAA,IAChB;AACA,QAAI,UAAU,aAAa,WAAW,CAAC,YAAY;AACjD,iBAAW,KAAK,IAAI;AACpB,MAAAA,QAAO;AAAA,IACT,WAAW,UAAU;AACnB,kBAAY,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC3C,uBAAe,iBAAiB,SAAS;AACzC,gBAAQ,WAAW,MAAM;AACvB,qBAAW,KAAK,IAAI;AACpB,sBAAY;AACZ,kBAAQA,QAAO,CAAC;AAChB,gBAAM;AAAA,QACR,GAAG,KAAK,IAAI,GAAG,WAAW,OAAO,CAAC;AAAA,MACpC,CAAC;AAAA,IACH;AACA,QAAI,CAAC,WAAW,CAAC;AACf,cAAQ,WAAW,MAAM,YAAY,MAAM,QAAQ;AACrD,gBAAY;AACZ,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,eAAe,eAAe,cAAc;AACnD,QAAM,WAAW,IAAI,IAAI;AACzB,WAAS,QAAQ;AACf,aAAS,QAAQ;AAAA,EACnB;AACA,WAAS,SAAS;AAChB,aAAS,QAAQ;AAAA,EACnB;AACA,QAAM,cAAc,IAAI,SAAS;AAC/B,QAAI,SAAS;AACX,mBAAa,GAAG,IAAI;AAAA,EACxB;AACA,SAAO,EAAE,UAAU,SAAS,QAAQ,GAAG,OAAO,QAAQ,YAAY;AACpE;AAEA,SAAS,WAAW,OAAO,iBAAiB;AAC1C,MAAI;AACF;AACF,QAAM,IAAI,MAAM,YAAY,IAAI,0BAA0B;AAC5D;AACA,SAAS,gBAAgB,OAAO,iBAAiB;AAC/C,MAAI,UAAU,QAAQ,WAAW,MAAM;AACrC;AACF,QAAM,IAAI,MAAM,YAAY,IAAI,qCAAqC;AACvE;AACA,IAAM,iBAAiB;AAAA,EACrB,SAAS,SAAS,YAAY;AAAA,EAC9B,SAAS,SAAS,YAAY;AAAA,EAC9B,WAAW,SAAS,cAAc;AACpC;AAEA,SAAS,eAAe,IAAI,iBAAiB,OAAO,SAAS,WAAW;AACtE,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI;AACF,iBAAW,MAAM,OAAO,MAAM,GAAG,EAAE;AAAA;AAEnC,iBAAW,SAAS,EAAE;AAAA,EAC1B,CAAC;AACH;AACA,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;AACA,SAAS,uBAAuB,IAAI;AAClC,MAAI;AACJ,WAAS,UAAU;AACjB,QAAI,CAAC;AACH,iBAAW,GAAG;AAChB,WAAO;AAAA,EACT;AACA,UAAQ,QAAQ,YAAY;AAC1B,UAAM,QAAQ;AACd,eAAW;AACX,QAAI;AACF,YAAM;AAAA,EACV;AACA,SAAO;AACT;AACA,SAAS,OAAO,IAAI;AAClB,SAAO,GAAG;AACZ;AACA,SAAS,aAAa,QAAQ,OAAO;AACnC,SAAO,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG;AACnC;AACA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAID;AACJ,MAAI,OAAO,WAAW;AACpB,WAAO,SAAS;AAClB,QAAM,UAAUA,MAAK,OAAO,MAAM,oBAAoB,MAAM,OAAO,SAASA,IAAG,CAAC,MAAM;AACtF,QAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACtC,QAAM,SAAS,WAAW,KAAK,IAAI;AACnC,MAAI,OAAO,MAAM,MAAM;AACrB,WAAO;AACT,SAAO,SAAS;AAClB;AACA,SAAS,WAAW,KAAKE,OAAM,gBAAgB,OAAO;AACpD,SAAOA,MAAK,OAAO,CAAC,GAAG,MAAM;AAC3B,QAAI,KAAK,KAAK;AACZ,UAAI,CAAC,iBAAiB,IAAI,CAAC,MAAM;AAC/B,UAAE,CAAC,IAAI,IAAI,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,oBAAoB,QAAQ,IAAI;AACvC,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,IAAI,IAAI;AACtB,QAAM,SAAS,MAAM;AACnB,UAAM,QAAQ;AACd,YAAQ;AAAA,EACV;AACA,QAAM,QAAQ,QAAQ,EAAE,OAAO,OAAO,CAAC;AACvC,QAAMC,OAAM,WAAW,EAAE,IAAI,KAAK,GAAG;AACrC,QAAMC,OAAM,WAAW,EAAE,IAAI,SAAS,GAAG;AACzC,QAAM,SAAS,UAAU,CAAC,QAAQ,aAAa;AAC7C,YAAQ;AACR,cAAU;AACV,WAAO;AAAA,MACL,MAAM;AACJ,YAAI,MAAM,OAAO;AACf,cAAID,KAAI;AACR,gBAAM,QAAQ;AAAA,QAChB;AACA,cAAM;AACN,eAAO;AAAA,MACT;AAAA,MACA,IAAI,IAAI;AACN,QAAAC,QAAO,OAAO,SAASA,KAAI,EAAE;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,OAAO,aAAa,MAAM;AAC5B,WAAO,UAAU;AACnB,SAAO;AACT;AAEA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,gBAAgB,GAAG;AACrB,mBAAe,EAAE;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB;AACzB,QAAM,MAAM,CAAC;AACb,QAAM,MAAM,CAAC,OAAO;AAClB,UAAM,QAAQ,IAAI,QAAQ,EAAE;AAC5B,QAAI,UAAU;AACZ,UAAI,OAAO,OAAO,CAAC;AAAA,EACvB;AACA,QAAM,KAAK,CAAC,OAAO;AACjB,QAAI,KAAK,EAAE;AACX,UAAM,QAAQ,MAAM,IAAI,EAAE;AAC1B,sBAAkB,KAAK;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACA,QAAM,UAAU,CAAC,UAAU;AACzB,QAAI,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;AAAA,EAC/B;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,cAAc;AACvC,MAAI,cAAc;AAClB,MAAI;AACJ,QAAM,QAAQ,YAAY,IAAI;AAC9B,SAAO,MAAM;AACX,QAAI,CAAC,aAAa;AAChB,cAAQ,MAAM,IAAI,YAAY;AAC9B,oBAAc;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,qBAAqB,YAAY;AACxC,QAAM,MAAM,OAAO,gBAAgB;AACnC,QAAM,oBAAoB,IAAI,SAAS;AACrC,UAAM,QAAQ,WAAW,GAAG,IAAI;AAChC,YAAQ,KAAK,KAAK;AAClB,WAAO;AAAA,EACT;AACA,QAAM,mBAAmB,MAAM,OAAO,GAAG;AACzC,SAAO,CAAC,mBAAmB,gBAAgB;AAC7C;AAEA,SAAS,uBAAuB,YAAY;AAC1C,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,MAAM;AACpB,mBAAe;AACf,QAAI,SAAS,eAAe,GAAG;AAC7B,YAAM,KAAK;AACX,cAAQ;AACR,cAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO,IAAI,SAAS;AAClB,mBAAe;AACf,QAAI,CAAC,OAAO;AACV,cAAQ,YAAY,IAAI;AACxB,cAAQ,MAAM,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC;AAAA,IAC7C;AACA,sBAAkB,OAAO;AACzB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,UAAUC,MAAK,QAAQ,EAAE,aAAa,OAAO,SAAS,KAAK,IAAI,CAAC,GAAG;AAC1E,kBAAgB;AAChB,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACjD,QAAI,QAAQ;AACV;AACF,QAAI,MAAM,KAAK,KAAK,QAAQ;AAC1B,aAAO,eAAeA,MAAK,KAAK;AAAA,QAC9B,MAAM;AACJ,iBAAO,MAAM;AAAA,QACf;AAAA,QACA,IAAI,GAAG;AACL,gBAAM,QAAQ;AAAA,QAChB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,aAAO,eAAeA,MAAK,KAAK,EAAE,OAAO,WAAW,CAAC;AAAA,IACvD;AAAA,EACF;AACA,SAAOA;AACT;AAEA,SAAS,IAAI,KAAK,KAAK;AACrB,MAAI,OAAO;AACT,WAAO,MAAM,GAAG;AAClB,SAAO,MAAM,GAAG,EAAE,GAAG;AACvB;AAEA,SAAS,UAAU,GAAG;AACpB,SAAO,MAAM,CAAC,KAAK;AACrB;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,mBAAmB,KAAK,KAAK;AACpC,MAAI,OAAO,WAAW,aAAa;AACjC,UAAM,QAAQ,iBAAiB,CAAC,GAAG,GAAG;AACtC,WAAO,eAAe,OAAO,OAAO,UAAU;AAAA,MAC5C,YAAY;AAAA,MACZ,QAAQ;AACN,YAAI,QAAQ;AACZ,eAAO;AAAA,UACL,MAAM,OAAO;AAAA,YACX,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM,QAAQ,IAAI;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG;AAAA,EACpC;AACF;AAEA,SAAS,SAAS,IAAI,SAAS;AAC7B,QAAM,WAAW,WAAW,OAAO,SAAS,QAAQ,oBAAoB,QAAQ,QAAQ;AACxF,SAAO,YAAY,MAAM;AACvB,WAAO,SAAS,MAAM,GAAG,MAAM,MAAM,KAAK,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,EACnE;AACF;AAEA,SAAS,eAAe,KAAK,gBAAgB,CAAC,GAAG;AAC/C,MAAIH,QAAO,CAAC;AACZ,MAAI;AACJ,MAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,IAAAA,QAAO;AAAA,EACT,OAAO;AACL,cAAU;AACV,UAAM,EAAE,uBAAuB,KAAK,IAAI;AACxC,IAAAA,MAAK,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC;AAC7B,QAAI;AACF,MAAAA,MAAK,KAAK,GAAG,OAAO,oBAAoB,GAAG,CAAC;AAAA,EAChD;AACA,SAAO,OAAO,YAAYA,MAAK,IAAI,CAAC,QAAQ;AAC1C,UAAM,QAAQ,IAAI,GAAG;AACrB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,aAAa,SAAS,MAAM,KAAK,GAAG,GAAG,OAAO,IAAI;AAAA,IACrE;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,SAAS,WAAW,WAAW;AAC7B,MAAI,CAAC,MAAM,SAAS;AAClB,WAAO,SAAS,SAAS;AAC3B,QAAM,QAAQ,IAAI,MAAM,CAAC,GAAG;AAAA,IAC1B,IAAI,GAAG,GAAG,UAAU;AAClB,aAAO,MAAM,QAAQ,IAAI,UAAU,OAAO,GAAG,QAAQ,CAAC;AAAA,IACxD;AAAA,IACA,IAAI,GAAG,GAAG,OAAO;AACf,UAAI,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK;AAC3C,kBAAU,MAAM,CAAC,EAAE,QAAQ;AAAA;AAE3B,kBAAU,MAAM,CAAC,IAAI;AACvB,aAAO;AAAA,IACT;AAAA,IACA,eAAe,GAAG,GAAG;AACnB,aAAO,QAAQ,eAAe,UAAU,OAAO,CAAC;AAAA,IAClD;AAAA,IACA,IAAI,GAAG,GAAG;AACR,aAAO,QAAQ,IAAI,UAAU,OAAO,CAAC;AAAA,IACvC;AAAA,IACA,UAAU;AACR,aAAO,OAAO,KAAK,UAAU,KAAK;AAAA,IACpC;AAAA,IACA,2BAA2B;AACzB,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,SAAS,KAAK;AACvB;AAEA,SAAS,iBAAiB,IAAI;AAC5B,SAAO,WAAW,SAAS,EAAE,CAAC;AAChC;AAEA,SAAS,aAAa,QAAQA,OAAM;AAClC,QAAM,WAAWA,MAAK,KAAK;AAC3B,SAAO,iBAAiB,MAAM,OAAO,YAAY,OAAO,QAAQ,OAAS,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACzH;AAEA,SAAS,aAAa,QAAQA,OAAM;AAClC,QAAM,WAAWA,MAAK,KAAK;AAC3B,SAAO,SAAS,OAAO,YAAY,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E;AAEA,SAAS,aAAa,cAAc,UAAU,KAAK;AACjD,SAAO,UAAU,CAAC,OAAO,YAAY;AACnC,QAAI,QAAQ;AACZ,QAAI;AACJ,UAAM,aAAa,MAAM,WAAW,MAAM;AACxC,cAAQ;AACR,cAAQ;AAAA,IACV,GAAG,aAAa,OAAO,CAAC;AACxB,sBAAkB,MAAM;AACtB,mBAAa,KAAK;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,MACL,MAAM;AACJ,cAAM;AACN,eAAO;AAAA,MACT;AAAA,MACA,IAAI,UAAU;AACZ,gBAAQ;AACR,gBAAQ;AACR,qBAAa,KAAK;AAClB,gBAAQ,WAAW;AAAA,MACrB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,cAAc,IAAI,KAAK,KAAK,UAAU,CAAC,GAAG;AACjD,SAAO,oBAAoB,eAAe,IAAI,OAAO,GAAG,EAAE;AAC5D;AAEA,SAAS,aAAa,OAAO,KAAK,KAAK,UAAU,CAAC,GAAG;AACnD,QAAM,YAAY,IAAI,MAAM,KAAK;AACjC,QAAM,UAAU,cAAc,MAAM;AAClC,cAAU,QAAQ,MAAM;AAAA,EAC1B,GAAG,IAAI,OAAO;AACd,QAAM,OAAO,MAAM,QAAQ,CAAC;AAC5B,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ,cAAc;AACxC,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,UAAIF;AACJ,cAAQA,MAAK,OAAO,UAAU,OAAOA,MAAK;AAAA,IAC5C;AAAA,IACA,IAAI,OAAO;AACT,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AAEA,SAAS,cAAc,IAAI,KAAK,KAAK,WAAW,OAAO,UAAU,MAAM,iBAAiB,OAAO;AAC7F,SAAO,oBAAoB,eAAe,IAAI,UAAU,SAAS,cAAc,GAAG,EAAE;AACtF;AAEA,SAAS,aAAa,OAAO,QAAQ,KAAK,WAAW,MAAM,UAAU,MAAM;AACzE,MAAI,SAAS;AACX,WAAO;AACT,QAAM,YAAY,IAAI,MAAM,KAAK;AACjC,QAAM,UAAU,cAAc,MAAM;AAClC,cAAU,QAAQ,MAAM;AAAA,EAC1B,GAAG,OAAO,UAAU,OAAO;AAC3B,QAAM,OAAO,MAAM,QAAQ,CAAC;AAC5B,SAAO;AACT;AAEA,SAAS,eAAe,SAAS,UAAU,CAAC,GAAG;AAC7C,MAAI,SAAS;AACb,MAAI;AACJ,MAAI;AACJ,QAAMK,OAAM,UAAU,CAAC,QAAQ,aAAa;AAC1C,YAAQ;AACR,cAAU;AACV,WAAO;AAAA,MACL,MAAM;AACJ,eAAOF,KAAI;AAAA,MACb;AAAA,MACA,IAAI,GAAG;AACL,QAAAC,KAAI,CAAC;AAAA,MACP;AAAA,IACF;AAAA,EACF,CAAC;AACD,WAASD,KAAI,WAAW,MAAM;AAC5B,QAAI;AACF,YAAM;AACR,WAAO;AAAA,EACT;AACA,WAASC,KAAI,OAAO,aAAa,MAAM;AACrC,QAAIJ,KAAI;AACR,QAAI,UAAU;AACZ;AACF,UAAM,MAAM;AACZ,UAAMA,MAAK,QAAQ,mBAAmB,OAAO,SAASA,IAAG,KAAK,SAAS,OAAO,GAAG,OAAO;AACtF;AACF,aAAS;AACT,KAAC,KAAK,QAAQ,cAAc,OAAO,SAAS,GAAG,KAAK,SAAS,OAAO,GAAG;AACvE,QAAI;AACF,cAAQ;AAAA,EACZ;AACA,QAAM,eAAe,MAAMG,KAAI,KAAK;AACpC,QAAM,YAAY,CAAC,MAAMC,KAAI,GAAG,KAAK;AACrC,QAAM,OAAO,MAAMD,KAAI,KAAK;AAC5B,QAAM,MAAM,CAAC,MAAMC,KAAI,GAAG,KAAK;AAC/B,SAAO,UAAUC,MAAK;AAAA,IACpB,KAAAF;AAAA,IACA,KAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,EAAE,YAAY,KAAK,CAAC;AACzB;AACA,IAAM,gBAAgB;AAEtB,SAAS,WAAW,GAAG;AACrB,SAAO,OAAO,MAAM,aAAa,SAAS,CAAC,IAAI,IAAI,CAAC;AACtD;AAEA,SAASA,QAAO,MAAM;AACpB,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,CAACC,MAAK,KAAK,IAAI;AACrB,IAAAA,KAAI,QAAQ;AAAA,EACd;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,QAAI,QAAQ;AACV,UAAM,GAAG,IAAI;AAAA,IACf,OAAO;AACL,YAAM,CAAC,QAAQ,KAAK,KAAK,IAAI;AAC7B,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF;AACF;AAEA,SAAS,QAAQ,MAAM,OAAO,UAAU,CAAC,GAAG;AAC1C,MAAIL,KAAI;AACR,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY,CAAC;AAAA,EACf,IAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,gBAAgBA,MAAK,UAAU,QAAQ,OAAOA,MAAK,CAAC,MAAM;AAChE,QAAM,gBAAgB,KAAK,UAAU,QAAQ,OAAO,KAAK,CAAC,MAAM;AAChE,MAAI,cAAc,UAAU,cAAc,OAAO;AAC/C,gBAAY,MAAM,MAAM,CAAC,aAAa,MAAM,QAAQ,aAAa,QAAQ,GAAG,EAAE,OAAO,MAAM,UAAU,CAAC;AAAA,EACxG;AACA,MAAI,cAAc,UAAU,cAAc,OAAO;AAC/C,iBAAa,MAAM,OAAO,CAAC,aAAa,KAAK,QAAQ,aAAa,QAAQ,GAAG,EAAE,OAAO,MAAM,UAAU,CAAC;AAAA,EACzG;AACA,SAAO,MAAM;AACX,iBAAa,OAAO,SAAS,UAAU;AACvC,kBAAc,OAAO,SAAS,WAAW;AAAA,EAC3C;AACF;AAEA,SAAS,SAAS,QAAQ,SAAS,UAAU,CAAC,GAAG;AAC/C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,EACd,IAAI;AACJ,MAAI,CAAC,MAAM,QAAQ,OAAO;AACxB,cAAU,CAAC,OAAO;AACpB,SAAO,MAAM,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,WAAW,OAAO,QAAQ,QAAQ,GAAG,EAAE,OAAO,MAAM,UAAU,CAAC;AACrH;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,SAASM,QAAO,WAAW;AACzB,MAAI,CAAC,MAAM,SAAS;AAClB,WAAO,OAAS,SAAS;AAC3B,QAAM,SAAS,MAAM,QAAQ,UAAU,KAAK,IAAI,IAAI,MAAM,UAAU,MAAM,MAAM,IAAI,CAAC;AACrF,aAAW,OAAO,UAAU,OAAO;AACjC,WAAO,GAAG,IAAI,UAAU,OAAO;AAAA,MAC7B,MAAM;AACJ,eAAO,UAAU,MAAM,GAAG;AAAA,MAC5B;AAAA,MACA,IAAI,GAAG;AACL,YAAI,MAAM,QAAQ,UAAU,KAAK,GAAG;AAClC,gBAAM,OAAO,CAAC,GAAG,UAAU,KAAK;AAChC,eAAK,GAAG,IAAI;AACZ,oBAAU,QAAQ;AAAA,QACpB,OAAO;AACL,gBAAM,YAAY,gBAAgB,iBAAiB,CAAC,GAAG,UAAU,KAAK,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;AACrF,iBAAO,eAAe,WAAW,UAAU,KAAK;AAChD,oBAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,IACF,EAAE;AAAA,EACJ;AACA,SAAO;AACT;AAEA,SAAS,iBAAiB,IAAI,OAAO,MAAM;AACzC,MAAI,mBAAmB;AACrB,kBAAc,EAAE;AAAA,WACT;AACP,OAAG;AAAA;AAEH,aAAS,EAAE;AACf;AAEA,SAAS,mBAAmB,IAAI;AAC9B,MAAI,mBAAmB;AACrB,oBAAgB,EAAE;AACtB;AAEA,SAAS,aAAa,IAAI,OAAO,MAAM;AACrC,MAAI,mBAAmB;AACrB,cAAU,EAAE;AAAA,WACL;AACP,OAAG;AAAA;AAEH,aAAS,EAAE;AACf;AAEA,SAAS,eAAe,IAAI;AAC1B,MAAI,mBAAmB;AACrB,gBAAY,EAAE;AAClB;AAEA,SAAS,YAAY,GAAG,QAAQ,OAAO;AACrC,WAAS,QAAQ,WAAW,EAAE,QAAQ,QAAQ,OAAO,OAAO,SAAS,eAAe,IAAI,CAAC,GAAG;AAC1F,QAAI,OAAO;AACX,UAAM,UAAU,IAAI,QAAQ,CAAC,YAAY;AACvC,aAAO,MAAM,GAAG,CAAC,MAAM;AACrB,YAAI,UAAU,CAAC,MAAM,OAAO;AAC1B,kBAAQ,OAAO,SAAS,KAAK;AAC7B,kBAAQ,CAAC;AAAA,QACX;AAAA,MACF,GAAG;AAAA,QACD;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AACD,UAAM,WAAW,CAAC,OAAO;AACzB,QAAI,WAAW,MAAM;AACnB,eAAS,KAAK,eAAe,SAAS,cAAc,EAAE,KAAK,MAAM,aAAa,CAAC,CAAC,EAAE,QAAQ,MAAM,QAAQ,OAAO,SAAS,KAAK,CAAC,CAAC;AAAA,IACjI;AACA,WAAO,QAAQ,KAAK,QAAQ;AAAA,EAC9B;AACA,WAAS,KAAK,OAAO,SAAS;AAC5B,QAAI,CAAC,MAAM,KAAK;AACd,aAAO,QAAQ,CAAC,MAAM,MAAM,OAAO,OAAO;AAC5C,UAAM,EAAE,QAAQ,QAAQ,OAAO,OAAO,SAAS,eAAe,IAAI,WAAW,OAAO,UAAU,CAAC;AAC/F,QAAI,OAAO;AACX,UAAM,UAAU,IAAI,QAAQ,CAAC,YAAY;AACvC,aAAO,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM;AACrC,YAAI,WAAW,OAAO,KAAK;AACzB,kBAAQ,OAAO,SAAS,KAAK;AAC7B,kBAAQ,EAAE;AAAA,QACZ;AAAA,MACF,GAAG;AAAA,QACD;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AACD,UAAM,WAAW,CAAC,OAAO;AACzB,QAAI,WAAW,MAAM;AACnB,eAAS,KAAK,eAAe,SAAS,cAAc,EAAE,KAAK,MAAM,aAAa,CAAC,CAAC,EAAE,QAAQ,MAAM;AAC9F,gBAAQ,OAAO,SAAS,KAAK;AAC7B,eAAO,aAAa,CAAC;AAAA,MACvB,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,QAAQ,KAAK,QAAQ;AAAA,EAC9B;AACA,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,CAAC,MAAM,QAAQ,CAAC,GAAG,OAAO;AAAA,EAC3C;AACA,WAAS,SAAS,SAAS;AACzB,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B;AACA,WAAS,cAAc,SAAS;AAC9B,WAAO,KAAK,QAAQ,OAAO;AAAA,EAC7B;AACA,WAAS,QAAQ,SAAS;AACxB,WAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,EACtC;AACA,WAAS,WAAW,OAAO,SAAS;AAClC,WAAO,QAAQ,CAAC,MAAM;AACpB,YAAM,QAAQ,MAAM,KAAK,CAAC;AAC1B,aAAO,MAAM,SAAS,KAAK,KAAK,MAAM,SAAS,aAAa,KAAK,CAAC;AAAA,IACpE,GAAG,OAAO;AAAA,EACZ;AACA,WAAS,QAAQ,SAAS;AACxB,WAAO,aAAa,GAAG,OAAO;AAAA,EAChC;AACA,WAAS,aAAa,IAAI,GAAG,SAAS;AACpC,QAAI,QAAQ;AACZ,WAAO,QAAQ,MAAM;AACnB,eAAS;AACT,aAAO,SAAS;AAAA,IAClB,GAAG,OAAO;AAAA,EACZ;AACA,MAAI,MAAM,QAAQ,aAAa,CAAC,CAAC,GAAG;AAClC,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI,MAAM;AACR,eAAO,YAAY,GAAG,CAAC,KAAK;AAAA,MAC9B;AAAA,IACF;AACA,WAAO;AAAA,EACT,OAAO;AACL,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI,MAAM;AACR,eAAO,YAAY,GAAG,CAAC,KAAK;AAAA,MAC9B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,MAAM,GAAG;AAChB,SAAO,YAAY,CAAC;AACtB;AAEA,SAAS,cAAc,MAAM,IAAI;AAC/B,SAAO,SAAS,MAAM,aAAa,IAAI,EAAE,MAAM,CAAC,SAAS,OAAO,UAAU,GAAG,aAAa,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC;AACpH;AAEA,SAAS,eAAe,MAAM,IAAI;AAChC,SAAO,SAAS,MAAM,aAAa,IAAI,EAAE,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;AACjF;AAEA,SAAS,aAAa,MAAM,IAAI;AAC9B,SAAO,SAAS,MAAM,aAAa,aAAa,IAAI,EAAE,KAAK,CAAC,SAAS,OAAO,UAAU,GAAG,aAAa,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,CAAC;AACjI;AAEA,SAAS,kBAAkB,MAAM,IAAI;AACnC,SAAO,SAAS,MAAM,aAAa,IAAI,EAAE,UAAU,CAAC,SAAS,OAAO,UAAU,GAAG,aAAa,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC;AACxH;AAEA,SAAS,SAAS,KAAK,IAAI;AACzB,MAAI,QAAQ,IAAI;AAChB,SAAO,UAAU,GAAG;AAClB,QAAI,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG;AAC3B,aAAO,IAAI,KAAK;AAAA,EACpB;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM,IAAI;AAClC,SAAO,SAAS,MAAM,aAAa,CAAC,MAAM,UAAU,WAAW,SAAS,aAAa,IAAI,GAAG,CAAC,SAAS,OAAO,UAAU,GAAG,aAAa,OAAO,GAAG,OAAO,KAAK,CAAC,IAAI,aAAa,IAAI,EAAE,SAAS,CAAC,SAAS,OAAO,UAAU,GAAG,aAAa,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,CAAC;AACpQ;AAEA,SAAS,aAAa,MAAM,WAAW;AACrC,SAAO,SAAS,MAAM,aAAa,IAAI,EAAE,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC,EAAE,KAAK,aAAa,SAAS,CAAC,CAAC;AACpG;AAEA,SAAS,YAAY,MAAM,IAAI;AAC7B,SAAO,SAAS,MAAM,aAAa,IAAI,EAAE,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;AAC9E;AAEA,SAAS,eAAe,MAAM,YAAY,MAAM;AAC9C,QAAM,iBAAiB,CAAC,KAAK,OAAO,UAAU,QAAQ,aAAa,GAAG,GAAG,aAAa,KAAK,GAAG,KAAK;AACnG,SAAO,SAAS,MAAM;AACpB,UAAM,WAAW,aAAa,IAAI;AAClC,WAAO,KAAK,SAAS,SAAS,OAAO,gBAAgB,aAAa,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS,OAAO,cAAc;AAAA,EAC9G,CAAC;AACH;AAEA,SAAS,aAAa,MAAM,IAAI;AAC9B,SAAO,SAAS,MAAM,aAAa,IAAI,EAAE,KAAK,CAAC,SAAS,OAAO,UAAU,GAAG,aAAa,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC;AACnH;AAEA,SAAS,eAAe,MAAM;AAC5B,SAAO,SAAS,MAAM,CAAC,GAAG,IAAI,IAAI,aAAa,IAAI,EAAE,IAAI,CAAC,YAAY,aAAa,OAAO,CAAC,CAAC,CAAC,CAAC;AAChG;AAEA,SAAS,WAAW,eAAe,GAAG,UAAU,CAAC,GAAG;AAClD,QAAM,QAAQ,IAAI,YAAY;AAC9B,QAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,EACR,IAAI;AACJ,QAAM,MAAM,CAAC,QAAQ,MAAM,MAAM,QAAQ,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1E,QAAM,MAAM,CAAC,QAAQ,MAAM,MAAM,QAAQ,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1E,QAAMH,OAAM,MAAM,MAAM;AACxB,QAAMC,OAAM,CAAC,QAAQ,MAAM,QAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC;AACnE,QAAM,QAAQ,CAAC,MAAM,iBAAiB;AACpC,mBAAe;AACf,WAAOA,KAAI,GAAG;AAAA,EAChB;AACA,SAAO,EAAE,OAAO,KAAK,KAAK,KAAAD,MAAK,KAAAC,MAAK,MAAM;AAC5C;AAEA,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,kBAAkB,CAAC,OAAO,SAAS,aAAa,cAAc;AAClE,MAAI,IAAI,QAAQ,KAAK,OAAO;AAC5B,MAAI;AACF,QAAI,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,KAAK,SAAS,OAAO,GAAG,IAAI,KAAK,EAAE;AAC7D,SAAO,cAAc,EAAE,YAAY,IAAI;AACzC;AACA,IAAM,aAAa,CAAC,MAAM,WAAW,UAAU,CAAC,MAAM;AACpD,MAAIJ;AACJ,QAAM,QAAQ,KAAK,YAAY;AAC/B,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,OAAO,KAAK,QAAQ;AAC1B,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,UAAU,KAAK,WAAW;AAChC,QAAM,UAAU,KAAK,WAAW;AAChC,QAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAM,MAAM,KAAK,OAAO;AACxB,QAAM,YAAYA,MAAK,QAAQ,mBAAmB,OAAOA,MAAK;AAC9D,QAAM,UAAU;AAAA,IACd,IAAI,MAAM,OAAO,KAAK,EAAE,MAAM,EAAE;AAAA,IAChC,MAAM,MAAM;AAAA,IACZ,GAAG,MAAM,QAAQ;AAAA,IACjB,IAAI,MAAM,GAAG,QAAQ,CAAC,GAAG,SAAS,GAAG,GAAG;AAAA,IACxC,KAAK,MAAM,KAAK,mBAAmB,QAAQ,SAAS,EAAE,OAAO,QAAQ,CAAC;AAAA,IACtE,MAAM,MAAM,KAAK,mBAAmB,QAAQ,SAAS,EAAE,OAAO,OAAO,CAAC;AAAA,IACtE,GAAG,MAAM,OAAO,IAAI;AAAA,IACpB,IAAI,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,GAAG;AAAA,IACnC,GAAG,MAAM,OAAO,KAAK;AAAA,IACrB,IAAI,MAAM,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG;AAAA,IACpC,GAAG,MAAM,GAAG,QAAQ,MAAM,EAAE,GAAG,SAAS,GAAG,GAAG;AAAA,IAC9C,IAAI,MAAM,GAAG,QAAQ,MAAM,EAAE,GAAG,SAAS,GAAG,GAAG;AAAA,IAC/C,GAAG,MAAM,OAAO,OAAO;AAAA,IACvB,IAAI,MAAM,GAAG,OAAO,GAAG,SAAS,GAAG,GAAG;AAAA,IACtC,GAAG,MAAM,OAAO,OAAO;AAAA,IACvB,IAAI,MAAM,GAAG,OAAO,GAAG,SAAS,GAAG,GAAG;AAAA,IACtC,KAAK,MAAM,GAAG,YAAY,GAAG,SAAS,GAAG,GAAG;AAAA,IAC5C,GAAG,MAAM;AAAA,IACT,IAAI,MAAM,KAAK,mBAAmB,QAAQ,SAAS,EAAE,SAAS,SAAS,CAAC;AAAA,IACxE,KAAK,MAAM,KAAK,mBAAmB,QAAQ,SAAS,EAAE,SAAS,QAAQ,CAAC;AAAA,IACxE,MAAM,MAAM,KAAK,mBAAmB,QAAQ,SAAS,EAAE,SAAS,OAAO,CAAC;AAAA,IACxE,GAAG,MAAM,SAAS,OAAO,OAAO;AAAA,IAChC,IAAI,MAAM,SAAS,OAAO,SAAS,OAAO,IAAI;AAAA,IAC9C,GAAG,MAAM,SAAS,OAAO,SAAS,IAAI;AAAA,IACtC,IAAI,MAAM,SAAS,OAAO,SAAS,MAAM,IAAI;AAAA,EAC/C;AACA,SAAO,UAAU,QAAQ,cAAc,CAAC,OAAO,OAAO,MAAM,QAAQ,KAAK,EAAE,CAAC;AAC9E;AACA,IAAM,gBAAgB,CAAC,SAAS;AAC9B,MAAI,SAAS;AACX,WAAO,oBAAI,KAAK,GAAG;AACrB,MAAI,SAAS;AACX,WAAO,oBAAI,KAAK;AAClB,MAAI,gBAAgB;AAClB,WAAO,IAAI,KAAK,IAAI;AACtB,MAAI,OAAO,SAAS,YAAY,CAAC,MAAM,KAAK,IAAI,GAAG;AACjD,UAAM,IAAI,KAAK,MAAM,WAAW;AAChC,QAAI,GAAG;AACL,YAAM,IAAI,EAAE,CAAC,IAAI,KAAK;AACtB,YAAM,MAAM,EAAE,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC;AACvC,aAAO,IAAI,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE;AAAA,IACzE;AAAA,EACF;AACA,SAAO,IAAI,KAAK,IAAI;AACtB;AACA,SAAS,cAAc,MAAM,YAAY,YAAY,UAAU,CAAC,GAAG;AACjE,SAAO,SAAS,MAAM,WAAW,cAAc,aAAa,IAAI,CAAC,GAAG,aAAa,SAAS,GAAG,OAAO,CAAC;AACvG;AAEA,SAAS,cAAc,IAAI,WAAW,KAAK,UAAU,CAAC,GAAG;AACvD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,oBAAoB;AAAA,EACtB,IAAI;AACJ,MAAI,QAAQ;AACZ,QAAM,WAAW,IAAI,KAAK;AAC1B,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,oBAAc,KAAK;AACnB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,QAAQ;AACf,aAAS,QAAQ;AACjB,UAAM;AAAA,EACR;AACA,WAAS,SAAS;AAChB,UAAM,gBAAgB,aAAa,QAAQ;AAC3C,QAAI,iBAAiB;AACnB;AACF,aAAS,QAAQ;AACjB,QAAI;AACF,SAAG;AACL,UAAM;AACN,YAAQ,YAAY,IAAI,aAAa;AAAA,EACvC;AACA,MAAI,aAAa;AACf,WAAO;AACT,MAAI,MAAM,QAAQ,KAAK,WAAW,QAAQ,GAAG;AAC3C,UAAM,YAAY,MAAM,UAAU,MAAM;AACtC,UAAI,SAAS,SAAS;AACpB,eAAO;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS;AAAA,EAC7B;AACA,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,YAAY,WAAW,KAAK,UAAU,CAAC,GAAG;AACjD,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B,YAAY;AAAA,IACZ;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,IAAI,CAAC;AACrB,QAAM,SAAS,MAAM,QAAQ,SAAS;AACtC,QAAM,QAAQ,MAAM;AAClB,YAAQ,QAAQ;AAAA,EAClB;AACA,QAAM,WAAW,cAAc,WAAW,MAAM;AAC9C,WAAO;AACP,aAAS,QAAQ,KAAK;AAAA,EACxB,IAAI,QAAQ,UAAU,EAAE,UAAU,CAAC;AACnC,MAAI,gBAAgB;AAClB,WAAO,iBAAiB;AAAA,MACtB;AAAA,MACA;AAAA,IACF,GAAG,QAAQ;AAAA,EACb,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,QAAQ,UAAU,CAAC,GAAG;AAC5C,MAAIA;AACJ,QAAM,KAAK,KAAKA,MAAK,QAAQ,iBAAiB,OAAOA,MAAK,IAAI;AAC9D,QAAM,QAAQ,MAAM,GAAG,QAAQ,UAAU,GAAG,OAAO;AACnD,SAAO;AACT;AAEA,SAAS,aAAa,IAAI,UAAU,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,YAAY,IAAI,KAAK;AAC3B,MAAI,QAAQ;AACZ,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,OAAO;AACd,cAAU,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,WAAS,SAAS,MAAM;AACtB,UAAM;AACN,cAAU,QAAQ;AAClB,YAAQ,WAAW,MAAM;AACvB,gBAAU,QAAQ;AAClB,cAAQ;AACR,SAAG,GAAG,IAAI;AAAA,IACZ,GAAG,aAAa,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,WAAW;AACb,cAAU,QAAQ;AAClB,QAAI;AACF,YAAM;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL,WAAW,SAAS,SAAS;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,WAAW,WAAW,KAAK,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,aAAa,YAAY,OAAO,WAAW,MAAM,UAAU,OAAO;AACnF,QAAM,QAAQ,SAAS,MAAM,CAAC,SAAS,UAAU,KAAK;AACtD,MAAI,gBAAgB;AAClB,WAAO,iBAAiB;AAAA,MACtB;AAAA,IACF,GAAG,QAAQ;AAAA,EACb,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,OAAO,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACJ,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,SAAS,MAAM;AACpB,QAAI,WAAW,aAAa,KAAK;AACjC,QAAI,OAAO,aAAa;AACtB,iBAAW,OAAO,MAAM,EAAE,UAAU,KAAK;AAC3C,QAAI,aAAa,MAAM,QAAQ;AAC7B,iBAAW;AACb,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,MAAM,GAAG,aAAa,KAAK,CAAC,EAAE;AAChD;AAEA,SAAS,UAAU,eAAe,OAAO,UAAU,CAAC,GAAG;AACrD,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,aAAa;AAAA,EACf,IAAI;AACJ,QAAM,aAAa,MAAM,YAAY;AACrC,QAAM,SAAS,IAAI,YAAY;AAC/B,WAAS,OAAO,OAAO;AACrB,QAAI,UAAU,QAAQ;AACpB,aAAO,QAAQ;AACf,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,YAAM,SAAS,aAAa,WAAW;AACvC,aAAO,QAAQ,OAAO,UAAU,SAAS,aAAa,UAAU,IAAI;AACpE,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACA,MAAI;AACF,WAAO;AAAA;AAEP,WAAO,CAAC,QAAQ,MAAM;AAC1B;AAEA,SAAS,WAAW,QAAQ,IAAI,SAAS;AACvC,MAAI,WAAW,WAAW,OAAO,SAAS,QAAQ,aAAa,CAAC,IAAI;AAAA,IAClE,GAAG,kBAAkB,WAAW,OAAO,IAAI,MAAM,QAAQ,MAAM,IAAI,SAAS,MAAM,MAAM;AAAA,EAC1F;AACA,SAAO,MAAM,QAAQ,CAAC,SAAS,GAAG,cAAc;AAC9C,UAAM,iBAAiB,IAAI,MAAM,QAAQ,MAAM;AAC/C,UAAM,QAAQ,CAAC;AACf,eAAW,OAAO,SAAS;AACzB,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,CAAC,eAAe,CAAC,KAAK,QAAQ,QAAQ,CAAC,GAAG;AAC5C,yBAAe,CAAC,IAAI;AACpB,kBAAQ;AACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC;AACH,cAAM,KAAK,GAAG;AAAA,IAClB;AACA,UAAM,UAAU,QAAQ,OAAO,CAAC,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC;AAC5D,OAAG,SAAS,SAAS,OAAO,SAAS,SAAS;AAC9C,cAAU,CAAC,GAAG,OAAO;AAAA,EACvB,GAAG,OAAO;AACZ;AAEA,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,cAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,gBAAgB,QAAQ,IAAI,UAAU,CAAC,GAAG;AACjD,QAAMA,MAAK,SAAS;AAAA,IAClB,cAAc;AAAA,EAChB,IAAIA,KAAI,eAAe,YAAYA,KAAI;AAAA,IACrC;AAAA,EACF,CAAC;AACD,SAAO,MAAM,QAAQ,oBAAoB,aAAa,EAAE,GAAG,YAAY;AACzE;AAEA,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,cAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,YAAY,QAAQ,IAAI,SAAS;AACxC,QAAMA,MAAK,SAAS;AAAA,IAClB;AAAA,EACF,IAAIA,KAAI,eAAe,YAAYA,KAAI;AAAA,IACrC;AAAA,EACF,CAAC;AACD,QAAM,UAAU,IAAI,CAAC;AACrB,QAAM,OAAO,gBAAgB,QAAQ,IAAI,SAAS;AAChD,YAAQ,SAAS;AACjB,QAAI,QAAQ,SAAS,aAAa,KAAK;AACrC,eAAS,MAAM,KAAK,CAAC;AACvB,OAAG,GAAG,IAAI;AAAA,EACZ,GAAG,YAAY;AACf,SAAO,EAAE,OAAO,SAAS,KAAK;AAChC;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,IAAI,cAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,IAAI,UAAU,CAAC,GAAG;AAChD,QAAMA,MAAK,SAAS;AAAA,IAClB,WAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAIA,KAAI,eAAe,YAAYA,KAAI;AAAA,IACrC;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,gBAAgB,QAAQ,IAAI,gBAAgB,iBAAiB,CAAC,GAAG,YAAY,GAAG;AAAA,IACrF,aAAa,eAAe,UAAU,EAAE,QAAQ,CAAC;AAAA,EACnD,CAAC,CAAC;AACJ;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,IAAI,cAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,IAAI,UAAU,CAAC,GAAG;AAChD,QAAMA,MAAK,SAAS;AAAA,IAClB,cAAc;AAAA,EAChB,IAAIA,KAAI,eAAe,YAAYA,KAAI;AAAA,IACrC;AAAA,EACF,CAAC;AACD,QAAM,aAAa,oBAAoB,aAAa,EAAE;AACtD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa,UAAU,QAAQ;AACjC,UAAM,SAAS,IAAI,KAAK;AACxB,6BAAyB,MAAM;AAAA,IAC/B;AACA,oBAAgB,CAAC,YAAY;AAC3B,aAAO,QAAQ;AACf,cAAQ;AACR,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO,MAAM,QAAQ,IAAI,SAAS;AAChC,UAAI,CAAC,OAAO;AACV,mBAAW,GAAG,IAAI;AAAA,IACtB,GAAG,YAAY;AAAA,EACjB,OAAO;AACL,UAAM,cAAc,CAAC;AACrB,UAAM,gBAAgB,IAAI,CAAC;AAC3B,UAAM,cAAc,IAAI,CAAC;AACzB,6BAAyB,MAAM;AAC7B,oBAAc,QAAQ,YAAY;AAAA,IACpC;AACA,gBAAY,KAAK,MAAM,QAAQ,MAAM;AACnC,kBAAY;AAAA,IACd,GAAG,gBAAgB,iBAAiB,CAAC,GAAG,YAAY,GAAG,EAAE,OAAO,OAAO,CAAC,CAAC,CAAC;AAC1E,oBAAgB,CAAC,YAAY;AAC3B,YAAM,kBAAkB,YAAY;AACpC,cAAQ;AACR,oBAAc,SAAS,YAAY,QAAQ;AAAA,IAC7C;AACA,gBAAY,KAAK,MAAM,QAAQ,IAAI,SAAS;AAC1C,YAAM,SAAS,cAAc,QAAQ,KAAK,cAAc,UAAU,YAAY;AAC9E,oBAAc,QAAQ;AACtB,kBAAY,QAAQ;AACpB,UAAI;AACF;AACF,iBAAW,GAAG,IAAI;AAAA,IACpB,GAAG,YAAY,CAAC;AAChB,WAAO,MAAM;AACX,kBAAY,QAAQ,CAAC,OAAO,GAAG,CAAC;AAAA,IAClC;AAAA,EACF;AACA,SAAO,EAAE,MAAM,eAAe,uBAAuB;AACvD;AAEA,SAAS,UAAU,QAAQ,IAAI,SAAS;AACtC,QAAM,OAAO,MAAM,QAAQ,IAAI,SAAS;AACtC,aAAS,MAAM,KAAK,CAAC;AACrB,WAAO,GAAG,GAAG,IAAI;AAAA,EACnB,GAAG,OAAO;AACZ;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,IAAI,cAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,cAAc,QAAQ,IAAI,UAAU,CAAC,GAAG;AAC/C,QAAMA,MAAK,SAAS;AAAA,IAClB,aAAa;AAAA,EACf,IAAIA,KAAI,eAAe,YAAYA,KAAI;AAAA,IACrC;AAAA,EACF,CAAC;AACD,QAAM,EAAE,aAAa,OAAO,QAAQ,SAAS,IAAI,eAAe,MAAM;AACtE,QAAM,OAAO,gBAAgB,QAAQ,IAAI,gBAAgB,iBAAiB,CAAC,GAAG,YAAY,GAAG;AAAA,IAC3F;AAAA,EACF,CAAC,CAAC;AACF,SAAO,EAAE,MAAM,OAAO,QAAQ,SAAS;AACzC;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,IAAI,YAAY,CAAC,QAAQ,YAAY;AACnC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,IAAI,UAAU,CAAC,GAAG;AAChD,QAAMA,MAAK,SAAS;AAAA,IAClB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAIA,KAAI,eAAe,UAAUA,KAAI;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,gBAAgB,QAAQ,IAAI,gBAAgB,iBAAiB,CAAC,GAAG,YAAY,GAAG;AAAA,IACrF,aAAa,eAAe,UAAU,UAAU,OAAO;AAAA,EACzD,CAAC,CAAC;AACJ;AAEA,IAAI,YAAY,OAAO;AACvB,IAAI,aAAa,OAAO;AACxB,IAAI,oBAAoB,OAAO;AAC/B,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,sBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AACpC,MAAI;AACF,aAAS,QAAQ,oBAAoB,CAAC,GAAG;AACvC,UAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,wBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACpC;AACF,SAAO;AACT;AACA,IAAI,gBAAgB,CAAC,GAAG,MAAM,WAAW,GAAG,kBAAkB,CAAC,CAAC;AAChE,SAAS,iBAAiB,QAAQ,IAAI,UAAU,CAAC,GAAG;AAClD,MAAI;AACJ,WAAS,WAAW;AAClB,QAAI,CAAC;AACH;AACF,UAAM,KAAK;AACX,gBAAY;AACZ,OAAG;AAAA,EACL;AACA,WAAS,UAAU,UAAU;AAC3B,gBAAY;AAAA,EACd;AACA,QAAM,MAAM,CAAC,OAAO,aAAa;AAC/B,aAAS;AACT,WAAO,GAAG,OAAO,UAAU,SAAS;AAAA,EACtC;AACA,QAAM,MAAM,eAAe,QAAQ,KAAK,OAAO;AAC/C,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,UAAU,MAAM;AACpB,QAAI;AACJ,kBAAc,MAAM;AAClB,aAAO,IAAI,gBAAgB,MAAM,GAAG,YAAY,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,WAAO;AAAA,EACT;AACA,SAAO,cAAc,eAAe,CAAC,GAAG,GAAG,GAAG;AAAA,IAC5C;AAAA,EACF,CAAC;AACH;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,WAAW,OAAO;AACpB,WAAO;AACT,MAAI,MAAM,QAAQ,OAAO;AACvB,WAAO,QAAQ,IAAI,CAAC,SAAS,kBAAkB,IAAI,CAAC;AACtD,SAAO,kBAAkB,OAAO;AAClC;AACA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,OAAO,WAAW,aAAa,OAAO,IAAI,MAAM,MAAM;AAC/D;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,MAAM,QAAQ,MAAM,IAAI,OAAO,IAAI,MAAM,MAAM,IAAI;AAC5D;AAEA,SAAS,SAAS,QAAQ,IAAI,SAAS;AACrC,SAAO,MAAM,QAAQ,CAAC,GAAG,IAAI,iBAAiB;AAC5C,QAAI;AACF,SAAG,GAAG,IAAI,YAAY;AAAA,EAC1B,GAAG,OAAO;AACZ;;;AC1hDA,SAAS,cAAc,oBAAoB,cAAc,cAAc;AACrE,MAAI;AACJ,MAAI,MAAM,YAAY,GAAG;AACvB,cAAU;AAAA,MACR,YAAY;AAAA,IACd;AAAA,EACF,OAAO;AACL,cAAU,gBAAgB,CAAC;AAAA,EAC7B;AACA,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,UAAU,IAAI,CAAC,IAAI;AACzB,QAAM,UAAU,UAAU,WAAW,YAAY,IAAI,IAAI,YAAY;AACrE,MAAI,UAAU;AACd,cAAY,OAAO,iBAAiB;AAClC,QAAI,CAAC,QAAQ;AACX;AACF;AACA,UAAM,qBAAqB;AAC3B,QAAI,cAAc;AAClB,QAAI,YAAY;AACd,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,mBAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,QAAI;AACF,YAAM,SAAS,MAAM,mBAAmB,CAAC,mBAAmB;AAC1D,qBAAa,MAAM;AACjB,cAAI;AACF,uBAAW,QAAQ;AACrB,cAAI,CAAC;AACH,2BAAe;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AACD,UAAI,uBAAuB;AACzB,gBAAQ,QAAQ;AAAA,IACpB,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX,UAAE;AACA,UAAI,cAAc,uBAAuB;AACvC,mBAAW,QAAQ;AACrB,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,MAAI,MAAM;AACR,WAAO,SAAS,MAAM;AACpB,cAAQ,QAAQ;AAChB,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,KAAK,SAAS,eAAe,uBAAuB;AAC1E,MAAI,SAAS,OAAO,GAAG;AACvB,MAAI;AACF,aAAS,OAAO,KAAK,aAAa;AACpC,MAAI;AACF,aAAS,OAAO,KAAK,eAAe,qBAAqB;AAC3D,MAAI,OAAO,YAAY,YAAY;AACjC,WAAO,SAAS,CAAC,QAAQ,QAAQ,QAAQ,GAAG,CAAC;AAAA,EAC/C,OAAO;AACL,WAAO,SAAS;AAAA,MACd,KAAK,CAAC,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AAAA,MACrC,KAAK,QAAQ;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAEA,IAAM,gBAAgB,CAAC,OAAO;AAC5B,SAAO,YAAY,MAAM;AACvB,WAAO,GAAG,MAAM,MAAM,KAAK,IAAI,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,EACjD;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,MAAIO;AACJ,QAAM,QAAQ,aAAa,KAAK;AAChC,UAAQA,MAAK,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAOA,MAAK;AAClE;AAEA,IAAM,gBAAgB,WAAW,SAAS;AAC1C,IAAM,kBAAkB,WAAW,OAAO,WAAW;AACrD,IAAM,mBAAmB,WAAW,OAAO,YAAY;AACvD,IAAM,kBAAkB,WAAW,OAAO,WAAW;AAErD,SAAS,oBAAoB,MAAM;AACjC,MAAI;AACJ,MAAIC;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,SAAS,KAAK,CAAC,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC,CAAC,GAAG;AAC/C,KAACA,SAAQ,WAAW,OAAO,IAAI;AAC/B,aAAS;AAAA,EACX,OAAO;AACL,KAAC,QAAQA,SAAQ,WAAW,OAAO,IAAI;AAAA,EACzC;AACA,MAAI,CAAC;AACH,WAAO;AACT,MAAI,CAAC,MAAM,QAAQA,OAAM;AACvB,IAAAA,UAAS,CAACA,OAAM;AAClB,MAAI,CAAC,MAAM,QAAQ,SAAS;AAC1B,gBAAY,CAAC,SAAS;AACxB,QAAM,WAAW,CAAC;AAClB,QAAM,UAAU,MAAM;AACpB,aAAS,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC7B,aAAS,SAAS;AAAA,EACpB;AACA,QAAM,WAAW,CAAC,IAAI,OAAO,UAAU,aAAa;AAClD,OAAG,iBAAiB,OAAO,UAAU,QAAQ;AAC7C,WAAO,MAAM,GAAG,oBAAoB,OAAO,UAAU,QAAQ;AAAA,EAC/D;AACA,QAAM,YAAY,MAAM,MAAM,CAAC,aAAa,MAAM,GAAG,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,MAAM;AAC/F,YAAQ;AACR,QAAI,CAAC;AACH;AACF,aAAS,KAAK,GAAGA,QAAO,QAAQ,CAAC,UAAU;AACzC,aAAO,UAAU,IAAI,CAAC,aAAa,SAAS,IAAI,OAAO,UAAU,QAAQ,CAAC;AAAA,IAC5E,CAAC,CAAC;AAAA,EACJ,GAAG,EAAE,WAAW,MAAM,OAAO,OAAO,CAAC;AACrC,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,IAAI,iBAAiB;AACrB,SAAS,eAAe,QAAQ,SAAS,UAAU,CAAC,GAAG;AACrD,QAAM,EAAE,QAAAC,UAAS,eAAe,SAAS,CAAC,GAAG,UAAU,MAAM,eAAe,MAAM,IAAI;AACtF,MAAI,CAACA;AACH;AACF,MAAI,SAAS,CAAC,gBAAgB;AAC5B,qBAAiB;AACjB,UAAM,KAAKA,QAAO,SAAS,KAAK,QAAQ,EAAE,QAAQ,CAAC,OAAO,GAAG,iBAAiB,SAAS,IAAI,CAAC;AAAA,EAC9F;AACA,MAAI,eAAe;AACnB,QAAM,eAAe,CAAC,UAAU;AAC9B,WAAO,OAAO,KAAK,CAAC,YAAY;AAC9B,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO,MAAM,KAAKA,QAAO,SAAS,iBAAiB,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,EAAE,CAAC;AAAA,MACpI,OAAO;AACL,cAAM,KAAK,aAAa,OAAO;AAC/B,eAAO,OAAO,MAAM,WAAW,MAAM,MAAM,aAAa,EAAE,SAAS,EAAE;AAAA,MACvE;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,WAAW,CAAC,UAAU;AAC1B,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC,MAAM,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,EAAE;AAChE;AACF,QAAI,MAAM,WAAW;AACnB,qBAAe,CAAC,aAAa,KAAK;AACpC,QAAI,CAAC,cAAc;AACjB,qBAAe;AACf;AAAA,IACF;AACA,YAAQ,KAAK;AAAA,EACf;AACA,QAAM,UAAU;AAAA,IACd,iBAAiBA,SAAQ,SAAS,UAAU,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,IACtE,iBAAiBA,SAAQ,eAAe,CAAC,MAAM;AAC7C,YAAM,KAAK,aAAa,MAAM;AAC9B,UAAI;AACF,uBAAe,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,aAAa,CAAC;AAAA,IACpE,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,IACpB,gBAAgB,iBAAiBA,SAAQ,QAAQ,CAAC,UAAU;AAC1D,UAAIF;AACJ,YAAM,KAAK,aAAa,MAAM;AAC9B,YAAMA,MAAKE,QAAO,SAAS,kBAAkB,OAAO,SAASF,IAAG,aAAa,YAAY,EAAE,MAAM,OAAO,SAAS,GAAG,SAASE,QAAO,SAAS,aAAa;AACxJ,gBAAQ,KAAK;AAAA,IACjB,CAAC;AAAA,EACH,EAAE,OAAO,OAAO;AAChB,QAAM,OAAO,MAAM,QAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC/C,SAAO;AACT;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,IAAM,qBAAqB,CAAC,cAAc;AACxC,MAAI,OAAO,cAAc;AACvB,WAAO;AAAA,WACA,OAAO,cAAc;AAC5B,WAAO,CAAC,UAAU,MAAM,QAAQ;AAAA,WACzB,MAAM,QAAQ,SAAS;AAC9B,WAAO,CAAC,UAAU,UAAU,SAAS,MAAM,GAAG;AAChD,SAAO,MAAM;AACf;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,KAAK,CAAC;AACZ,cAAU,KAAK,CAAC;AAChB,cAAU,KAAK,CAAC;AAAA,EAClB,WAAW,KAAK,WAAW,GAAG;AAC5B,QAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,YAAM;AACN,gBAAU,KAAK,CAAC;AAChB,gBAAU,KAAK,CAAC;AAAA,IAClB,OAAO;AACL,YAAM,KAAK,CAAC;AACZ,gBAAU,KAAK,CAAC;AAAA,IAClB;AAAA,EACF,OAAO;AACL,UAAM;AACN,cAAU,KAAK,CAAC;AAAA,EAClB;AACA,QAAM,EAAE,SAAS,eAAe,YAAY,WAAW,UAAU,MAAM,IAAI;AAC3E,QAAM,YAAY,mBAAmB,GAAG;AACxC,QAAM,WAAW,CAAC,MAAM;AACtB,QAAI,UAAU,CAAC;AACb,cAAQ,CAAC;AAAA,EACb;AACA,SAAO,iBAAiB,QAAQ,WAAW,UAAU,OAAO;AAC9D;AACA,SAAS,UAAU,KAAK,SAAS,UAAU,CAAC,GAAG;AAC7C,SAAO,YAAY,KAAK,SAAS,gBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG,EAAE,WAAW,UAAU,CAAC,CAAC;AAC3G;AACA,SAAS,aAAa,KAAK,SAAS,UAAU,CAAC,GAAG;AAChD,SAAO,YAAY,KAAK,SAAS,gBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG,EAAE,WAAW,WAAW,CAAC,CAAC;AAC5G;AACA,SAAS,QAAQ,KAAK,SAAS,UAAU,CAAC,GAAG;AAC3C,SAAO,YAAY,KAAK,SAAS,gBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG,EAAE,WAAW,QAAQ,CAAC,CAAC;AACzG;AAEA,IAAM,gBAAgB;AACtB,SAAS,YAAY,QAAQ,SAAS,SAAS;AAC7C,MAAIF,KAAI;AACR,QAAM,aAAa,SAAS,MAAM,aAAa,MAAM,CAAC;AACtD,MAAI;AACJ,WAAS,QAAQ;AACf,QAAI,SAAS;AACX,mBAAa,OAAO;AACpB,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,WAAS,OAAO,IAAI;AAClB,QAAIG,MAAK,KAAK,IAAI;AAClB,UAAMA,OAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAASA,KAAI,SAAS,GAAG,WAAW,WAAW;AACjH;AACF,UAAM;AACN,SAAK,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI;AAC9E,SAAG,eAAe;AACpB,SAAK,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAC5E,SAAG,gBAAgB;AACrB,cAAU,WAAW,MAAM,QAAQ,EAAE,IAAI,KAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAO,KAAK,aAAa;AAAA,EACtH;AACA,QAAM,kBAAkB;AAAA,IACtB,UAAUH,MAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAASA,IAAG;AAAA,IACnF,OAAO,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAAA,EAClF;AACA,mBAAiB,YAAY,eAAe,QAAQ,eAAe;AACnE,mBAAiB,YAAY,aAAa,OAAO,eAAe;AAChE,mBAAiB,YAAY,gBAAgB,OAAO,eAAe;AACrE;AAEA,IAAM,2BAA2B,MAAM;AACrC,QAAM,EAAE,eAAe,KAAK,IAAI;AAChC,MAAI,CAAC;AACH,WAAO;AACT,MAAI,kBAAkB;AACpB,WAAO;AACT,UAAQ,cAAc,SAAS;AAAA,IAC7B,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AACA,SAAO,cAAc,aAAa,iBAAiB;AACrD;AACA,IAAM,mBAAmB,CAAC;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,WAAW,WAAW;AACxB,WAAO;AACT,MAAI,WAAW,MAAM,WAAW,MAAM,WAAW,MAAM,WAAW;AAChE,WAAO;AACT,MAAI,WAAW,MAAM,WAAW;AAC9B,WAAO;AACT,SAAO;AACT;AACA,SAAS,cAAc,UAAU,UAAU,CAAC,GAAG;AAC7C,QAAM,EAAE,UAAU,YAAY,gBAAgB,IAAI;AAClD,QAAM,UAAU,CAAC,UAAU;AACzB,KAAC,yBAAyB,KAAK,iBAAiB,KAAK,KAAK,SAAS,KAAK;AAAA,EAC1E;AACA,MAAI;AACF,qBAAiB,WAAW,WAAW,SAAS,EAAE,SAAS,KAAK,CAAC;AACrE;AAEA,SAAS,YAAY,KAAK,eAAe,MAAM;AAC7C,QAAM,WAAW,mBAAmB;AACpC,MAAI,WAAW,MAAM;AAAA,EACrB;AACA,QAAM,UAAU,UAAU,CAAC,OAAO,YAAY;AAC5C,eAAW;AACX,WAAO;AAAA,MACL,MAAM;AACJ,YAAIA,KAAI;AACR,cAAM;AACN,gBAAQ,MAAMA,MAAK,YAAY,OAAO,SAAS,SAAS,UAAU,OAAO,SAASA,IAAG,MAAM,GAAG,MAAM,OAAO,KAAK;AAAA,MAClH;AAAA,MACA,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF,CAAC;AACD,eAAa,QAAQ;AACrB,YAAU,QAAQ;AAClB,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,MAAIA;AACJ,QAAM,EAAE,QAAAE,UAAS,cAAc,IAAI;AACnC,QAAME,aAAYJ,MAAK,QAAQ,aAAa,OAAOA,MAAKE,WAAU,OAAO,SAASA,QAAO;AACzF,QAAM,gBAAgB,oBAAoB,MAAM,MAAM,MAAME,aAAY,OAAO,SAASA,UAAS,aAAa;AAC9G,MAAIF,SAAQ;AACV,qBAAiBA,SAAQ,QAAQ,CAAC,UAAU;AAC1C,UAAI,MAAM,kBAAkB;AAC1B;AACF,oBAAc,QAAQ;AAAA,IACxB,GAAG,IAAI;AACP,qBAAiBA,SAAQ,SAAS,cAAc,SAAS,IAAI;AAAA,EAC/D;AACA,SAAO;AACT;AAEA,SAAS,cAAc,OAAO,UAAU,CAAC,GAAG;AAC1C,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,aAAa;AAAA,EACf,IAAI;AACJ,QAAM,eAAe;AAAA,IACnB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AACA,QAAM,gBAAgB,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,OAAO,EAAE,OAAO,aAAa,SAAS,MAAM,KAAK,EAAE;AAC7G,QAAM,SAAS,SAAS,aAAa;AACrC,QAAM,cAAc,IAAI,EAAE;AAC1B,MAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC,eAAW;AACX,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,WAAS,aAAa,OAAO,KAAK;AAChC,gBAAY;AACZ,WAAO,YAAY,KAAK,EAAE,OAAO;AACjC,WAAO,YAAY,KAAK,EAAE,QAAQ;AAAA,EACpC;AACA,QAAM,OAAO,CAAC,MAAM,SAAS;AAC3B,WAAO,KAAK,KAAK,CAAC,YAAY;AAC5B,UAAIF;AACJ,YAAMA,MAAK,OAAO,YAAY,KAAK,MAAM,OAAO,SAASA,IAAG,WAAW,aAAa,YAAY,WAAW;AACzG,mBAAW;AACX;AAAA,MACF;AACA,aAAO,KAAK,OAAO,EAAE,KAAK,CAAC,eAAe;AACxC,qBAAa,aAAa,WAAW,UAAU;AAC/C,oBAAY,UAAU,MAAM,SAAS,KAAK,WAAW;AACrD,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,mBAAa,aAAa,UAAU,CAAC;AACrC,cAAQ;AACR,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,QAAQ,QAAQ,CAAC;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,cAAc,SAAS,cAAc,SAAS;AACrD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV;AAAA,EACF,IAAI,WAAW,OAAO,UAAU,CAAC;AACjC,QAAM,QAAQ,UAAU,WAAW,YAAY,IAAI,IAAI,YAAY;AACnE,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,YAAY,IAAI,KAAK;AAC3B,QAAM,QAAQ,IAAI,MAAM;AACxB,iBAAe,QAAQ,SAAS,MAAM,MAAM;AAC1C,QAAI;AACF,YAAM,QAAQ;AAChB,UAAM,QAAQ;AACd,YAAQ,QAAQ;AAChB,cAAU,QAAQ;AAClB,QAAI,SAAS;AACX,YAAM,eAAe,MAAM;AAC7B,UAAM,WAAW,OAAO,YAAY,aAAa,QAAQ,GAAG,IAAI,IAAI;AACpE,QAAI;AACF,YAAM,OAAO,MAAM;AACnB,YAAM,QAAQ;AACd,cAAQ,QAAQ;AAChB,gBAAU,IAAI;AAAA,IAChB,SAAS,GAAG;AACV,YAAM,QAAQ;AACd,cAAQ,CAAC;AACT,UAAI;AACF,cAAM;AAAA,IACV,UAAE;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO,MAAM;AAAA,EACf;AACA,MAAI;AACF,YAAQ,KAAK;AACf,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,WAAW;AAAA,EACf,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,EAC9B,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,EAC/B,KAAK,CAAC,MAAM,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC;AAAA,EACxC,KAAK,CAAC,MAAM,KAAK,UAAU,OAAO,YAAY,CAAC,CAAC;AAAA,EAChD,MAAM,MAAM;AACd;AACA,SAAS,wBAAwB,QAAQ;AACvC,MAAI,CAAC;AACH,WAAO,SAAS;AAClB,MAAI,kBAAkB;AACpB,WAAO,SAAS;AAAA,WACT,kBAAkB;AACzB,WAAO,SAAS;AAAA,WACT,MAAM,QAAQ,MAAM;AAC3B,WAAO,SAAS;AAAA;AAEhB,WAAO,SAAS;AACpB;AAEA,SAAS,UAAU,QAAQ,SAAS;AAClC,QAAM,SAAS,IAAI,EAAE;AACrB,QAAM,UAAU,IAAI;AACpB,WAAS,UAAU;AACjB,QAAI,CAAC;AACH;AACF,YAAQ,QAAQ,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,UAAI;AACF,cAAM,UAAU,aAAa,MAAM;AACnC,YAAI,WAAW,MAAM;AACnB,kBAAQ,EAAE;AAAA,QACZ,WAAW,OAAO,YAAY,UAAU;AACtC,kBAAQ,aAAa,IAAI,KAAK,CAAC,OAAO,GAAG,EAAE,MAAM,aAAa,CAAC,CAAC,CAAC;AAAA,QACnE,WAAW,mBAAmB,MAAM;AAClC,kBAAQ,aAAa,OAAO,CAAC;AAAA,QAC/B,WAAW,mBAAmB,aAAa;AACzC,kBAAQ,OAAO,KAAK,OAAO,aAAa,GAAG,IAAI,WAAW,OAAO,CAAC,CAAC,CAAC;AAAA,QACtE,WAAW,mBAAmB,mBAAmB;AAC/C,kBAAQ,QAAQ,UAAU,WAAW,OAAO,SAAS,QAAQ,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO,CAAC;AAAA,QAChH,WAAW,mBAAmB,kBAAkB;AAC9C,gBAAM,MAAM,QAAQ,UAAU,KAAK;AACnC,cAAI,cAAc;AAClB,oBAAU,GAAG,EAAE,KAAK,MAAM;AACxB,kBAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,kBAAM,MAAM,OAAO,WAAW,IAAI;AAClC,mBAAO,QAAQ,IAAI;AACnB,mBAAO,SAAS,IAAI;AACpB,gBAAI,UAAU,KAAK,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AACpD,oBAAQ,OAAO,UAAU,WAAW,OAAO,SAAS,QAAQ,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO,CAAC;AAAA,UAC/G,CAAC,EAAE,MAAM,MAAM;AAAA,QACjB,WAAW,OAAO,YAAY,UAAU;AACtC,gBAAM,gBAAgB,WAAW,OAAO,SAAS,QAAQ,eAAe,wBAAwB,OAAO;AACvG,gBAAM,aAAa,aAAa,OAAO;AACvC,iBAAO,QAAQ,aAAa,IAAI,KAAK,CAAC,UAAU,GAAG,EAAE,MAAM,mBAAmB,CAAC,CAAC,CAAC;AAAA,QACnF,OAAO;AACL,iBAAO,IAAI,MAAM,6BAA6B,CAAC;AAAA,QACjD;AAAA,MACF,SAAS,OAAO;AACd,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AACD,YAAQ,MAAM,KAAK,CAAC,QAAQ,OAAO,QAAQ,GAAG;AAC9C,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,MAAM,MAAM,KAAK,WAAW,MAAM;AACpC,UAAM,QAAQ,SAAS,EAAE,WAAW,KAAK,CAAC;AAAA;AAE1C,YAAQ;AACV,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,CAAC,IAAI,UAAU;AACjB,UAAI,SAAS,MAAM;AACjB,gBAAQ;AAAA,MACV;AACA,UAAI,UAAU;AAAA,IAChB,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,KAAK,IAAI,WAAW;AAC1B,OAAG,SAAS,CAAC,MAAM;AACjB,cAAQ,EAAE,OAAO,MAAM;AAAA,IACzB;AACA,OAAG,UAAU;AACb,OAAG,cAAc,IAAI;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,aAAa,UAAU,OAAO,OAAO;AAC5C,QAAM,cAAc,IAAI;AACxB,QAAM,SAAS,MAAM,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAC3D,SAAO;AACP,eAAa,QAAQ,IAAI;AACzB,SAAO;AACT;AAEA,SAAS,WAAW,EAAE,YAAY,iBAAiB,IAAI,CAAC,GAAG;AACzD,QAAMC,UAAS,CAAC,kBAAkB,sBAAsB,yBAAyB,aAAa;AAC9F,QAAM,cAAc,aAAa,MAAM,aAAa,gBAAgB,SAAS;AAC7E,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,eAAe,IAAI,CAAC;AAC1B,QAAM,kBAAkB,IAAI,CAAC;AAC7B,QAAM,QAAQ,IAAI,CAAC;AACnB,MAAI;AACJ,WAAS,oBAAoB;AAC3B,aAAS,QAAQ,KAAK;AACtB,iBAAa,QAAQ,KAAK,gBAAgB;AAC1C,oBAAgB,QAAQ,KAAK,mBAAmB;AAChD,UAAM,QAAQ,KAAK;AAAA,EACrB;AACA,MAAI,YAAY,OAAO;AACrB,cAAU,WAAW,EAAE,KAAK,CAAC,aAAa;AACxC,gBAAU;AACV,wBAAkB,KAAK,OAAO;AAC9B,iBAAW,SAASA;AAClB,yBAAiB,SAAS,OAAO,mBAAmB,EAAE,SAAS,KAAK,CAAC;AAAA,IACzE,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,aAAa,SAAS;AAC7B,MAAI;AAAA,IACF,mBAAmB;AAAA,EACrB,IAAI,WAAW,CAAC;AAChB,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,YAAY;AAAA,EACd,IAAI,WAAW,CAAC;AAChB,QAAM,cAAc,aAAa,MAAM,aAAa,eAAe,SAAS;AAC5E,QAAM,SAAS,WAAW,MAAM;AAChC,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,QAAQ,MAAM;AAClB,iCAA6B;AAAA,EAC/B,CAAC;AACD,iBAAe,gBAAgB;AAC7B,QAAI,CAAC,YAAY;AACf;AACF,UAAM,QAAQ;AACd,QAAI,WAAW,QAAQ,SAAS;AAC9B,yBAAmB;AACrB,QAAI;AACF,aAAO,QAAQ,OAAO,aAAa,OAAO,SAAS,UAAU,UAAU,cAAc;AAAA,QACnF;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,SAAS,KAAK;AACZ,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AACA,QAAM,SAAS,IAAI;AACnB,QAAM,cAAc,SAAS,MAAM;AACjC,QAAID;AACJ,aAASA,MAAK,OAAO,UAAU,OAAO,SAASA,IAAG,cAAc;AAAA,EAClE,CAAC;AACD,iBAAe,+BAA+B;AAC5C,UAAM,QAAQ;AACd,QAAI,OAAO,SAAS,OAAO,MAAM,MAAM;AACrC,aAAO,MAAM,iBAAiB,0BAA0B,MAAM;AAAA,MAC9D,CAAC;AACD,UAAI;AACF,eAAO,QAAQ,MAAM,OAAO,MAAM,KAAK,QAAQ;AAAA,MACjD,SAAS,KAAK;AACZ,cAAM,QAAQ;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,eAAa,MAAM;AACjB,QAAIA;AACJ,QAAI,OAAO;AACT,OAACA,MAAK,OAAO,MAAM,SAAS,OAAO,SAASA,IAAG,QAAQ;AAAA,EAC3D,CAAC;AACD,oBAAkB,MAAM;AACtB,QAAIA;AACJ,QAAI,OAAO;AACT,OAACA,MAAK,OAAO,MAAM,SAAS,OAAO,SAASA,IAAG,WAAW;AAAA,EAC9D,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,cAAc,OAAO,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,QAAAE,UAAS,cAAc,IAAI;AACnC,QAAM,cAAc,aAAa,MAAMA,WAAU,gBAAgBA,WAAU,OAAOA,QAAO,eAAe,UAAU;AAClH,MAAI;AACJ,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,UAAU,MAAM;AACpB,QAAI,CAAC;AACH;AACF,QAAI,yBAAyB;AAC3B,iBAAW,oBAAoB,UAAU,MAAM;AAAA;AAE/C,iBAAW,eAAe,MAAM;AAAA,EACpC;AACA,QAAM,SAAS,MAAM;AACnB,QAAI,CAAC,YAAY;AACf;AACF,YAAQ;AACR,iBAAaA,QAAO,WAAW,WAAW,KAAK,EAAE,KAAK;AACtD,YAAQ,QAAQ,WAAW;AAC3B,QAAI,sBAAsB;AACxB,iBAAW,iBAAiB,UAAU,MAAM;AAAA;AAE5C,iBAAW,YAAY,MAAM;AAAA,EACjC;AACA,cAAY,MAAM;AAClB,oBAAkB,MAAM,QAAQ,CAAC;AACjC,SAAO;AACT;AAEA,IAAM,sBAAsB;AAAA,EAC1B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,yBAAyB;AAAA,EAC7B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AACA,IAAM,qBAAqB;AAAA,EACzB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,uBAAuB;AAAA,EAC3B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AACA,IAAM,oBAAoB;AAAA,EACxB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AACb;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,eAAe,aAAa,UAAU,CAAC,GAAG;AACjD,WAASG,UAAS,GAAG,OAAO;AAC1B,QAAI,IAAI,YAAY,CAAC;AACrB,QAAI,SAAS;AACX,UAAI,iBAAiB,GAAG,KAAK;AAC/B,QAAI,OAAO,MAAM;AACf,UAAI,GAAG,CAAC;AACV,WAAO;AAAA,EACT;AACA,QAAM,EAAE,QAAAH,UAAS,cAAc,IAAI;AACnC,WAAS,MAAM,OAAO;AACpB,QAAI,CAACA;AACH,aAAO;AACT,WAAOA,QAAO,WAAW,KAAK,EAAE;AAAA,EAClC;AACA,QAAM,iBAAiB,CAAC,MAAM;AAC5B,WAAO,cAAc,eAAeG,UAAS,CAAC,CAAC,KAAK,OAAO;AAAA,EAC7D;AACA,QAAM,kBAAkB,OAAO,KAAK,WAAW,EAAE,OAAO,CAAC,WAAW,MAAM;AACxE,WAAO,eAAe,WAAW,GAAG;AAAA,MAClC,KAAK,MAAM,eAAe,CAAC;AAAA,MAC3B,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,iBAAiB;AAAA,IACtB,QAAQ,GAAG;AACT,aAAO,cAAc,eAAeA,UAAS,GAAG,GAAG,CAAC,KAAK,OAAO;AAAA,IAClE;AAAA,IACA;AAAA,IACA,QAAQ,GAAG;AACT,aAAO,cAAc,eAAeA,UAAS,GAAG,IAAI,CAAC,KAAK,OAAO;AAAA,IACnE;AAAA,IACA,eAAe,GAAG;AAChB,aAAO,cAAc,eAAeA,UAAS,CAAC,CAAC,KAAK,OAAO;AAAA,IAC7D;AAAA,IACA,QAAQ,GAAG,GAAG;AACZ,aAAO,cAAc,eAAeA,UAAS,CAAC,CAAC,qBAAqBA,UAAS,GAAG,IAAI,CAAC,KAAK,OAAO;AAAA,IACnG;AAAA,IACA,UAAU,GAAG;AACX,aAAO,MAAM,eAAeA,UAAS,GAAG,GAAG,CAAC,GAAG;AAAA,IACjD;AAAA,IACA,iBAAiB,GAAG;AAClB,aAAO,MAAM,eAAeA,UAAS,CAAC,CAAC,GAAG;AAAA,IAC5C;AAAA,IACA,UAAU,GAAG;AACX,aAAO,MAAM,eAAeA,UAAS,GAAG,IAAI,CAAC,GAAG;AAAA,IAClD;AAAA,IACA,iBAAiB,GAAG;AAClB,aAAO,MAAM,eAAeA,UAAS,CAAC,CAAC,GAAG;AAAA,IAC5C;AAAA,IACA,YAAY,GAAG,GAAG;AAChB,aAAO,MAAM,eAAeA,UAAS,CAAC,CAAC,qBAAqBA,UAAS,GAAG,IAAI,CAAC,GAAG;AAAA,IAClF;AAAA,EACF,GAAG,eAAe;AACpB;AAEA,IAAM,sBAAsB,CAAC,YAAY;AACvC,QAAM;AAAA,IACJ;AAAA,IACA,QAAAH,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,sBAAsBA,OAAM;AAC7E,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,UAAU,IAAI;AACpB,QAAM,OAAO,IAAI;AACjB,QAAM,QAAQ,IAAI,IAAI;AACtB,QAAM,OAAO,CAAC,UAAU;AACtB,QAAI,QAAQ;AACV,cAAQ,MAAM,YAAY,KAAK;AAAA,EACnC;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,QAAQ;AACV,cAAQ,MAAM,MAAM;AACtB,aAAS,QAAQ;AAAA,EACnB;AACA,MAAI,YAAY,OAAO;AACrB,iBAAa,MAAM;AACjB,YAAM,QAAQ;AACd,cAAQ,QAAQ,IAAI,iBAAiB,IAAI;AACzC,cAAQ,MAAM,iBAAiB,WAAW,CAAC,MAAM;AAC/C,aAAK,QAAQ,EAAE;AAAA,MACjB,GAAG,EAAE,SAAS,KAAK,CAAC;AACpB,cAAQ,MAAM,iBAAiB,gBAAgB,CAAC,MAAM;AACpD,cAAM,QAAQ;AAAA,MAChB,GAAG,EAAE,SAAS,KAAK,CAAC;AACpB,cAAQ,MAAM,iBAAiB,SAAS,MAAM;AAC5C,iBAAS,QAAQ;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,oBAAkB,MAAM;AACtB,UAAM;AAAA,EACR,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,EAAE,QAAAA,UAAS,cAAc,IAAI,CAAC,GAAG;AAC3D,QAAM,aAAa,CAAC,YAAY;AAC9B,UAAM,EAAE,OAAO,QAAQ,OAAO,KAAKA,WAAU,OAAO,SAASA,QAAO,YAAY,CAAC;AACjF,UAAM,EAAE,MAAM,MAAM,UAAU,MAAM,QAAQ,UAAU,MAAM,UAAU,OAAO,KAAKA,WAAU,OAAO,SAASA,QAAO,aAAa,CAAC;AACjI,WAAO;AAAA,MACL;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,IAAI,WAAW,MAAM,CAAC;AACpC,MAAIA,SAAQ;AACV,qBAAiBA,SAAQ,YAAY,MAAM,MAAM,QAAQ,WAAW,UAAU,GAAG,EAAE,SAAS,KAAK,CAAC;AAClG,qBAAiBA,SAAQ,cAAc,MAAM,MAAM,QAAQ,WAAW,YAAY,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,EACxG;AACA,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,aAAa,CAAC,GAAG,MAAM,MAAM,GAAG,cAAc;AACzE,QAAM,cAAc,IAAI,SAAS,KAAK;AACtC,QAAM,MAAM,SAAS,OAAO,CAAC,UAAU;AACrC,QAAI,CAAC,WAAW,OAAO,YAAY,KAAK;AACtC,kBAAY,QAAQ;AAAA,EACxB,GAAG,YAAY;AACf,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,OAAO;AAAA,IACP;AAAA,IACA,eAAe;AAAA,IACf,SAAS;AAAA,EACX,IAAI;AACJ,QAAMD,UAAS,CAAC,QAAQ,KAAK;AAC7B,QAAM,0BAA0B,aAAa,MAAM,aAAa,eAAe,SAAS;AACxF,QAAM,cAAc,SAAS,MAAM,wBAAwB,SAAS,MAAM;AAC1E,QAAM,OAAO,IAAI,EAAE;AACnB,QAAM,SAAS,IAAI,KAAK;AACxB,QAAM,UAAU,aAAa,MAAM,OAAO,QAAQ,OAAO,YAAY;AACrE,WAAS,aAAa;AACpB,QAAI,wBAAwB,OAAO;AACjC,gBAAU,UAAU,SAAS,EAAE,KAAK,CAAC,UAAU;AAC7C,aAAK,QAAQ;AAAA,MACf,CAAC;AAAA,IACH,OAAO;AACL,WAAK,QAAQ,WAAW;AAAA,IAC1B;AAAA,EACF;AACA,MAAI,YAAY,SAAS,MAAM;AAC7B,eAAW,SAASA;AAClB,uBAAiB,OAAO,UAAU;AAAA,EACtC;AACA,iBAAe,KAAK,QAAQ,aAAa,MAAM,GAAG;AAChD,QAAI,YAAY,SAAS,SAAS,MAAM;AACtC,UAAI,wBAAwB;AAC1B,cAAM,UAAU,UAAU,UAAU,KAAK;AAAA;AAEzC,mBAAW,KAAK;AAClB,WAAK,QAAQ;AACb,aAAO,QAAQ;AACf,cAAQ,MAAM;AAAA,IAChB;AAAA,EACF;AACA,WAAS,WAAW,OAAO;AACzB,UAAM,KAAK,SAAS,cAAc,UAAU;AAC5C,OAAG,QAAQ,SAAS,OAAO,QAAQ;AACnC,OAAG,MAAM,WAAW;AACpB,OAAG,MAAM,UAAU;AACnB,aAAS,KAAK,YAAY,EAAE;AAC5B,OAAG,OAAO;AACV,aAAS,YAAY,MAAM;AAC3B,OAAG,OAAO;AAAA,EACZ;AACA,WAAS,aAAa;AACpB,QAAID,KAAI,IAAI;AACZ,YAAQ,MAAM,MAAMA,MAAK,YAAY,OAAO,SAAS,SAAS,iBAAiB,OAAO,SAASA,IAAG,KAAK,QAAQ,MAAM,OAAO,SAAS,GAAG,SAAS,MAAM,OAAO,KAAK;AAAA,EACrK;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,SAAS,YAAY,QAAQ;AAC3B,SAAO,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAC1C;AACA,SAAS,UAAU,QAAQ,UAAU,CAAC,GAAG;AACvC,QAAM,SAAS,IAAI,CAAC,CAAC;AACrB,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,EACd,IAAI;AACJ,WAAS,OAAO;AACd,WAAO,QAAQ,MAAM,MAAM,MAAM,CAAC;AAAA,EACpC;AACA,MAAI,CAAC,UAAU,MAAM,MAAM,GAAG;AAC5B,UAAM,QAAQ,MAAM,gBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG;AAAA,MACjE;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,OAAO;AACL,SAAK;AAAA,EACP;AACA,SAAO,EAAE,QAAQ,KAAK;AACxB;AAEA,IAAM,UAAU,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AACzL,IAAM,YAAY;AAClB,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC5C,IAAM,WAAW,QAAQ,SAAS;AAClC,SAAS,cAAc,KAAK,UAAU;AACpC,SAAO,SAAS,GAAG,KAAK;AAC1B;AACA,SAAS,cAAc,KAAK,IAAI;AAC9B,WAAS,GAAG,IAAI;AAClB;AAEA,SAAS,oBAAoB,SAAS;AACpC,SAAO,WAAW,OAAO,QAAQ,mBAAmB,MAAM,QAAQ,mBAAmB,MAAM,QAAQ,mBAAmB,OAAO,SAAS,OAAO,YAAY,YAAY,YAAY,OAAO,YAAY,WAAW,WAAW,OAAO,YAAY,WAAW,WAAW,CAAC,OAAO,MAAM,OAAO,IAAI,WAAW;AACzS;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,IACP,MAAM,CAAC,MAAM,MAAM;AAAA,IACnB,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;AAAA,IACzB,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,EAChC;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM,OAAO,WAAW,CAAC;AAAA,IAChC,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,IAClC,OAAO,CAAC,MAAM,KAAK,UAAU,MAAM,KAAK,EAAE,QAAQ,CAAC,CAAC;AAAA,EACtD;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,IAClC,OAAO,CAAC,MAAM,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC;AAAA,EAC5C;AAAA,EACA,MAAM;AAAA,IACJ,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC;AAAA,IACvB,OAAO,CAAC,MAAM,EAAE,YAAY;AAAA,EAC9B;AACF;AACA,IAAM,yBAAyB;AAC/B,SAAS,WAAW,KAAKM,WAAU,SAAS,UAAU,CAAC,GAAG;AACxD,MAAIN;AACJ,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,yBAAyB;AAAA,IACzB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB;AAAA,IACA,QAAAE,UAAS;AAAA,IACT;AAAA,IACA,UAAU,CAAC,MAAM;AACf,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,UAAU,aAAa,KAAKI,SAAQ;AAClD,MAAI,CAAC,SAAS;AACZ,QAAI;AACF,gBAAU,cAAc,qBAAqB,MAAM;AACjD,YAAIH;AACJ,gBAAQA,OAAM,kBAAkB,OAAO,SAASA,KAAI;AAAA,MACtD,CAAC,EAAE;AAAA,IACL,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,MAAI,CAAC;AACH,WAAO;AACT,QAAM,UAAU,aAAaG,SAAQ;AACrC,QAAM,OAAO,oBAAoB,OAAO;AACxC,QAAM,cAAcN,MAAK,QAAQ,eAAe,OAAOA,MAAK,mBAAmB,IAAI;AACnF,QAAM,EAAE,OAAO,YAAY,QAAQ,YAAY,IAAI,cAAc,MAAM,MAAM,MAAM,KAAK,KAAK,GAAG,EAAE,OAAO,MAAM,YAAY,CAAC;AAC5H,MAAIE,WAAU,wBAAwB;AACpC,qBAAiBA,SAAQ,WAAW,MAAM;AAC1C,qBAAiBA,SAAQ,wBAAwB,qBAAqB;AAAA,EACxE;AACA,SAAO;AACP,SAAO;AACP,WAAS,MAAM,GAAG;AAChB,QAAI;AACF,UAAI,KAAK,MAAM;AACb,gBAAQ,WAAW,GAAG;AAAA,MACxB,OAAO;AACL,cAAM,aAAa,WAAW,MAAM,CAAC;AACrC,cAAM,WAAW,QAAQ,QAAQ,GAAG;AACpC,YAAI,aAAa,YAAY;AAC3B,kBAAQ,QAAQ,KAAK,UAAU;AAC/B,cAAIA,SAAQ;AACV,YAAAA,QAAO,cAAc,IAAI,YAAY,wBAAwB;AAAA,cAC3D,QAAQ;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,gBACV,aAAa;AAAA,cACf;AAAA,YACF,CAAC,CAAC;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,WAAS,KAAK,OAAO;AACnB,UAAM,WAAW,QAAQ,MAAM,WAAW,QAAQ,QAAQ,GAAG;AAC7D,QAAI,YAAY,MAAM;AACpB,UAAI,iBAAiB,YAAY;AAC/B,gBAAQ,QAAQ,KAAK,WAAW,MAAM,OAAO,CAAC;AAChD,aAAO;AAAA,IACT,WAAW,CAAC,SAAS,eAAe;AAClC,YAAM,QAAQ,WAAW,KAAK,QAAQ;AACtC,UAAI,WAAW,aAAa;AAC1B,eAAO,cAAc,OAAO,OAAO;AAAA,eAC5B,SAAS,YAAY,CAAC,MAAM,QAAQ,KAAK;AAChD,eAAO,iBAAiB,iBAAiB,CAAC,GAAG,OAAO,GAAG,KAAK;AAC9D,aAAO;AAAA,IACT,WAAW,OAAO,aAAa,UAAU;AACvC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,WAAW,KAAK,QAAQ;AAAA,IACjC;AAAA,EACF;AACA,WAAS,sBAAsB,OAAO;AACpC,WAAO,MAAM,MAAM;AAAA,EACrB;AACA,WAAS,OAAO,OAAO;AACrB,QAAI,SAAS,MAAM,gBAAgB;AACjC;AACF,QAAI,SAAS,MAAM,OAAO,MAAM;AAC9B,WAAK,QAAQ;AACb;AAAA,IACF;AACA,QAAI,SAAS,MAAM,QAAQ;AACzB;AACF,eAAW;AACX,QAAI;AACF,WAAK,QAAQ,KAAK,KAAK;AAAA,IACzB,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX,UAAE;AACA,UAAI;AACF,iBAAS,WAAW;AAAA;AAEpB,oBAAY;AAAA,IAChB;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAO,cAAc,gCAAgC,OAAO;AAC9D;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,QAAAA,UAAS;AAAA,IACT;AAAA,IACA,aAAa;AAAA,IACb,yBAAyB;AAAA,IACzB;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,iBAAiB;AAAA,IAC7B,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,EACR,GAAG,QAAQ,SAAS,CAAC,CAAC;AACtB,QAAM,gBAAgB,iBAAiB,EAAE,QAAAA,QAAO,CAAC;AACjD,QAAM,gBAAgB,SAAS,MAAM,cAAc,QAAQ,SAAS,OAAO;AAC3E,QAAM,QAAQ,eAAe,cAAc,OAAO,IAAI,YAAY,IAAI,WAAW,YAAY,cAAc,SAAS,EAAE,QAAAA,SAAQ,uBAAuB,CAAC;AACtJ,QAAM,QAAQ,SAAS;AAAA,IACrB,MAAM;AACJ,aAAO,MAAM,UAAU,UAAU,CAAC,WAAW,cAAc,QAAQ,MAAM;AAAA,IAC3E;AAAA,IACA,IAAI,GAAG;AACL,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF,CAAC;AACD,QAAM,kBAAkB,cAAc,mBAAmB,CAAC,WAAW,YAAY,UAAU;AACzF,UAAM,KAAKA,WAAU,OAAO,SAASA,QAAO,SAAS,cAAc,SAAS;AAC5E,QAAI,CAAC;AACH;AACF,QAAI,eAAe,SAAS;AAC1B,YAAM,UAAU,MAAM,MAAM,KAAK;AACjC,aAAO,OAAO,KAAK,EAAE,QAAQ,CAAC,OAAO,KAAK,IAAI,MAAM,KAAK,CAAC,EAAE,OAAO,OAAO,EAAE,QAAQ,CAAC,MAAM;AACzF,YAAI,QAAQ,SAAS,CAAC;AACpB,aAAG,UAAU,IAAI,CAAC;AAAA;AAElB,aAAG,UAAU,OAAO,CAAC;AAAA,MACzB,CAAC;AAAA,IACH,OAAO;AACL,SAAG,aAAa,YAAY,KAAK;AAAA,IACnC;AAAA,EACF,CAAC;AACD,WAAS,iBAAiB,MAAM;AAC9B,QAAIF;AACJ,UAAM,eAAe,SAAS,SAAS,cAAc,QAAQ;AAC7D,oBAAgB,UAAU,YAAYA,MAAK,MAAM,YAAY,MAAM,OAAOA,MAAK,YAAY;AAAA,EAC7F;AACA,WAAS,UAAU,MAAM;AACvB,QAAI,QAAQ;AACV,cAAQ,UAAU,MAAM,gBAAgB;AAAA;AAExC,uBAAiB,IAAI;AAAA,EACzB;AACA,QAAM,OAAO,WAAW,EAAE,OAAO,QAAQ,WAAW,KAAK,CAAC;AAC1D,MAAI;AACF,UAAM,eAAe,MAAM,UAAU,MAAM,KAAK,GAAG,EAAE,OAAO,OAAO,CAAC;AACtE,eAAa,MAAM,UAAU,MAAM,KAAK,CAAC;AACzC,SAAO;AACT;AAEA,SAAS,iBAAiB,WAAW,IAAI,KAAK,GAAG;AAC/C,QAAM,cAAc,gBAAgB;AACpC,QAAM,aAAa,gBAAgB;AACnC,QAAM,aAAa,gBAAgB;AACnC,MAAI,WAAW;AACf,QAAM,SAAS,CAAC,SAAS;AACvB,eAAW,QAAQ,IAAI;AACvB,aAAS,QAAQ;AACjB,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,iBAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,QAAM,UAAU,CAAC,SAAS;AACxB,aAAS,QAAQ;AACjB,gBAAY,QAAQ,IAAI;AACxB,aAAS,EAAE,MAAM,YAAY,MAAM,CAAC;AAAA,EACtC;AACA,QAAM,SAAS,CAAC,SAAS;AACvB,aAAS,QAAQ;AACjB,eAAW,QAAQ,IAAI;AACvB,aAAS,EAAE,MAAM,YAAY,KAAK,CAAC;AAAA,EACrC;AACA,SAAO;AAAA,IACL,YAAY,SAAS,MAAM,SAAS,KAAK;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,WAAW;AAAA,IACrB,WAAW,YAAY;AAAA,IACvB,UAAU,WAAW;AAAA,EACvB;AACF;AAEA,SAAS,UAAU,MAAM,QAAQ,EAAE,QAAAE,UAAS,eAAe,eAAe,GAAG,IAAI,CAAC,GAAG;AACnF,QAAM,WAAW,IAAI,YAAY;AACjC,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAIF;AACJ,WAAO,aAAa,MAAM,OAAOA,MAAKE,WAAU,OAAO,SAASA,QAAO,aAAa,OAAO,SAASF,IAAG;AAAA,EACzG,CAAC;AACD,QAAM,CAAC,OAAO,MAAM,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,MAAM;AACxD,QAAIA;AACJ,QAAI,MAAME,SAAQ;AAChB,YAAM,SAASF,MAAKE,QAAO,iBAAiB,EAAE,EAAE,iBAAiB,KAAK,MAAM,OAAO,SAASF,IAAG,KAAK;AACpG,eAAS,QAAQ,SAAS;AAAA,IAC5B;AAAA,EACF,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,QAAM,UAAU,CAAC,QAAQ;AACvB,QAAIA;AACJ,SAAKA,MAAK,MAAM,UAAU,OAAO,SAASA,IAAG;AAC3C,YAAM,MAAM,MAAM,YAAY,aAAa,IAAI,GAAG,GAAG;AAAA,EACzD,CAAC;AACD,SAAO;AACT;AAEA,SAAS,oBAAoB;AAC3B,QAAM,KAAK,mBAAmB;AAC9B,QAAM,iBAAiB,oBAAoB,MAAM,MAAM,MAAM,GAAG,MAAM,GAAG;AACzE,YAAU,eAAe,OAAO;AAChC,YAAU,eAAe,OAAO;AAChC,SAAO;AACT;AAEA,SAAS,aAAa,MAAM,SAAS;AACnC,MAAIA;AACJ,QAAM,QAAQ,YAAYA,MAAK,WAAW,OAAO,SAAS,QAAQ,iBAAiB,OAAOA,MAAK,KAAK,CAAC,CAAC;AACtG,QAAM,QAAQ,SAAS;AAAA,IACrB,MAAM;AACJ,UAAIG;AACJ,UAAI,UAAU,WAAW,OAAO,SAAS,QAAQ,cAAc,QAAQ,WAAW,MAAM,OAAO,IAAI,IAAI,KAAK,QAAQ,MAAM,KAAK;AAC/H,UAAI,SAAS;AACX,kBAAUA,OAAM,WAAW,OAAO,SAAS,QAAQ,kBAAkB,OAAOA,OAAM;AACpF,aAAO;AAAA,IACT;AAAA,IACA,IAAI,GAAG;AACL,MAAAI,KAAI,CAAC;AAAA,IACP;AAAA,EACF,CAAC;AACD,WAASA,KAAI,GAAG;AACd,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU,IAAI,SAAS,UAAU;AACvC,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,WAAS,MAAM,QAAQ,GAAG;AACxB,WAAOA,KAAI,MAAM,QAAQ,KAAK;AAAA,EAChC;AACA,WAAS,KAAK,IAAI,GAAG;AACnB,WAAO,MAAM,CAAC;AAAA,EAChB;AACA,WAAS,KAAK,IAAI,GAAG;AACnB,WAAO,MAAM,CAAC,CAAC;AAAA,EACjB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,eAAe,OAAO;AAC1B,IAAI,sBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,GAAG,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC;AACtE,SAAS,QAAQ,UAAU,CAAC,GAAG;AAC7B,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAAL,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,OAAO,aAAa,gBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG;AAAA,IACvE,WAAW,CAAC,OAAO,mBAAmB;AACpC,UAAIF;AACJ,UAAI,QAAQ;AACV,SAACA,MAAK,QAAQ,cAAc,OAAO,SAASA,IAAG,KAAK,SAAS,UAAU,MAAM;AAAA;AAE7E,uBAAe,KAAK;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AACF,QAAM,gBAAgB,iBAAiB,EAAE,QAAAE,QAAO,CAAC;AACjD,QAAM,SAAS,SAAS;AAAA,IACtB,MAAM;AACJ,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA,IACA,IAAI,GAAG;AACL,UAAI,MAAM,cAAc;AACtB,aAAK,QAAQ;AAAA;AAEb,aAAK,QAAQ,IAAI,SAAS;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,WAAW,CAAC,MAAM;AACxB,IAAM,cAAc,CAAC,QAAQ,UAAU,OAAO,QAAQ;AACtD,SAAS,YAAY,OAAO;AAC1B,SAAO,QAAQ,WAAW,KAAK,IAAI,QAAQ,cAAc;AAC3D;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,QAAQ,WAAW,KAAK,IAAI,QAAQ,cAAc;AAC3D;AACA,SAAS,oBAAoB,QAAQ,UAAU,CAAC,GAAG;AACjD,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO,YAAY,KAAK;AAAA,IACxB,QAAQ,aAAa,KAAK;AAAA,IAC1B,YAAY;AAAA,EACd,IAAI;AACJ,WAAS,uBAAuB;AAC9B,WAAO,QAAQ;AAAA,MACb,UAAU,KAAK,OAAO,KAAK;AAAA,MAC3B,WAAW,UAAU;AAAA,IACvB,CAAC;AAAA,EACH;AACA,QAAM,OAAO,IAAI,qBAAqB,CAAC;AACvC,QAAM,YAAY,IAAI,CAAC,CAAC;AACxB,QAAM,YAAY,IAAI,CAAC,CAAC;AACxB,QAAM,aAAa,CAAC,WAAW;AAC7B,cAAU,QAAQ,MAAM,OAAO,QAAQ,CAAC;AACxC,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,SAAS,MAAM;AACnB,cAAU,MAAM,QAAQ,KAAK,KAAK;AAClC,SAAK,QAAQ,qBAAqB;AAClC,QAAI,QAAQ,YAAY,UAAU,MAAM,SAAS,QAAQ;AACvD,gBAAU,MAAM,OAAO,QAAQ,UAAU,QAAQ;AACnD,QAAI,UAAU,MAAM;AAClB,gBAAU,MAAM,OAAO,GAAG,UAAU,MAAM,MAAM;AAAA,EACpD;AACA,QAAM,QAAQ,MAAM;AAClB,cAAU,MAAM,OAAO,GAAG,UAAU,MAAM,MAAM;AAChD,cAAU,MAAM,OAAO,GAAG,UAAU,MAAM,MAAM;AAAA,EAClD;AACA,QAAM,OAAO,MAAM;AACjB,UAAM,QAAQ,UAAU,MAAM,MAAM;AACpC,QAAI,OAAO;AACT,gBAAU,MAAM,QAAQ,KAAK,KAAK;AAClC,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,QAAM,OAAO,MAAM;AACjB,UAAM,QAAQ,UAAU,MAAM,MAAM;AACpC,QAAI,OAAO;AACT,gBAAU,MAAM,QAAQ,KAAK,KAAK;AAClC,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAClB,eAAW,KAAK,KAAK;AAAA,EACvB;AACA,QAAM,UAAU,SAAS,MAAM,CAAC,KAAK,OAAO,GAAG,UAAU,KAAK,CAAC;AAC/D,QAAM,UAAU,SAAS,MAAM,UAAU,MAAM,SAAS,CAAC;AACzD,QAAM,UAAU,SAAS,MAAM,UAAU,MAAM,SAAS,CAAC;AACzD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,cAAc,OAAO;AACzB,IAAIM,gBAAe,OAAO;AAC1B,IAAIC,uBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAIC,mBAAkB,CAAC,GAAG,MAAMF,cAAa,GAAGC,qBAAoB,CAAC,CAAC;AACtE,SAAS,cAAc,QAAQ,UAAU,CAAC,GAAG;AAC3C,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,aAAa;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,IAAI,eAAe,WAAW;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,eAAe,QAAQ,QAAQ,EAAE,MAAM,OAAO,aAAa,eAAe,CAAC;AAC/E,WAAS,UAAU,SAAS,OAAO;AACjC,2BAAuB;AACvB,kBAAc,MAAM;AAClB,cAAQ,QAAQ;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,oBAAoB,QAAQC,iBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG,EAAE,OAAO,QAAQ,SAAS,MAAM,UAAU,CAAC,CAAC;AAC7I,QAAM,EAAE,OAAO,QAAQ,aAAa,IAAI;AACxC,WAAS,SAAS;AAChB,2BAAuB;AACvB,iBAAa;AAAA,EACf;AACA,WAAS,OAAO,WAAW;AACzB,mBAAe;AACf,QAAI;AACF,aAAO;AAAA,EACX;AACA,WAAS,MAAM,IAAI;AACjB,QAAI,WAAW;AACf,UAAM,SAAS,MAAM,WAAW;AAChC,kBAAc,MAAM;AAClB,SAAG,MAAM;AAAA,IACX,CAAC;AACD,QAAI,CAAC;AACH,aAAO;AAAA,EACX;AACA,WAAS,UAAU;AACjB,SAAK;AACL,UAAM;AAAA,EACR;AACA,SAAOA,iBAAgB,iBAAiB,CAAC,GAAG,aAAa,GAAG;AAAA,IAC1D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,cAAc,OAAO;AACzB,IAAIC,gBAAe,OAAO;AAC1B,IAAIC,uBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAIC,mBAAkB,CAAC,GAAG,MAAMF,cAAa,GAAGC,qBAAoB,CAAC,CAAC;AACtE,SAAS,uBAAuB,QAAQ,UAAU,CAAC,GAAG;AACpD,QAAM,SAAS,QAAQ,WAAW,eAAe,QAAQ,QAAQ,IAAI;AACrE,QAAM,UAAU,cAAc,QAAQC,iBAAgB,iBAAiB,CAAC,GAAG,OAAO,GAAG,EAAE,aAAa,OAAO,CAAC,CAAC;AAC7G,SAAO,iBAAiB,CAAC,GAAG,OAAO;AACrC;AAEA,SAAS,gBAAgB,UAAU,CAAC,GAAG;AACrC,QAAM;AAAA,IACJ,QAAAX,UAAS;AAAA,IACT,cAAc;AAAA,EAChB,IAAI;AACJ,QAAM,eAAe,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;AACtD,QAAM,eAAe,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,KAAK,CAAC;AACjE,QAAM,WAAW,IAAI,CAAC;AACtB,QAAM,+BAA+B,IAAI;AAAA,IACvC,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACD,MAAIA,SAAQ;AACV,UAAM,iBAAiB,oBAAoB,aAAa,CAAC,UAAU;AACjE,mBAAa,QAAQ,MAAM;AAC3B,mCAA6B,QAAQ,MAAM;AAC3C,mBAAa,QAAQ,MAAM;AAC3B,eAAS,QAAQ,MAAM;AAAA,IACzB,CAAC;AACD,qBAAiBA,SAAQ,gBAAgB,cAAc;AAAA,EACzD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,QAAM,cAAc,aAAa,MAAMA,WAAU,4BAA4BA,OAAM;AACnF,QAAM,aAAa,IAAI,KAAK;AAC5B,QAAM,QAAQ,IAAI,IAAI;AACtB,QAAM,OAAO,IAAI,IAAI;AACrB,QAAM,QAAQ,IAAI,IAAI;AACtB,MAAIA,WAAU,YAAY,OAAO;AAC/B,qBAAiBA,SAAQ,qBAAqB,CAAC,UAAU;AACvD,iBAAW,QAAQ,MAAM;AACzB,YAAM,QAAQ,MAAM;AACpB,WAAK,QAAQ,MAAM;AACnB,YAAM,QAAQ,MAAM;AAAA,IACtB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB;AAAA,EAC3B,QAAAA,UAAS;AACX,IAAI,CAAC,GAAG;AACN,QAAM,aAAa,IAAI,CAAC;AACxB,MAAIA,SAAQ;AACV,QAAI,UAAU,WAAW;AACvB,iBAAW,QAAQA,QAAO;AAC1B,cAAQ;AACR,cAAQA,QAAO,WAAW,gBAAgB,WAAW,KAAK,OAAO;AACjE,YAAM,iBAAiB,UAAU,SAAS,EAAE,MAAM,KAAK,CAAC;AAAA,IAC1D,GAAG,UAAU,WAAW;AACtB,eAAS,OAAO,SAAS,MAAM,oBAAoB,UAAU,OAAO;AAAA,IACtE;AACA,QAAI;AACJ,YAAQ;AACR,sBAAkB,OAAO;AAAA,EAC3B;AACA,SAAO,EAAE,WAAW;AACtB;AAEA,SAAS,cAAc,gBAAgB,UAAU,CAAC,GAAG;AACnD,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAM,aAAa,iBAAiB,SAAS;AAC9E,MAAI;AACJ,QAAM,OAAO,OAAO,mBAAmB,WAAW,EAAE,MAAM,eAAe,IAAI;AAC7E,QAAM,QAAQ,IAAI;AAClB,QAAM,WAAW,MAAM;AACrB,QAAI;AACF,YAAM,QAAQ,iBAAiB;AAAA,EACnC;AACA,QAAM,QAAQ,uBAAuB,YAAY;AAC/C,QAAI,CAAC,YAAY;AACf;AACF,QAAI,CAAC,kBAAkB;AACrB,UAAI;AACF,2BAAmB,MAAM,UAAU,YAAY,MAAM,IAAI;AACzD,yBAAiB,kBAAkB,UAAU,QAAQ;AACrD,iBAAS;AAAA,MACX,SAAS,GAAG;AACV,cAAM,QAAQ;AAAA,MAChB;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM;AACN,MAAI,UAAU;AACZ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,cAAc,EAAE,OAAO,MAAM,OAAO,KAAK;AAAA,IACzC,WAAAY;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,IAAI,CAAC,CAAC;AACtB,QAAM,cAAc,SAAS,MAAM,QAAQ,MAAM,OAAO,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC;AACvF,QAAM,cAAc,SAAS,MAAM,QAAQ,MAAM,OAAO,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC;AACvF,QAAM,eAAe,SAAS,MAAM,QAAQ,MAAM,OAAO,CAAC,MAAM,EAAE,SAAS,aAAa,CAAC;AACzF,QAAM,cAAc,aAAa,MAAM,aAAa,UAAU,gBAAgB,UAAU,aAAa,gBAAgB;AACrH,QAAM,oBAAoB,IAAI,KAAK;AACnC,iBAAe,SAAS;AACtB,QAAI,CAAC,YAAY;AACf;AACF,YAAQ,QAAQ,MAAM,UAAU,aAAa,iBAAiB;AAC9D,IAAAA,cAAa,OAAO,SAASA,WAAU,QAAQ,KAAK;AAAA,EACtD;AACA,iBAAe,oBAAoB;AACjC,QAAI,CAAC,YAAY;AACf,aAAO;AACT,QAAI,kBAAkB;AACpB,aAAO;AACT,UAAM,EAAE,OAAO,MAAM,IAAI,cAAc,UAAU,EAAE,UAAU,KAAK,CAAC;AACnE,UAAM,MAAM;AACZ,QAAI,MAAM,UAAU,WAAW;AAC7B,YAAM,SAAS,MAAM,UAAU,aAAa,aAAa,WAAW;AACpE,aAAO,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;AAC1C,aAAO;AACP,wBAAkB,QAAQ;AAAA,IAC5B,OAAO;AACL,wBAAkB,QAAQ;AAAA,IAC5B;AACA,WAAO,kBAAkB;AAAA,EAC3B;AACA,MAAI,YAAY,OAAO;AACrB,QAAI;AACF,wBAAkB;AACpB,qBAAiB,UAAU,cAAc,gBAAgB,MAAM;AAC/D,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,UAAU,CAAC,GAAG;AACrC,MAAId;AACJ,QAAM,UAAU,KAAKA,MAAK,QAAQ,YAAY,OAAOA,MAAK,KAAK;AAC/D,QAAM,QAAQ,QAAQ;AACtB,QAAM,QAAQ,QAAQ;AACtB,QAAM,EAAE,YAAY,iBAAiB,IAAI;AACzC,QAAM,cAAc,aAAa,MAAM;AACrC,QAAIG;AACJ,YAAQA,OAAM,aAAa,OAAO,SAAS,UAAU,iBAAiB,OAAO,SAASA,KAAI;AAAA,EAC5F,CAAC;AACD,QAAM,aAAa,EAAE,OAAO,MAAM;AAClC,QAAM,SAAS,WAAW;AAC1B,iBAAe,SAAS;AACtB,QAAI,CAAC,YAAY,SAAS,OAAO;AAC/B;AACF,WAAO,QAAQ,MAAM,UAAU,aAAa,gBAAgB,UAAU;AACtE,WAAO,OAAO;AAAA,EAChB;AACA,iBAAe,QAAQ;AACrB,QAAIA;AACJ,KAACA,OAAM,OAAO,UAAU,OAAO,SAASA,KAAI,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;AAC/E,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,OAAO;AACd,UAAM;AACN,YAAQ,QAAQ;AAAA,EAClB;AACA,iBAAe,QAAQ;AACrB,UAAM,OAAO;AACb,QAAI,OAAO;AACT,cAAQ,QAAQ;AAClB,WAAO,OAAO;AAAA,EAChB;AACA,QAAM,SAAS,CAAC,MAAM;AACpB,QAAI;AACF,aAAO;AAAA;AAEP,YAAM;AAAA,EACV,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,EAAE,UAAAC,YAAW,gBAAgB,IAAI,CAAC,GAAG;AAClE,MAAI,CAACA;AACH,WAAO,IAAI,SAAS;AACtB,QAAM,aAAa,IAAIA,UAAS,eAAe;AAC/C,mBAAiBA,WAAU,oBAAoB,MAAM;AACnD,eAAW,QAAQA,UAAS;AAAA,EAC9B,CAAC;AACD,SAAO;AACT;AAEA,IAAI,cAAc,OAAO;AACzB,IAAIW,gBAAe,OAAO;AAC1B,IAAIC,uBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAIC,mBAAkB,CAAC,GAAG,MAAMF,cAAa,GAAGC,qBAAoB,CAAC,CAAC;AACtE,SAAS,aAAa,QAAQ,UAAU,CAAC,GAAG;AAC1C,MAAIhB,KAAI,IAAI;AACZ,QAAM,mBAAmBA,MAAK,QAAQ,oBAAoB,OAAOA,MAAK;AACtE,QAAM,kBAAkB,KAAK,QAAQ,WAAW,OAAO,KAAK;AAC5D,QAAM,WAAW,KAAK,KAAK,aAAa,QAAQ,YAAY,MAAM,OAAO,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAC5F,QAAM,eAAe,IAAI;AACzB,QAAM,cAAc,CAAC,MAAM;AACzB,QAAI,QAAQ;AACV,aAAO,QAAQ,aAAa,SAAS,EAAE,WAAW;AACpD,WAAO;AAAA,EACT;AACA,QAAM,cAAc,CAAC,MAAM;AACzB,QAAI,aAAa,QAAQ,cAAc;AACrC,QAAE,eAAe;AACnB,QAAI,aAAa,QAAQ,eAAe;AACtC,QAAE,gBAAgB;AAAA,EACtB;AACA,QAAM,QAAQ,CAAC,MAAM;AACnB,QAAIG;AACJ,QAAI,CAAC,YAAY,CAAC;AAChB;AACF,QAAI,aAAa,QAAQ,KAAK,KAAK,EAAE,WAAW,aAAa,MAAM;AACjE;AACF,UAAM,OAAO,aAAa,MAAM,EAAE,sBAAsB;AACxD,UAAM,MAAM;AAAA,MACV,GAAG,EAAE,UAAU,KAAK;AAAA,MACpB,GAAG,EAAE,UAAU,KAAK;AAAA,IACtB;AACA,UAAMA,OAAM,QAAQ,YAAY,OAAO,SAASA,KAAI,KAAK,SAAS,KAAK,CAAC,OAAO;AAC7E;AACF,iBAAa,QAAQ;AACrB,gBAAY,CAAC;AAAA,EACf;AACA,QAAM,OAAO,CAAC,MAAM;AAClB,QAAIA;AACJ,QAAI,CAAC,YAAY,CAAC;AAChB;AACF,QAAI,CAAC,aAAa;AAChB;AACF,aAAS,QAAQ;AAAA,MACf,GAAG,EAAE,UAAU,aAAa,MAAM;AAAA,MAClC,GAAG,EAAE,UAAU,aAAa,MAAM;AAAA,IACpC;AACA,KAACA,OAAM,QAAQ,WAAW,OAAO,SAASA,KAAI,KAAK,SAAS,SAAS,OAAO,CAAC;AAC7E,gBAAY,CAAC;AAAA,EACf;AACA,QAAM,MAAM,CAAC,MAAM;AACjB,QAAIA;AACJ,QAAI,CAAC,YAAY,CAAC;AAChB;AACF,QAAI,CAAC,aAAa;AAChB;AACF,iBAAa,QAAQ;AACrB,KAACA,OAAM,QAAQ,UAAU,OAAO,SAASA,KAAI,KAAK,SAAS,SAAS,OAAO,CAAC;AAC5E,gBAAY,CAAC;AAAA,EACf;AACA,MAAI,UAAU;AACZ,qBAAiB,gBAAgB,eAAe,OAAO,IAAI;AAC3D,qBAAiB,iBAAiB,eAAe,MAAM,IAAI;AAC3D,qBAAiB,iBAAiB,aAAa,KAAK,IAAI;AAAA,EAC1D;AACA,SAAOc,iBAAgB,iBAAiB,CAAC,GAAGC,QAAO,QAAQ,CAAC,GAAG;AAAA,IAC7D;AAAA,IACA,YAAY,SAAS,MAAM,CAAC,CAAC,aAAa,KAAK;AAAA,IAC/C,OAAO,SAAS,MAAM,QAAQ,SAAS,MAAM,CAAC,UAAU,SAAS,MAAM,CAAC,KAAK;AAAA,EAC/E,CAAC;AACH;AAEA,SAAS,YAAY,QAAQ,QAAQ;AACnC,QAAM,iBAAiB,IAAI,KAAK;AAChC,MAAI,UAAU;AACd,MAAI,UAAU;AACZ,qBAAiB,QAAQ,aAAa,CAAC,UAAU;AAC/C,YAAM,eAAe;AACrB,iBAAW;AACX,qBAAe,QAAQ;AAAA,IACzB,CAAC;AACD,qBAAiB,QAAQ,YAAY,CAAC,UAAU;AAC9C,YAAM,eAAe;AAAA,IACvB,CAAC;AACD,qBAAiB,QAAQ,aAAa,CAAC,UAAU;AAC/C,YAAM,eAAe;AACrB,iBAAW;AACX,UAAI,YAAY;AACd,uBAAe,QAAQ;AAAA,IAC3B,CAAC;AACD,qBAAiB,QAAQ,QAAQ,CAAC,UAAU;AAC1C,UAAIlB,KAAI;AACR,YAAM,eAAe;AACrB,gBAAU;AACV,qBAAe,QAAQ;AACvB,YAAM,QAAQ,MAAM,MAAM,MAAMA,MAAK,MAAM,iBAAiB,OAAO,SAASA,IAAG,UAAU,OAAO,KAAK,CAAC,CAAC;AACvG,gBAAU,OAAO,SAAS,OAAO,MAAM,WAAW,IAAI,OAAO,KAAK;AAAA,IACpE,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAImB,eAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAI,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQ;AACpB,aAAS,QAAQ,sBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK,eAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,kBAAkB,QAAQ,UAAU,UAAU,CAAC,GAAG;AACzD,QAAMnB,MAAK,SAAS,EAAE,QAAAE,UAAS,cAAc,IAAIF,KAAI,kBAAkBmB,aAAYnB,KAAI,CAAC,QAAQ,CAAC;AACjG,MAAI;AACJ,QAAM,cAAc,aAAa,MAAME,WAAU,oBAAoBA,OAAM;AAC3E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,YAAY,MAAM,MAAM,aAAa,MAAM,GAAG,CAAC,OAAO;AAC1D,YAAQ;AACR,QAAI,YAAY,SAASA,WAAU,IAAI;AACrC,iBAAW,IAAI,eAAe,QAAQ;AACtC,eAAS,QAAQ,IAAI,eAAe;AAAA,IACtC;AAAA,EACF,GAAG,EAAE,WAAW,MAAM,OAAO,OAAO,CAAC;AACrC,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,QAAQ,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,SAAS,IAAI,CAAC;AACpB,QAAM,SAAS,IAAI,CAAC;AACpB,QAAM,OAAO,IAAI,CAAC;AAClB,QAAM,QAAQ,IAAI,CAAC;AACnB,QAAM,MAAM,IAAI,CAAC;AACjB,QAAM,QAAQ,IAAI,CAAC;AACnB,QAAM,IAAI,IAAI,CAAC;AACf,QAAM,IAAI,IAAI,CAAC;AACf,WAAS,SAAS;AAChB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC,IAAI;AACP,UAAI,OAAO;AACT,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf,aAAK,QAAQ;AACb,cAAM,QAAQ;AACd,YAAI,QAAQ;AACZ,cAAM,QAAQ;AACd,UAAE,QAAQ;AACV,UAAE,QAAQ;AAAA,MACZ;AACA;AAAA,IACF;AACA,UAAM,OAAO,GAAG,sBAAsB;AACtC,WAAO,QAAQ,KAAK;AACpB,WAAO,QAAQ,KAAK;AACpB,SAAK,QAAQ,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,QAAI,QAAQ,KAAK;AACjB,UAAM,QAAQ,KAAK;AACnB,MAAE,QAAQ,KAAK;AACf,MAAE,QAAQ,KAAK;AAAA,EACjB;AACA,oBAAkB,QAAQ,MAAM;AAChC,QAAM,MAAM,aAAa,MAAM,GAAG,CAAC,QAAQ,CAAC,OAAO,OAAO,CAAC;AAC3D,MAAI;AACF,qBAAiB,UAAU,QAAQ,EAAE,SAAS,MAAM,SAAS,KAAK,CAAC;AACrE,MAAI;AACF,qBAAiB,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACtD,eAAa,MAAM;AACjB,QAAI;AACF,aAAO;AAAA,EACX,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,IAAI,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,WAAW,IAAI,KAAK;AAC1B,MAAI,yBAAyB;AAC7B,MAAI,QAAQ;AACZ,WAAS,KAAKkB,YAAW;AACvB,QAAI,CAAC,SAAS,SAAS,CAAClB;AACtB;AACF,UAAM,QAAQkB,aAAY;AAC1B,OAAG,EAAE,OAAO,WAAAA,WAAU,CAAC;AACvB,6BAAyBA;AACzB,YAAQlB,QAAO,sBAAsB,IAAI;AAAA,EAC3C;AACA,WAAS,SAAS;AAChB,QAAI,CAAC,SAAS,SAASA,SAAQ;AAC7B,eAAS,QAAQ;AACjB,cAAQA,QAAO,sBAAsB,IAAI;AAAA,IAC3C;AAAA,EACF;AACA,WAAS,QAAQ;AACf,aAAS,QAAQ;AACjB,QAAI,SAAS,QAAQA,SAAQ;AAC3B,MAAAA,QAAO,qBAAqB,KAAK;AACjC,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI;AACF,WAAO;AACT,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL,UAAU,SAAS,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM,UAAU,IAAI,IAAI;AACxB,QAAM,EAAE,GAAG,GAAG,UAAAE,YAAW,gBAAgB,IAAI;AAC7C,QAAM,WAAW,SAAS,MAAM;AAC9B,YAAQ,SAASA,aAAY,OAAO,SAASA,UAAS,iBAAiB,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC,MAAM;AAAA,EAC/G,CAAC;AACD,SAAO,iBAAiB;AAAA,IACtB;AAAA,EACF,GAAG,QAAQ;AACb;AAEA,SAAS,gBAAgB,IAAI,UAAU,CAAC,GAAG;AACzC,QAAM,aAAa,UAAU,QAAQ,aAAa;AAClD,QAAM,aAAa,UAAU,QAAQ,aAAa;AAClD,QAAM,YAAY,IAAI,KAAK;AAC3B,MAAI;AACJ,QAAM,SAAS,CAAC,aAAa;AAC3B,UAAM,QAAQ,WAAW,aAAa;AACtC,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AACA,QAAI;AACF,cAAQ,WAAW,MAAM,UAAU,QAAQ,UAAU,KAAK;AAAA;AAE1D,gBAAU,QAAQ;AAAA,EACtB;AACA,MAAI,CAAC;AACH,WAAO;AACT,mBAAiB,IAAI,cAAc,MAAM,OAAO,IAAI,GAAG,EAAE,SAAS,KAAK,CAAC;AACxE,mBAAiB,IAAI,cAAc,MAAM,OAAO,KAAK,GAAG,EAAE,SAAS,KAAK,CAAC;AACzE,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ,cAAc,EAAE,OAAO,GAAG,QAAQ,EAAE,GAAG,UAAU,CAAC,GAAG;AACnF,QAAM,EAAE,QAAAF,UAAS,eAAe,MAAM,cAAc,IAAI;AACxD,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAIF,KAAI;AACR,YAAQ,MAAMA,MAAK,aAAa,MAAM,MAAM,OAAO,SAASA,IAAG,iBAAiB,OAAO,SAAS,GAAG,SAAS,KAAK;AAAA,EACnH,CAAC;AACD,QAAM,QAAQ,IAAI,YAAY,KAAK;AACnC,QAAM,SAAS,IAAI,YAAY,MAAM;AACrC,oBAAkB,QAAQ,CAAC,CAAC,KAAK,MAAM;AACrC,UAAM,UAAU,QAAQ,eAAe,MAAM,gBAAgB,QAAQ,gBAAgB,MAAM,iBAAiB,MAAM;AAClH,QAAIE,WAAU,MAAM,OAAO;AACzB,YAAM,QAAQ,aAAa,MAAM;AACjC,UAAI,OAAO;AACT,cAAM,SAASA,QAAO,iBAAiB,KAAK;AAC5C,cAAM,QAAQ,WAAW,OAAO,KAAK;AACrC,eAAO,QAAQ,WAAW,OAAO,MAAM;AAAA,MACzC;AAAA,IACF,OAAO;AACL,UAAI,SAAS;AACX,cAAM,gBAAgB,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACjE,cAAM,QAAQ,cAAc,OAAO,CAAC,KAAK,EAAE,WAAW,MAAM,MAAM,YAAY,CAAC;AAC/E,eAAO,QAAQ,cAAc,OAAO,CAAC,KAAK,EAAE,UAAU,MAAM,MAAM,WAAW,CAAC;AAAA,MAChF,OAAO;AACL,cAAM,QAAQ,MAAM,YAAY;AAChC,eAAO,QAAQ,MAAM,YAAY;AAAA,MACnC;AAAA,IACF;AAAA,EACF,GAAG,OAAO;AACV,QAAM,MAAM,aAAa,MAAM,GAAG,CAAC,QAAQ;AACzC,UAAM,QAAQ,MAAM,YAAY,QAAQ;AACxC,WAAO,QAAQ,MAAM,YAAY,SAAS;AAAA,EAC5C,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,SAAS,EAAE,QAAAA,UAAS,eAAe,aAAa,IAAI,CAAC,GAAG;AACpF,QAAM,mBAAmB,IAAI,KAAK;AAClC,QAAM,eAAe,MAAM;AACzB,QAAI,CAACA;AACH;AACF,UAAME,YAAWF,QAAO;AACxB,UAAM,KAAK,aAAa,OAAO;AAC/B,QAAI,CAAC,IAAI;AACP,uBAAiB,QAAQ;AAAA,IAC3B,OAAO;AACL,YAAM,OAAO,GAAG,sBAAsB;AACtC,uBAAiB,QAAQ,KAAK,QAAQA,QAAO,eAAeE,UAAS,gBAAgB,iBAAiB,KAAK,SAASF,QAAO,cAAcE,UAAS,gBAAgB,gBAAgB,KAAK,UAAU,KAAK,KAAK,SAAS;AAAA,IACtN;AAAA,EACF;AACA,QAAM,MAAM,aAAa,OAAO,GAAG,MAAM,aAAa,GAAG,EAAE,WAAW,MAAM,OAAO,OAAO,CAAC;AAC3F,MAAIF,SAAQ;AACV,qBAAiB,gBAAgBA,SAAQ,UAAU,cAAc;AAAA,MAC/D,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,IAAM,SAAS,oBAAI,IAAI;AAEvB,SAAS,YAAY,KAAK;AACxB,QAAM,QAAQ,gBAAgB;AAC9B,WAAS,GAAG,UAAU;AACpB,QAAIF;AACJ,UAAM,YAAY,OAAO,IAAI,GAAG,KAAK,CAAC;AACtC,cAAU,KAAK,QAAQ;AACvB,WAAO,IAAI,KAAK,SAAS;AACzB,UAAM,OAAO,MAAM,IAAI,QAAQ;AAC/B,KAACA,MAAK,SAAS,OAAO,SAAS,MAAM,aAAa,OAAO,SAASA,IAAG,KAAK,IAAI;AAC9E,WAAO;AAAA,EACT;AACA,WAAS,KAAK,UAAU;AACtB,aAAS,aAAa,MAAM;AAC1B,UAAI,SAAS;AACb,eAAS,GAAG,IAAI;AAAA,IAClB;AACA,WAAO,GAAG,SAAS;AAAA,EACrB;AACA,WAAS,IAAI,UAAU;AACrB,UAAM,YAAY,OAAO,IAAI,GAAG;AAChC,QAAI,CAAC;AACH;AACF,UAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,QAAI,QAAQ;AACV,gBAAU,OAAO,OAAO,CAAC;AAC3B,QAAI,CAAC,UAAU;AACb,aAAO,OAAO,GAAG;AAAA,EACrB;AACA,WAAS,QAAQ;AACf,WAAO,OAAO,GAAG;AAAA,EACnB;AACA,WAAS,KAAK,OAAO,SAAS;AAC5B,QAAIA;AACJ,KAACA,MAAK,OAAO,IAAI,GAAG,MAAM,OAAO,SAASA,IAAG,QAAQ,CAAC,MAAM,EAAE,OAAO,OAAO,CAAC;AAAA,EAC/E;AACA,SAAO,EAAE,IAAI,MAAM,KAAK,MAAM,MAAM;AACtC;AAEA,SAAS,eAAe,KAAKC,UAAS,CAAC,GAAG,UAAU,CAAC,GAAG;AACtD,QAAM,QAAQ,IAAI,IAAI;AACtB,QAAM,OAAO,IAAI,IAAI;AACrB,QAAM,SAAS,IAAI,YAAY;AAC/B,QAAM,cAAc,IAAI,IAAI;AAC5B,QAAM,QAAQ,IAAI,IAAI;AACtB,QAAM;AAAA,IACJ,kBAAkB;AAAA,EACpB,IAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,QAAI,YAAY,OAAO;AACrB,kBAAY,MAAM,MAAM;AACxB,kBAAY,QAAQ;AACpB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,QAAM,KAAK,IAAI,YAAY,KAAK,EAAE,gBAAgB,CAAC;AACnD,cAAY,QAAQ;AACpB,KAAG,SAAS,MAAM;AAChB,WAAO,QAAQ;AACf,UAAM,QAAQ;AAAA,EAChB;AACA,KAAG,UAAU,CAAC,MAAM;AAClB,WAAO,QAAQ;AACf,UAAM,QAAQ;AAAA,EAChB;AACA,KAAG,YAAY,CAAC,MAAM;AACpB,UAAM,QAAQ;AACd,SAAK,QAAQ,EAAE;AAAA,EACjB;AACA,aAAW,cAAcA,SAAQ;AAC/B,qBAAiB,IAAI,YAAY,CAAC,MAAM;AACtC,YAAM,QAAQ;AACd,WAAK,QAAQ,EAAE,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH;AACA,oBAAkB,MAAM;AACtB,UAAM;AAAA,EACR,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,cAAc,UAAU,CAAC,GAAG;AACnC,QAAM,EAAE,eAAe,GAAG,IAAI;AAC9B,QAAM,cAAc,aAAa,MAAM,OAAO,WAAW,eAAe,gBAAgB,MAAM;AAC9F,QAAM,UAAU,IAAI,YAAY;AAChC,iBAAe,KAAK,aAAa;AAC/B,QAAI,CAAC,YAAY;AACf;AACF,UAAM,aAAa,IAAI,OAAO,WAAW;AACzC,UAAM,SAAS,MAAM,WAAW,KAAK,WAAW;AAChD,YAAQ,QAAQ,OAAO;AACvB,WAAO;AAAA,EACT;AACA,SAAO,EAAE,aAAa,SAAS,KAAK;AACtC;AAEA,SAAS,WAAW,UAAU,MAAM,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAAG,YAAW;AAAA,EACb,IAAI;AACJ,QAAM,UAAU,WAAW,OAAO;AAClC,QAAM,YAAY,CAAC,SAAS;AAC1B,IAAAA,aAAY,OAAO,SAASA,UAAS,KAAK,iBAAiB,cAAc,GAAG,IAAI,EAAE,QAAQ,CAAC,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,IAAI,EAAE;AAAA,EACjI;AACA,QAAM,SAAS,CAAC,GAAG,MAAM;AACvB,QAAI,SAAS,CAAC,KAAK,MAAM;AACvB,gBAAU,CAAC;AAAA,EACf,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,SAAO;AACT;AAEA,IAAI,cAAc,OAAO;AACzB,IAAIiB,gBAAe,OAAO;AAC1B,IAAIC,uBAAsB,OAAO;AACjC,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAIC,mBAAkB,CAAC,GAAG,MAAMF,cAAa,GAAGC,qBAAoB,CAAC,CAAC;AACtE,IAAM,iBAAiB;AAAA,EACrB,MAAM;AAAA,EACN,MAAM;AACR;AACA,SAAS,eAAe,KAAK;AAC3B,SAAO,OAAO,aAAa,KAAK,aAAa,WAAW,eAAe,WAAW,eAAe,cAAc,gBAAgB,OAAO;AACxI;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,8BAA8B,KAAK,GAAG;AAC/C;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,OAAO,YAAY,eAAe,mBAAmB;AACvD,WAAO,OAAO,YAAY,CAAC,GAAG,QAAQ,QAAQ,CAAC,CAAC;AAClD,SAAO;AACT;AACA,SAAS,iBAAiB,gBAAgB,WAAW;AACnD,MAAI,gBAAgB,aAAa;AAC/B,WAAO,OAAO,QAAQ;AACpB,YAAM,WAAW,UAAU,UAAU,SAAS,CAAC;AAC/C,UAAI,aAAa;AACf,cAAM,SAAS,GAAG;AACpB,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO,OAAO,QAAQ;AACpB,YAAM,UAAU,OAAO,CAAC,cAAc,aAAa,aAAa,KAAK,YAAY;AAC/E,YAAI;AACF,gBAAM,iBAAiB,iBAAiB,CAAC,GAAG,GAAG,GAAG,MAAM,SAAS,GAAG,CAAC;AAAA,MACzE,CAAC,GAAG,QAAQ,QAAQ,CAAC;AACrB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,YAAY,SAAS,CAAC,GAAG;AAChC,QAAM,eAAe,OAAO,eAAe;AAC3C,QAAM,WAAW,OAAO,WAAW,CAAC;AACpC,QAAM,gBAAgB,OAAO,gBAAgB,CAAC;AAC9C,WAAS,gBAAgB,QAAQ,MAAM;AACrC,UAAM,cAAc,SAAS,MAAM;AACjC,YAAM,UAAU,aAAa,OAAO,OAAO;AAC3C,YAAM,YAAY,aAAa,GAAG;AAClC,aAAO,WAAW,CAAC,cAAc,SAAS,IAAI,UAAU,SAAS,SAAS,IAAI;AAAA,IAChF,CAAC;AACD,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,KAAK,SAAS,GAAG;AACnB,UAAI,eAAe,KAAK,CAAC,CAAC,GAAG;AAC3B,kBAAUC,iBAAgB,iBAAiB,iBAAiB,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG;AAAA,UAClF,aAAa,iBAAiB,cAAc,SAAS,aAAa,KAAK,CAAC,EAAE,WAAW;AAAA,UACrF,YAAY,iBAAiB,cAAc,SAAS,YAAY,KAAK,CAAC,EAAE,UAAU;AAAA,UAClF,cAAc,iBAAiB,cAAc,SAAS,cAAc,KAAK,CAAC,EAAE,YAAY;AAAA,QAC1F,CAAC;AAAA,MACH,OAAO;AACL,uBAAeA,iBAAgB,iBAAiB,iBAAiB,CAAC,GAAG,YAAY,GAAG,KAAK,CAAC,CAAC,GAAG;AAAA,UAC5F,SAAS,iBAAiB,iBAAiB,CAAC,GAAG,gBAAgB,aAAa,OAAO,KAAK,CAAC,CAAC,GAAG,gBAAgB,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;AAAA,QACrI,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,KAAK,SAAS,KAAK,eAAe,KAAK,CAAC,CAAC,GAAG;AAC9C,gBAAUA,iBAAgB,iBAAiB,iBAAiB,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG;AAAA,QAClF,aAAa,iBAAiB,cAAc,SAAS,aAAa,KAAK,CAAC,EAAE,WAAW;AAAA,QACrF,YAAY,iBAAiB,cAAc,SAAS,YAAY,KAAK,CAAC,EAAE,UAAU;AAAA,QAClF,cAAc,iBAAiB,cAAc,SAAS,cAAc,KAAK,CAAC,EAAE,YAAY;AAAA,MAC1F,CAAC;AAAA,IACH;AACA,WAAO,SAAS,aAAa,cAAc,OAAO;AAAA,EACpD;AACA,SAAO;AACT;AACA,SAAS,SAAS,QAAQ,MAAM;AAC9B,MAAIvB;AACJ,QAAM,gBAAgB,OAAO,oBAAoB;AACjD,MAAI,eAAe,CAAC;AACpB,MAAI,UAAU,EAAE,WAAW,MAAM,SAAS,OAAO,SAAS,EAAE;AAC5D,QAAM,SAAS;AAAA,IACb,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACA,MAAI,KAAK,SAAS,GAAG;AACnB,QAAI,eAAe,KAAK,CAAC,CAAC;AACxB,gBAAU,iBAAiB,iBAAiB,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AAAA;AAEjE,qBAAe,KAAK,CAAC;AAAA,EACzB;AACA,MAAI,KAAK,SAAS,GAAG;AACnB,QAAI,eAAe,KAAK,CAAC,CAAC;AACxB,gBAAU,iBAAiB,iBAAiB,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AAAA,EACrE;AACA,QAAM;AAAA,IACJ,SAASA,MAAK,kBAAkB,OAAO,SAASA,IAAG;AAAA,IACnD;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,aAAa,gBAAgB;AACnC,QAAM,eAAe,gBAAgB;AACrC,QAAM,aAAa,IAAI,KAAK;AAC5B,QAAM,aAAa,IAAI,KAAK;AAC5B,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,aAAa,IAAI,IAAI;AAC3B,QAAM,WAAW,WAAW,IAAI;AAChC,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,OAAO,WAAW,WAAW;AACnC,QAAM,WAAW,SAAS,MAAM,iBAAiB,WAAW,KAAK;AACjE,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,QAAI,iBAAiB,YAAY;AAC/B,iBAAW,MAAM;AACjB,mBAAa;AAAA,IACf;AAAA,EACF;AACA,QAAM,UAAU,CAAC,cAAc;AAC7B,eAAW,QAAQ;AACnB,eAAW,QAAQ,CAAC;AAAA,EACtB;AACA,MAAI;AACF,YAAQ,aAAa,OAAO,SAAS,EAAE,WAAW,MAAM,CAAC;AAC3D,QAAM,UAAU,OAAO,gBAAgB,UAAU;AAC/C,QAAIG;AACJ,YAAQ,IAAI;AACZ,UAAM,QAAQ;AACd,eAAW,QAAQ;AACnB,YAAQ,QAAQ;AAChB,QAAI,eAAe;AACjB,YAAM;AACN,mBAAa,IAAI,gBAAgB;AACjC,iBAAW,OAAO,UAAU,MAAM,QAAQ,QAAQ;AAClD,qBAAeoB,iBAAgB,iBAAiB,CAAC,GAAG,YAAY,GAAG;AAAA,QACjE,QAAQ,WAAW;AAAA,MACrB,CAAC;AAAA,IACH;AACA,UAAM,sBAAsB;AAAA,MAC1B,QAAQ,OAAO;AAAA,MACf,SAAS,CAAC;AAAA,IACZ;AACA,QAAI,OAAO,SAAS;AAClB,YAAM,UAAU,gBAAgB,oBAAoB,OAAO;AAC3D,UAAI,OAAO;AACT,gBAAQ,cAAc,KAAKpB,OAAM,eAAe,OAAO,WAAW,MAAM,OAAOA,OAAM,OAAO;AAC9F,YAAM,UAAU,aAAa,OAAO,OAAO;AAC3C,0BAAoB,OAAO,OAAO,gBAAgB,SAAS,KAAK,UAAU,OAAO,IAAI;AAAA,IACvF;AACA,QAAI,aAAa;AACjB,UAAM,UAAU;AAAA,MACd,KAAK,aAAa,GAAG;AAAA,MACrB,SAAS,iBAAiB,iBAAiB,CAAC,GAAG,mBAAmB,GAAG,YAAY;AAAA,MACjF,QAAQ,MAAM;AACZ,qBAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,QAAQ;AACV,aAAO,OAAO,SAAS,MAAM,QAAQ,YAAY,OAAO,CAAC;AAC3D,QAAI,cAAc,CAAC,OAAO;AACxB,cAAQ,KAAK;AACb,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AACA,QAAI,eAAe;AACnB,QAAI;AACF,YAAM,MAAM;AACd,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACJ,YAAM,QAAQ,KAAKoB,iBAAgB,iBAAiB,iBAAiB,CAAC,GAAG,mBAAmB,GAAG,QAAQ,OAAO,GAAG;AAAA,QAC/G,SAAS,iBAAiB,iBAAiB,CAAC,GAAG,gBAAgB,oBAAoB,OAAO,CAAC,GAAG,iBAAiB,MAAM,QAAQ,YAAY,OAAO,SAAS,IAAI,OAAO,CAAC;AAAA,MACvK,CAAC,CAAC,EAAE,KAAK,OAAO,kBAAkB;AAChC,iBAAS,QAAQ;AACjB,mBAAW,QAAQ,cAAc;AACjC,uBAAe,MAAM,cAAc,OAAO,IAAI,EAAE;AAChD,YAAI,QAAQ,cAAc,WAAW,SAAS,OAAO,WAAW,QAAQ;AACtE,WAAC,EAAE,MAAM,aAAa,IAAI,MAAM,QAAQ,WAAW,EAAE,MAAM,cAAc,UAAU,cAAc,CAAC;AACpG,aAAK,QAAQ;AACb,YAAI,CAAC,cAAc;AACjB,gBAAM,IAAI,MAAM,cAAc,UAAU;AAC1C,sBAAc,QAAQ,aAAa;AACnC,eAAO,QAAQ,aAAa;AAAA,MAC9B,CAAC,EAAE,MAAM,OAAO,eAAe;AAC7B,YAAI,YAAY,WAAW,WAAW,WAAW;AACjD,YAAI,QAAQ;AACV,WAAC,EAAE,MAAM,cAAc,OAAO,UAAU,IAAI,MAAM,QAAQ,aAAa,EAAE,MAAM,cAAc,OAAO,YAAY,UAAU,SAAS,MAAM,CAAC;AAC5I,aAAK,QAAQ;AACb,cAAM,QAAQ;AACd,mBAAW,QAAQ,UAAU;AAC7B,YAAI;AACF,iBAAO,OAAO,UAAU;AAC1B,eAAO,QAAQ,IAAI;AAAA,MACrB,CAAC,EAAE,QAAQ,MAAM;AACf,gBAAQ,KAAK;AACb,YAAI;AACF,gBAAM,KAAK;AACb,qBAAa,QAAQ,IAAI;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,UAAU,WAAW,QAAQ,OAAO;AAC1C,QAAM;AAAA,IACJ;AAAA,IACA,WAAW,GAAG;AAAA,EAChB,GAAG,CAAC,CAAC,QAAQ,MAAM,YAAY,QAAQ,GAAG,EAAE,MAAM,KAAK,CAAC;AACxD,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,cAAc;AAAA,IAC/B,cAAc,WAAW;AAAA,IACzB,gBAAgB,aAAa;AAAA,IAC7B,KAAK,UAAU,KAAK;AAAA,IACpB,KAAK,UAAU,KAAK;AAAA,IACpB,MAAM,UAAU,MAAM;AAAA,IACtB,QAAQ,UAAU,QAAQ;AAAA,IAC1B,OAAO,UAAU,OAAO;AAAA,IACxB,MAAM,UAAU,MAAM;AAAA,IACtB,SAAS,UAAU,SAAS;AAAA,IAC5B,MAAM,QAAQ,MAAM;AAAA,IACpB,MAAM,QAAQ,MAAM;AAAA,IACpB,MAAM,QAAQ,MAAM;AAAA,IACpB,aAAa,QAAQ,aAAa;AAAA,IAClC,UAAU,QAAQ,UAAU;AAAA,EAC9B;AACA,WAAS,UAAU,QAAQ;AACzB,WAAO,CAAC,SAAS,gBAAgB;AAC/B,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,SAAS;AAChB,eAAO,UAAU;AACjB,eAAO,cAAc;AACrB,YAAI,MAAM,OAAO,OAAO,GAAG;AACzB,gBAAM;AAAA,YACJ;AAAA,YACA,WAAW,OAAO,OAAO;AAAA,UAC3B,GAAG,CAAC,CAAC,QAAQ,MAAM,YAAY,QAAQ,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,QAC1D;AACA,cAAM,aAAa,aAAa,OAAO,OAAO;AAC9C,YAAI,CAAC,eAAe,cAAc,OAAO,eAAe,UAAU,MAAM,OAAO,aAAa,EAAE,sBAAsB;AAClH,iBAAO,cAAc;AACvB,eAAOA,iBAAgB,iBAAiB,CAAC,GAAG,KAAK,GAAG;AAAA,UAClD,KAAK,aAAa,YAAY;AAC5B,mBAAO,kBAAkB,EAAE,KAAK,aAAa,UAAU;AAAA,UACzD;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,oBAAoB;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,UAAU,EAAE,KAAK,IAAI,EAAE,KAAK,MAAM,QAAQ,KAAK,CAAC,EAAE,MAAM,CAAC,WAAW,OAAO,MAAM,CAAC;AAAA,IAC1F,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,MAAM;AACrB,WAAO,MAAM;AACX,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,OAAO;AACd,eAAOA,iBAAgB,iBAAiB,CAAC,GAAG,KAAK,GAAG;AAAA,UAClD,KAAK,aAAa,YAAY;AAC5B,mBAAO,kBAAkB,EAAE,KAAK,aAAa,UAAU;AAAA,UACzD;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,QAAQ;AACV,eAAW,SAAS,CAAC;AACvB,SAAOA,iBAAgB,iBAAiB,CAAC,GAAG,KAAK,GAAG;AAAA,IAClD,KAAK,aAAa,YAAY;AAC5B,aAAO,kBAAkB,EAAE,KAAK,aAAa,UAAU;AAAA,IACzD;AAAA,EACF,CAAC;AACH;AACA,SAAS,UAAU,OAAO,KAAK;AAC7B,MAAI,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,WAAW,GAAG;AAC7C,WAAO,GAAG,KAAK,IAAI,GAAG;AACxB,SAAO,GAAG,KAAK,GAAG,GAAG;AACvB;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAM,kBAAkB;AAAA,EACtB,UAAU;AAAA,EACV,QAAQ;AACV;AACA,SAAS,cAAc,UAAU,CAAC,GAAG;AACnC,QAAM;AAAA,IACJ,UAAAnB,YAAW;AAAA,EACb,IAAI;AACJ,QAAM,QAAQ,IAAI,IAAI;AACtB,MAAI;AACJ,MAAIA,WAAU;AACZ,YAAQA,UAAS,cAAc,OAAO;AACtC,UAAM,OAAO;AACb,UAAM,WAAW,CAAC,UAAU;AAC1B,YAAM,SAAS,MAAM;AACrB,YAAM,QAAQ,OAAO;AAAA,IACvB;AAAA,EACF;AACA,QAAM,OAAO,CAAC,iBAAiB;AAC7B,QAAI,CAAC;AACH;AACF,UAAM,WAAW,iBAAiB,iBAAiB,iBAAiB,CAAC,GAAG,eAAe,GAAG,OAAO,GAAG,YAAY;AAChH,UAAM,WAAW,SAAS;AAC1B,UAAM,SAAS,SAAS;AACxB,QAAI,OAAO,UAAU,SAAS;AAC5B,YAAM,UAAU,SAAS;AAC3B,UAAM,MAAM;AAAA,EACd;AACA,QAAM,QAAQ,MAAM;AAClB,UAAM,QAAQ;AACd,QAAI;AACF,YAAM,QAAQ;AAAA,EAClB;AACA,SAAO;AAAA,IACL,OAAO,SAAS,KAAK;AAAA,IACrB;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,oBAAoB,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,QAAQ,UAAU;AAAA,IAClB,WAAW;AAAA,EACb,IAAI,MAAM,OAAO;AACjB,QAAMF,UAAS;AACf,QAAM,cAAc,aAAa,MAAMA,WAAU,wBAAwBA,WAAU,wBAAwBA,OAAM;AACjH,QAAM,aAAa,IAAI;AACvB,QAAM,OAAO,IAAI;AACjB,QAAM,OAAO,IAAI;AACjB,QAAM,WAAW,SAAS,MAAM;AAC9B,QAAIF,KAAI;AACR,YAAQ,MAAMA,MAAK,KAAK,UAAU,OAAO,SAASA,IAAG,SAAS,OAAO,KAAK;AAAA,EAC5E,CAAC;AACD,QAAM,WAAW,SAAS,MAAM;AAC9B,QAAIA,KAAI;AACR,YAAQ,MAAMA,MAAK,KAAK,UAAU,OAAO,SAASA,IAAG,SAAS,OAAO,KAAK;AAAA,EAC5E,CAAC;AACD,QAAM,WAAW,SAAS,MAAM;AAC9B,QAAIA,KAAI;AACR,YAAQ,MAAMA,MAAK,KAAK,UAAU,OAAO,SAASA,IAAG,SAAS,OAAO,KAAK;AAAA,EAC5E,CAAC;AACD,QAAM,mBAAmB,SAAS,MAAM;AACtC,QAAIA,KAAI;AACR,YAAQ,MAAMA,MAAK,KAAK,UAAU,OAAO,SAASA,IAAG,iBAAiB,OAAO,KAAK;AAAA,EACpF,CAAC;AACD,iBAAe,KAAK,WAAW,CAAC,GAAG;AACjC,QAAI,CAAC,YAAY;AACf;AACF,UAAM,CAAC,MAAM,IAAI,MAAME,QAAO,mBAAmB,iBAAiB,iBAAiB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,QAAQ,CAAC;AACjH,eAAW,QAAQ;AACnB,UAAM,WAAW;AACjB,UAAM,WAAW;AAAA,EACnB;AACA,iBAAe,OAAO,WAAW,CAAC,GAAG;AACnC,QAAI,CAAC,YAAY;AACf;AACF,eAAW,QAAQ,MAAMA,QAAO,mBAAmB,iBAAiB,iBAAiB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,QAAQ,CAAC;AACnH,SAAK,QAAQ;AACb,UAAM,WAAW;AACjB,UAAM,WAAW;AAAA,EACnB;AACA,iBAAe,KAAK,WAAW,CAAC,GAAG;AACjC,QAAI,CAAC,YAAY;AACf;AACF,QAAI,CAAC,WAAW;AACd,aAAO,OAAO,QAAQ;AACxB,QAAI,KAAK,OAAO;AACd,YAAM,iBAAiB,MAAM,WAAW,MAAM,eAAe;AAC7D,YAAM,eAAe,MAAM,KAAK,KAAK;AACrC,YAAM,eAAe,MAAM;AAAA,IAC7B;AACA,UAAM,WAAW;AAAA,EACnB;AACA,iBAAe,OAAO,WAAW,CAAC,GAAG;AACnC,QAAI,CAAC,YAAY;AACf;AACF,eAAW,QAAQ,MAAMA,QAAO,mBAAmB,iBAAiB,iBAAiB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,QAAQ,CAAC;AACnH,QAAI,KAAK,OAAO;AACd,YAAM,iBAAiB,MAAM,WAAW,MAAM,eAAe;AAC7D,YAAM,eAAe,MAAM,KAAK,KAAK;AACrC,YAAM,eAAe,MAAM;AAAA,IAC7B;AACA,UAAM,WAAW;AAAA,EACnB;AACA,iBAAe,aAAa;AAC1B,QAAIF;AACJ,SAAK,QAAQ,QAAQA,MAAK,WAAW,UAAU,OAAO,SAASA,IAAG,QAAQ;AAAA,EAC5E;AACA,iBAAe,aAAa;AAC1B,QAAIA,KAAI;AACR,QAAI,MAAM,QAAQ,MAAM;AACtB,WAAK,QAAQ,QAAQA,MAAK,KAAK,UAAU,OAAO,SAASA,IAAG,KAAK;AACnE,QAAI,MAAM,QAAQ,MAAM;AACtB,WAAK,QAAQ,QAAQ,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,YAAY;AAC1E,QAAI,MAAM,QAAQ,MAAM;AACtB,WAAK,QAAQ,KAAK;AAAA,EACtB;AACA,QAAM,MAAM,MAAM,QAAQ,GAAG,UAAU;AACvC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,QAAQ,UAAU,CAAC,GAAG;AACtC,QAAM,EAAE,eAAe,MAAM,IAAI;AACjC,QAAM,eAAe,IAAI,KAAK;AAC9B,QAAM,gBAAgB,SAAS,MAAM,aAAa,MAAM,CAAC;AACzD,mBAAiB,eAAe,SAAS,MAAM,aAAa,QAAQ,IAAI;AACxE,mBAAiB,eAAe,QAAQ,MAAM,aAAa,QAAQ,KAAK;AACxE,QAAM,UAAU,SAAS;AAAA,IACvB,KAAK,MAAM,aAAa;AAAA,IACxB,IAAI,OAAO;AACT,UAAIA,KAAI;AACR,UAAI,CAAC,SAAS,aAAa;AACzB,SAACA,MAAK,cAAc,UAAU,OAAO,SAASA,IAAG,KAAK;AAAA,eAC/C,SAAS,CAAC,aAAa;AAC9B,SAAC,KAAK,cAAc,UAAU,OAAO,SAAS,GAAG,MAAM;AAAA,IAC3D;AAAA,EACF,CAAC;AACD,QAAM,eAAe,MAAM;AACzB,YAAQ,QAAQ;AAAA,EAClB,GAAG,EAAE,WAAW,MAAM,OAAO,OAAO,CAAC;AACrC,SAAO,EAAE,QAAQ;AACnB;AAEA,SAAS,eAAe,QAAQ,UAAU,CAAC,GAAG;AAC5C,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,gBAAgB,SAAS,MAAM,aAAa,MAAM,CAAC;AACzD,QAAM,UAAU,SAAS,MAAM,cAAc,SAAS,cAAc,QAAQ,cAAc,MAAM,SAAS,cAAc,KAAK,IAAI,KAAK;AACrI,SAAO,EAAE,QAAQ;AACnB;AAEA,SAAS,OAAO,SAAS;AACvB,MAAIA;AACJ,QAAM,MAAM,IAAI,CAAC;AACjB,MAAI,OAAO,gBAAgB;AACzB,WAAO;AACT,QAAM,SAASA,MAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAOA,MAAK;AAC7E,MAAI,OAAO,YAAY,IAAI;AAC3B,MAAI,QAAQ;AACZ,WAAS,MAAM;AACb,aAAS;AACT,QAAI,SAAS,OAAO;AAClB,YAAMwB,OAAM,YAAY,IAAI;AAC5B,YAAM,OAAOA,OAAM;AACnB,UAAI,QAAQ,KAAK,MAAM,OAAO,OAAO,MAAM;AAC3C,aAAOA;AACP,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,eAAe;AAAA,EACnB;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,cAAc,QAAQ,UAAU,CAAC,GAAG;AAC3C,QAAM,EAAE,UAAApB,YAAW,iBAAiB,WAAW,MAAM,IAAI;AACzD,QAAM,YAAY,WAAWA,aAAY,OAAO,SAASA,UAAS,cAAc,MAAM;AACtF,QAAM,eAAe,IAAI,KAAK;AAC9B,MAAI,MAAM,aAAa,CAAC;AACxB,QAAM,cAAc,aAAa,MAAM;AACrC,QAAI,CAACA,WAAU;AACb,aAAO;AAAA,IACT,OAAO;AACL,iBAAW,KAAK,cAAc;AAC5B,YAAI,EAAE,CAAC,KAAKA,WAAU;AACpB,gBAAM;AACN,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,CAAC,SAAS,MAAM,SAAS,EAAE,KAAK,IAAI;AAC1C,iBAAe,OAAO;AACpB,QAAI,CAAC,YAAY;AACf;AACF,QAAIA,aAAY,OAAO,SAASA,UAAS,OAAO;AAC9C,YAAMA,UAAS,IAAI,EAAE;AACvB,iBAAa,QAAQ;AAAA,EACvB;AACA,iBAAe,QAAQ;AACrB,QAAI,CAAC,YAAY;AACf;AACF,UAAM,KAAK;AACX,UAAM,UAAU,aAAa,SAAS;AACtC,QAAI,SAAS;AACX,YAAM,QAAQ,OAAO,EAAE;AACvB,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,iBAAe,SAAS;AACtB,QAAI,aAAa;AACf,YAAM,KAAK;AAAA;AAEX,YAAM,MAAM;AAAA,EAChB;AACA,MAAIA,WAAU;AACZ,qBAAiBA,WAAU,OAAO,MAAM;AACtC,mBAAa,QAAQ,CAAC,EAAEA,aAAY,OAAO,SAASA,UAAS,OAAO;AAAA,IACtE,GAAG,KAAK;AAAA,EACV;AACA,MAAI;AACF,sBAAkB,IAAI;AACxB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS;AAC9C,SAAO,SAAS,MAAM;AACpB,QAAI,QAAQ,OAAO;AACjB,aAAO;AAAA,QACL,SAAS;AAAA,UACP,GAAG,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC1B,GAAG,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC1B,GAAG,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC1B,GAAG,QAAQ,MAAM,QAAQ,CAAC;AAAA,QAC5B;AAAA,QACA,QAAQ;AAAA,UACN,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC7B,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAAA,QAChC;AAAA,QACA,UAAU;AAAA,UACR,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC7B,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAAA,QAChC;AAAA,QACA,OAAO;AAAA,UACL,MAAM;AAAA,YACJ,YAAY,QAAQ,MAAM,KAAK,CAAC;AAAA,YAChC,UAAU,QAAQ,MAAM,KAAK,CAAC;AAAA,YAC9B,QAAQ,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAClC;AAAA,UACA,OAAO;AAAA,YACL,YAAY,QAAQ,MAAM,KAAK,CAAC;AAAA,YAChC,UAAU,QAAQ,MAAM,KAAK,CAAC;AAAA,YAC9B,QAAQ,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAClC;AAAA,QACF;AAAA,QACA,MAAM;AAAA,UACJ,IAAI,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAC5B,MAAM,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAC9B,MAAM,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAC9B,OAAO,QAAQ,MAAM,QAAQ,EAAE;AAAA,QACjC;AAAA,QACA,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,QAC7B,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAAA,MAChC;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM;AAAA,IACJ,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAM,aAAa,iBAAiB,SAAS;AAC9E,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,kBAAkB,gBAAgB;AACxC,QAAM,qBAAqB,gBAAgB;AAC3C,QAAM,mBAAmB,CAAC,YAAY;AACpC,UAAM,kBAAkB,CAAC;AACzB,UAAM,oBAAoB,uBAAuB,UAAU,QAAQ,oBAAoB;AACvF,QAAI;AACF,sBAAgB,KAAK,iBAAiB;AACxC,QAAI,QAAQ;AACV,sBAAgB,KAAK,GAAG,QAAQ,eAAe;AACjD,WAAO;AAAA,MACL,IAAI,QAAQ;AAAA,MACZ;AAAA,MACA,OAAO,QAAQ;AAAA,MACf,SAAS,QAAQ;AAAA,MACjB,WAAW,QAAQ;AAAA,MACnB,WAAW,QAAQ;AAAA,MACnB,MAAM,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI;AAAA,MACrC,SAAS,QAAQ,QAAQ,IAAI,CAAC,YAAY,EAAE,SAAS,OAAO,SAAS,SAAS,OAAO,SAAS,OAAO,OAAO,MAAM,EAAE;AAAA,IACtH;AAAA,EACF;AACA,QAAM,qBAAqB,MAAM;AAC/B,UAAM,aAAa,aAAa,OAAO,SAAS,UAAU,YAAY,MAAM,CAAC;AAC7E,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,YAAM,UAAU,UAAU,CAAC;AAC3B,UAAI,SAAS;AACX,cAAM,QAAQ,SAAS,MAAM,UAAU,CAAC,EAAE,OAAO,OAAO,MAAM,WAAW,QAAQ,KAAK;AACtF,YAAI,QAAQ;AACV,mBAAS,MAAM,KAAK,IAAI,iBAAiB,OAAO;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,UAAU,OAAO,OAAO,IAAI,SAAS,kBAAkB;AAC/D,QAAM,qBAAqB,CAAC,YAAY;AACtC,QAAI,CAAC,SAAS,MAAM,KAAK,CAAC,EAAE,MAAM,MAAM,UAAU,QAAQ,KAAK,GAAG;AAChE,eAAS,MAAM,KAAK,iBAAiB,OAAO,CAAC;AAC7C,sBAAgB,QAAQ,QAAQ,KAAK;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACA,QAAM,wBAAwB,CAAC,YAAY;AACzC,aAAS,QAAQ,SAAS,MAAM,OAAO,CAAC,MAAM,EAAE,UAAU,QAAQ,KAAK;AACvE,uBAAmB,QAAQ,QAAQ,KAAK;AAAA,EAC1C;AACA,mBAAiB,oBAAoB,CAAC,MAAM,mBAAmB,EAAE,OAAO,CAAC;AACzE,mBAAiB,uBAAuB,CAAC,MAAM,sBAAsB,EAAE,OAAO,CAAC;AAC/E,eAAa,MAAM;AACjB,UAAM,aAAa,aAAa,OAAO,SAAS,UAAU,YAAY,MAAM,CAAC;AAC7E,QAAI,WAAW;AACb,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,cAAM,UAAU,UAAU,CAAC;AAC3B,YAAI;AACF,6BAAmB,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM;AACN,SAAO;AAAA,IACL;AAAA,IACA,aAAa,gBAAgB;AAAA,IAC7B,gBAAgB,mBAAmB;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM;AAAA,IACJ,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAM,aAAa,iBAAiB,SAAS;AAC9E,QAAM,YAAY,IAAI,IAAI;AAC1B,QAAM,QAAQ,IAAI,IAAI;AACtB,QAAM,SAAS,IAAI;AAAA,IACjB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,OAAO;AAAA,EACT,CAAC;AACD,WAAS,eAAe,UAAU;AAChC,cAAU,QAAQ,SAAS;AAC3B,WAAO,QAAQ,SAAS;AACxB,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI;AACJ,WAAS,SAAS;AAChB,QAAI,YAAY,OAAO;AACrB,gBAAU,UAAU,YAAY,cAAc,gBAAgB,CAAC,QAAQ,MAAM,QAAQ,KAAK;AAAA,QACxF;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI;AACF,WAAO;AACT,WAAS,QAAQ;AACf,QAAI,WAAW;AACb,gBAAU,YAAY,WAAW,OAAO;AAAA,EAC5C;AACA,oBAAkB,MAAM;AACtB,UAAM;AAAA,EACR,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,CAAC,aAAa,aAAa,UAAU,WAAW,cAAc,OAAO;AAC7F,IAAM,YAAY;AAClB,SAAS,QAAQ,UAAU,WAAW,UAAU,CAAC,GAAG;AAClD,QAAM;AAAA,IACJ,eAAe;AAAA,IACf,4BAA4B;AAAA,IAC5B,QAAAH,UAAS;AAAA,IACT,QAAAC,UAAS;AAAA,IACT,cAAc,eAAe,EAAE;AAAA,EACjC,IAAI;AACJ,QAAM,OAAO,IAAI,YAAY;AAC7B,QAAM,aAAa,IAAI,UAAU,CAAC;AAClC,MAAI;AACJ,QAAM,UAAU,oBAAoB,aAAa,MAAM;AACrD,SAAK,QAAQ;AACb,eAAW,QAAQ,UAAU;AAC7B,iBAAa,KAAK;AAClB,YAAQ,WAAW,MAAM,KAAK,QAAQ,MAAM,OAAO;AAAA,EACrD,CAAC;AACD,MAAIA,SAAQ;AACV,UAAME,YAAWF,QAAO;AACxB,eAAW,SAASD;AAClB,uBAAiBC,SAAQ,OAAO,SAAS,EAAE,SAAS,KAAK,CAAC;AAC5D,QAAI,2BAA2B;AAC7B,uBAAiBE,WAAU,oBAAoB,MAAM;AACnD,YAAI,CAACA,UAAS;AACZ,kBAAQ;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AACA,UAAQ,WAAW,MAAM,KAAK,QAAQ,MAAM,OAAO;AACnD,SAAO,EAAE,MAAM,WAAW;AAC5B;AAEA,IAAI,cAAc,OAAO;AACzB,IAAIqB,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAID,gBAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAID;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,eAAe,UAAU,SAAS;AAChC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,MAAM,IAAI,MAAM;AACtB,UAAM,EAAE,KAAK,QAAQ,MAAM,IAAI;AAC/B,QAAI,MAAM;AACV,QAAI;AACF,UAAI,SAAS;AACf,QAAI;AACF,UAAI,QAAQ;AACd,QAAI,SAAS,MAAM,QAAQ,GAAG;AAC9B,QAAI,UAAU;AAAA,EAChB,CAAC;AACH;AACA,IAAM,WAAW,CAAC,SAAS,oBAAoB,CAAC,MAAM;AACpD,QAAM,QAAQ,cAAc,MAAM,UAAU,aAAa,OAAO,CAAC,GAAG,QAAQ,iBAAiB;AAAA,IAC3F,gBAAgB;AAAA,EAClB,GAAG,iBAAiB,CAAC;AACrB,QAAM,MAAM,aAAa,OAAO,GAAG,MAAM,MAAM,QAAQ,kBAAkB,KAAK,GAAG,EAAE,MAAM,KAAK,CAAC;AAC/F,SAAO;AACT;AAEA,IAAM,iCAAiC;AACvC,SAAS,UAAU,SAAS,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,uBAAuB;AAAA,MACrB,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,EACb,IAAI;AACJ,QAAM,YAAY,IAAI,CAAC;AACvB,QAAM,YAAY,IAAI,CAAC;AACvB,QAAM,IAAI,SAAS;AAAA,IACjB,MAAM;AACJ,aAAO,UAAU;AAAA,IACnB;AAAA,IACA,IAAI,IAAI;AACN,eAAS,IAAI,MAAM;AAAA,IACrB;AAAA,EACF,CAAC;AACD,QAAM,IAAI,SAAS;AAAA,IACjB,MAAM;AACJ,aAAO,UAAU;AAAA,IACnB;AAAA,IACA,IAAI,IAAI;AACN,eAAS,QAAQ,EAAE;AAAA,IACrB;AAAA,EACF,CAAC;AACD,WAAS,SAAS,IAAI,IAAI;AACxB,QAAI3B,KAAI,IAAI;AACZ,UAAM,WAAW,aAAa,OAAO;AACrC,QAAI,CAAC;AACH;AACF,KAAC,KAAK,oBAAoB,WAAW,SAAS,OAAO,aAAa,OAAO,SAAS,GAAG,SAAS;AAAA,MAC5F,MAAMA,MAAK,aAAa,EAAE,MAAM,OAAOA,MAAK,EAAE;AAAA,MAC9C,OAAO,KAAK,aAAa,EAAE,MAAM,OAAO,KAAK,EAAE;AAAA,MAC/C,UAAU,aAAa,QAAQ;AAAA,IACjC,CAAC;AAAA,EACH;AACA,QAAM,cAAc,IAAI,KAAK;AAC7B,QAAM,eAAe,SAAS;AAAA,IAC5B,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,aAAa,SAAS;AAAA,IAC1B,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,cAAc,CAAC,MAAM;AACzB,QAAI,CAAC,YAAY;AACf;AACF,gBAAY,QAAQ;AACpB,eAAW,OAAO;AAClB,eAAW,QAAQ;AACnB,eAAW,MAAM;AACjB,eAAW,SAAS;AACpB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,uBAAuB,cAAc,aAAa,WAAW,IAAI;AACvE,QAAM,kBAAkB,CAAC,MAAM;AAC7B,UAAM,cAAc,EAAE,WAAW,WAAW,EAAE,OAAO,kBAAkB,EAAE;AACzE,UAAM,aAAa,YAAY;AAC/B,eAAW,OAAO,aAAa,UAAU;AACzC,eAAW,QAAQ,aAAa,UAAU;AAC1C,iBAAa,OAAO,cAAc,KAAK,OAAO,QAAQ;AACtD,iBAAa,QAAQ,aAAa,YAAY,eAAe,YAAY,eAAe,OAAO,SAAS,KAAK;AAC7G,cAAU,QAAQ;AAClB,QAAI,YAAY,YAAY;AAC5B,QAAI,EAAE,WAAW,YAAY,CAAC;AAC5B,kBAAY,SAAS,KAAK;AAC5B,eAAW,MAAM,YAAY,UAAU;AACvC,eAAW,SAAS,YAAY,UAAU;AAC1C,iBAAa,MAAM,aAAa,KAAK,OAAO,OAAO;AACnD,iBAAa,SAAS,YAAY,YAAY,gBAAgB,YAAY,gBAAgB,OAAO,UAAU,KAAK;AAChH,cAAU,QAAQ;AAClB,gBAAY,QAAQ;AACpB,yBAAqB,CAAC;AACtB,aAAS,CAAC;AAAA,EACZ;AACA,mBAAiB,SAAS,UAAU,WAAW,cAAc,iBAAiB,UAAU,MAAM,KAAK,IAAI,iBAAiB,oBAAoB;AAC5I,mBAAiB,SAAS,aAAa,aAAa,oBAAoB;AACxE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI4B,eAAc,OAAO;AACzB,IAAIC,gBAAe,OAAO;AAC1B,IAAIC,uBAAsB,OAAO;AACjC,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMN,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIO,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAIE,mBAAkB,CAAC,GAAG,MAAMP,cAAa,GAAGC,qBAAoB,CAAC,CAAC;AACtE,SAAS,kBAAkB,SAAS,YAAY,UAAU,CAAC,GAAG;AAC5D,MAAI9B,KAAI;AACR,QAAM,aAAaA,MAAK,QAAQ,cAAc,OAAOA,MAAK;AAC1D,QAAM,QAAQ,SAAS,UAAU,SAASoC,iBAAgBD,kBAAiB,CAAC,GAAG,OAAO,GAAG;AAAA,IACvF,QAAQA,kBAAiB;AAAA,MACvB,CAAC,SAAS,IAAI,KAAK,QAAQ,aAAa,OAAO,KAAK;AAAA,IACtD,GAAG,QAAQ,MAAM;AAAA,EACnB,CAAC,CAAC,CAAC;AACH,QAAM,MAAM,MAAM,aAAa,SAAS,GAAG,OAAO,MAAM;AACtD,QAAIhC,MAAK;AACT,QAAI,GAAG;AACL,YAAM,OAAO,aAAa,OAAO;AACjC,YAAM,WAAW;AAAA,QACf,SAASA,OAAM,QAAQ,OAAO,SAAS,KAAK,iBAAiB,OAAOA,OAAM;AAAA,QAC1E,QAAQ,MAAM,QAAQ,OAAO,SAAS,KAAK,gBAAgB,OAAO,MAAM;AAAA,MAC1E;AACA,YAAM,WAAW,KAAK;AACtB,UAAI,QAAQ,0BAA0B,MAAM;AAC1C,iBAAS,MAAM;AACb,eAAK,SAAS;AAAA,YACZ,KAAK,KAAK,eAAe,SAAS;AAAA,YAClC,MAAM,KAAK,cAAc,SAAS;AAAA,UACpC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,wBAAwB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAAD,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,0BAA0BA,OAAM;AACjF,MAAI,UAAU;AACd,QAAM,YAAY,YAAY,QAAQ,MAAM,OAAO;AAAA,IACjD,IAAI,aAAa,MAAM;AAAA,IACvB,MAAM,aAAa,IAAI;AAAA,EACzB,IAAI,CAAC,EAAE,IAAI,MAAM,MAAM,MAAM;AAC3B,YAAQ;AACR,QAAI,CAAC;AACH;AACF,UAAM,WAAW,IAAI,qBAAqB,UAAU;AAAA,MAClD,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AACD,aAAS,QAAQ,EAAE;AACnB,cAAU,MAAM;AACd,eAAS,WAAW;AACpB,gBAAU;AAAA,IACZ;AAAA,EACF,GAAG,EAAE,WAAW,MAAM,OAAO,OAAO,CAAC,IAAI;AACzC,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,gBAAgB,CAAC,aAAa,WAAW,WAAW,OAAO;AACjE,SAAS,eAAe,UAAU,UAAU,CAAC,GAAG;AAC9C,QAAM;AAAA,IACJ,QAAAD,UAAS;AAAA,IACT,UAAAG,YAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,QAAQ,IAAI,OAAO;AACzB,MAAIA,WAAU;AACZ,IAAAH,QAAO,QAAQ,CAAC,kBAAkB;AAChC,uBAAiBG,WAAU,eAAe,CAAC,QAAQ;AACjD,YAAI,OAAO,IAAI,qBAAqB;AAClC,gBAAM,QAAQ,IAAI,iBAAiB,QAAQ;AAAA,MAC/C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAK,cAAc,UAAU,CAAC,GAAG;AACxD,QAAM,EAAE,QAAAF,UAAS,cAAc,IAAI;AACnC,SAAO,WAAW,KAAK,cAAcA,WAAU,OAAO,SAASA,QAAO,cAAc,OAAO;AAC7F;AAEA,IAAM,2BAA2B;AAAA,EAC/B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AACT;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,UAAU,cAAc;AAAA,IACxB,SAAS;AAAA,IACT,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,EACjB,IAAI;AACJ,QAAM,UAAU,SAAS,oBAAI,IAAI,CAAC;AAClC,QAAM,MAAM;AAAA,IACV,SAAS;AACP,aAAO,CAAC;AAAA,IACV;AAAA,IACA;AAAA,EACF;AACA,QAAM,OAAO,cAAc,SAAS,GAAG,IAAI;AAC3C,QAAM,WAAW,oBAAI,IAAI;AACzB,QAAM,WAAW,oBAAI,IAAI;AACzB,WAAS,QAAQ,KAAK,OAAO;AAC3B,QAAI,OAAO,MAAM;AACf,UAAI;AACF,aAAK,GAAG,IAAI;AAAA;AAEZ,aAAK,GAAG,EAAE,QAAQ;AAAA,IACtB;AAAA,EACF;AACA,WAAS,QAAQ;AACf,YAAQ,MAAM;AACd,eAAW,OAAO;AAChB,cAAQ,KAAK,KAAK;AAAA,EACtB;AACA,WAAS,WAAW,GAAG,OAAO;AAC5B,QAAIF,KAAI;AACR,UAAM,OAAOA,MAAK,EAAE,QAAQ,OAAO,SAASA,IAAG,YAAY;AAC3D,UAAM,QAAQ,KAAK,EAAE,SAAS,OAAO,SAAS,GAAG,YAAY;AAC7D,UAAM,SAAS,CAAC,MAAM,GAAG,EAAE,OAAO,OAAO;AACzC,QAAI,KAAK;AACP,UAAI;AACF,gBAAQ,IAAI,GAAG;AAAA;AAEf,gBAAQ,OAAO,GAAG;AAAA,IACtB;AACA,eAAW,QAAQ,QAAQ;AACzB,eAAS,IAAI,IAAI;AACjB,cAAQ,MAAM,KAAK;AAAA,IACrB;AACA,QAAI,QAAQ,UAAU,CAAC,OAAO;AAC5B,eAAS,QAAQ,CAAC,SAAS;AACzB,gBAAQ,OAAO,IAAI;AACnB,gBAAQ,MAAM,KAAK;AAAA,MACrB,CAAC;AACD,eAAS,MAAM;AAAA,IACjB,WAAW,OAAO,EAAE,qBAAqB,cAAc,EAAE,iBAAiB,MAAM,KAAK,OAAO;AAC1F,OAAC,GAAG,SAAS,GAAG,MAAM,EAAE,QAAQ,CAAC,SAAS,SAAS,IAAI,IAAI,CAAC;AAAA,IAC9D;AAAA,EACF;AACA,mBAAiB,QAAQ,WAAW,CAAC,MAAM;AACzC,eAAW,GAAG,IAAI;AAClB,WAAO,aAAa,CAAC;AAAA,EACvB,GAAG,EAAE,QAAQ,CAAC;AACd,mBAAiB,QAAQ,SAAS,CAAC,MAAM;AACvC,eAAW,GAAG,KAAK;AACnB,WAAO,aAAa,CAAC;AAAA,EACvB,GAAG,EAAE,QAAQ,CAAC;AACd,mBAAiB,QAAQ,OAAO,EAAE,SAAS,KAAK,CAAC;AACjD,mBAAiB,SAAS,OAAO,EAAE,SAAS,KAAK,CAAC;AAClD,QAAM,QAAQ,IAAI,MAAM,MAAM;AAAA,IAC5B,IAAI,SAAS,MAAM,KAAK;AACtB,UAAI,OAAO,SAAS;AAClB,eAAO,QAAQ,IAAI,SAAS,MAAM,GAAG;AACvC,aAAO,KAAK,YAAY;AACxB,UAAI,QAAQ;AACV,eAAO,SAAS,IAAI;AACtB,UAAI,EAAE,QAAQ,OAAO;AACnB,YAAI,QAAQ,KAAK,IAAI,GAAG;AACtB,gBAAMqC,QAAO,KAAK,MAAM,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AACrD,eAAK,IAAI,IAAI,SAAS,MAAMA,MAAK,MAAM,CAAC,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,QACpE,OAAO;AACL,eAAK,IAAI,IAAI,IAAI,KAAK;AAAA,QACxB;AAAA,MACF;AACA,YAAM,IAAI,QAAQ,IAAI,SAAS,MAAM,GAAG;AACxC,aAAO,cAAc,MAAM,CAAC,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAIC,eAAc,OAAO;AACzB,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMJ,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIK,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,WAAW,QAAQ,IAAI;AAC9B,MAAI,aAAa,MAAM;AACrB,OAAG,aAAa,MAAM,CAAC;AAC3B;AACA,SAAS,iBAAiB,YAAY;AACpC,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE;AACvC,aAAS,CAAC,GAAG,QAAQ,CAAC,WAAW,MAAM,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC,CAAC;AAC/D,SAAO;AACT;AACA,SAAS,cAAc,QAAQ;AAC7B,SAAO,MAAM,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,OAAO,MAAM,UAAU,MAAM,YAAY,MAAM,gCAAgC,GAAG,QAAQ,EAAE,IAAI,OAAO,MAAM,UAAU,MAAM,YAAY,MAAM,gCAAgC,EAAE;AACpN;AACA,IAAM,iBAAiB;AAAA,EACrB,KAAK;AAAA,EACL,QAAQ,CAAC;AACX;AACA,SAAS,iBAAiB,QAAQ,UAAU,CAAC,GAAG;AAC9C,YAAUC,kBAAiBA,kBAAiB,CAAC,GAAG,cAAc,GAAG,OAAO;AACxE,QAAM;AAAA,IACJ,UAAAvC,YAAW;AAAA,EACb,IAAI;AACJ,QAAM,cAAc,IAAI,CAAC;AACzB,QAAM,WAAW,IAAI,CAAC;AACtB,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,SAAS,IAAI,CAAC;AACpB,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,QAAQ,IAAI,KAAK;AACvB,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,OAAO,IAAI,CAAC;AAClB,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,SAAS,IAAI,CAAC,CAAC;AACrB,QAAM,gBAAgB,IAAI,EAAE;AAC5B,QAAM,qBAAqB,IAAI,KAAK;AACpC,QAAM,QAAQ,IAAI,KAAK;AACvB,QAAM,2BAA2BA,aAAY,6BAA6BA;AAC1E,QAAM,mBAAmB,gBAAgB;AACzC,QAAM,eAAe,CAAC,UAAU;AAC9B,eAAW,QAAQ,CAAC,OAAO;AACzB,UAAI,OAAO;AACT,cAAM,KAAK,SAAS,KAAK,IAAI,QAAQ,MAAM;AAC3C,WAAG,WAAW,EAAE,EAAE,OAAO;AAAA,MAC3B,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,GAAG,WAAW,QAAQ,EAAE;AAC1C,aAAG,WAAW,CAAC,EAAE,OAAO;AAAA,MAC5B;AACA,oBAAc,QAAQ;AAAA,IACxB,CAAC;AAAA,EACH;AACA,QAAM,cAAc,CAAC,OAAO,gBAAgB,SAAS;AACnD,eAAW,QAAQ,CAAC,OAAO;AACzB,YAAM,KAAK,SAAS,KAAK,IAAI,QAAQ,MAAM;AAC3C,UAAI;AACF,qBAAa;AACf,SAAG,WAAW,EAAE,EAAE,OAAO;AACzB,oBAAc,QAAQ;AAAA,IACxB,CAAC;AAAA,EACH;AACA,QAAM,yBAAyB,MAAM;AACnC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,iBAAW,QAAQ,OAAO,OAAO;AAC/B,YAAI,0BAA0B;AAC5B,cAAI,CAAC,mBAAmB,OAAO;AAC7B,eAAG,wBAAwB,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,UACzD,OAAO;AACL,YAAAA,UAAS,qBAAqB,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,UAC5D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,cAAY,MAAM;AAChB,QAAI,CAACA;AACH;AACF,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,UAAM,MAAM,aAAa,QAAQ,GAAG;AACpC,QAAI,UAAU,CAAC;AACf,QAAI,CAAC;AACH;AACF,QAAI,SAAS,GAAG;AACd,gBAAU,CAAC,EAAE,IAAI,CAAC;AAAA,aACX,MAAM,QAAQ,GAAG;AACxB,gBAAU;AAAA,aACH,SAAS,GAAG;AACnB,gBAAU,CAAC,GAAG;AAChB,OAAG,iBAAiB,QAAQ,EAAE,QAAQ,CAAC,MAAM;AAC3C,QAAE,oBAAoB,SAAS,iBAAiB,OAAO;AACvD,QAAE,OAAO;AAAA,IACX,CAAC;AACD,YAAQ,QAAQ,CAAC,EAAE,KAAK,MAAM,KAAK,MAAM;AACvC,YAAM,SAASA,UAAS,cAAc,QAAQ;AAC9C,aAAO,aAAa,OAAO,IAAI;AAC/B,aAAO,aAAa,QAAQ,QAAQ,EAAE;AACtC,aAAO,iBAAiB,SAAS,iBAAiB,OAAO;AACzD,SAAG,YAAY,MAAM;AAAA,IACvB,CAAC;AACD,OAAG,KAAK;AAAA,EACV,CAAC;AACD,oBAAkB,MAAM;AACtB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,OAAG,iBAAiB,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,oBAAoB,SAAS,iBAAiB,OAAO,CAAC;AAAA,EACvG,CAAC;AACD,QAAM,QAAQ,CAAC,QAAQ;AACrB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,OAAG,SAAS;AAAA,EACd,CAAC;AACD,QAAM,OAAO,CAAC,SAAS;AACrB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,OAAG,QAAQ;AAAA,EACb,CAAC;AACD,QAAM,MAAM,CAAC,UAAU;AACrB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,OAAG,eAAe;AAAA,EACpB,CAAC;AACD,cAAY,MAAM;AAChB,QAAI,CAACA;AACH;AACF,UAAM,aAAa,aAAa,QAAQ,MAAM;AAC9C,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC;AACxC;AACF,OAAG,iBAAiB,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;AACtD,eAAW,QAAQ,CAAC,EAAE,SAAS,WAAW,MAAM,OAAO,KAAK,QAAQ,GAAG,MAAM;AAC3E,YAAM,QAAQA,UAAS,cAAc,OAAO;AAC5C,YAAM,UAAU,aAAa;AAC7B,YAAM,OAAO;AACb,YAAM,QAAQ;AACd,YAAM,MAAM;AACZ,YAAM,UAAU;AAChB,UAAI,MAAM;AACR,sBAAc,QAAQ;AACxB,SAAG,YAAY,KAAK;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACD,QAAM,EAAE,eAAe,yBAAyB,IAAI,eAAe,aAAa,CAAC,SAAS;AACxF,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,OAAG,cAAc;AAAA,EACnB,CAAC;AACD,QAAM,EAAE,eAAe,qBAAqB,IAAI,eAAe,SAAS,CAAC,cAAc;AACrF,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,gBAAY,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,EACnC,CAAC;AACD,mBAAiB,QAAQ,cAAc,MAAM,yBAAyB,MAAM,YAAY,QAAQ,aAAa,MAAM,EAAE,WAAW,CAAC;AACjI,mBAAiB,QAAQ,kBAAkB,MAAM,SAAS,QAAQ,aAAa,MAAM,EAAE,QAAQ;AAC/F,mBAAiB,QAAQ,YAAY,MAAM,SAAS,QAAQ,iBAAiB,aAAa,MAAM,EAAE,QAAQ,CAAC;AAC3G,mBAAiB,QAAQ,WAAW,MAAM,QAAQ,QAAQ,IAAI;AAC9D,mBAAiB,QAAQ,UAAU,MAAM,QAAQ,QAAQ,KAAK;AAC9D,mBAAiB,QAAQ,WAAW,MAAM,QAAQ,QAAQ,IAAI;AAC9D,mBAAiB,QAAQ,WAAW,MAAM;AACxC,YAAQ,QAAQ;AAChB,UAAM,QAAQ;AAAA,EAChB,CAAC;AACD,mBAAiB,QAAQ,cAAc,MAAM,KAAK,QAAQ,aAAa,MAAM,EAAE,YAAY;AAC3F,mBAAiB,QAAQ,WAAW,MAAM,QAAQ,QAAQ,IAAI;AAC9D,mBAAiB,QAAQ,SAAS,MAAM,MAAM,QAAQ,IAAI;AAC1D,mBAAiB,QAAQ,SAAS,MAAM,qBAAqB,MAAM,QAAQ,QAAQ,KAAK,CAAC;AACzF,mBAAiB,QAAQ,QAAQ,MAAM,qBAAqB,MAAM,QAAQ,QAAQ,IAAI,CAAC;AACvF,mBAAiB,QAAQ,yBAAyB,MAAM,mBAAmB,QAAQ,IAAI;AACvF,mBAAiB,QAAQ,yBAAyB,MAAM,mBAAmB,QAAQ,KAAK;AACxF,mBAAiB,QAAQ,gBAAgB,MAAM;AAC7C,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,WAAO,QAAQ,GAAG;AAClB,UAAM,QAAQ,GAAG;AAAA,EACnB,CAAC;AACD,QAAM,YAAY,CAAC;AACnB,QAAM,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM;AACjC,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC;AACH;AACF,SAAK;AACL,cAAU,CAAC,IAAI,iBAAiB,GAAG,YAAY,YAAY,MAAM,OAAO,QAAQ,cAAc,GAAG,UAAU,CAAC;AAC5G,cAAU,CAAC,IAAI,iBAAiB,GAAG,YAAY,eAAe,MAAM,OAAO,QAAQ,cAAc,GAAG,UAAU,CAAC;AAC/G,cAAU,CAAC,IAAI,iBAAiB,GAAG,YAAY,UAAU,MAAM,OAAO,QAAQ,cAAc,GAAG,UAAU,CAAC;AAAA,EAC5G,CAAC;AACD,oBAAkB,MAAM,UAAU,QAAQ,CAAC,aAAa,SAAS,CAAC,CAAC;AACnE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe,iBAAiB;AAAA,EAClC;AACF;AAEA,IAAM,mBAAmB,MAAM;AAC7B,QAAM,OAAO,SAAS,CAAC,CAAC;AACxB,SAAO;AAAA,IACL,KAAK,CAAC,QAAQ,KAAK,GAAG;AAAA,IACtB,KAAK,CAAC,KAAK,UAAU,IAAI,MAAM,KAAK,KAAK;AAAA,IACzC,KAAK,CAAC,QAAQ,OAAO,MAAM,GAAG;AAAA,IAC9B,QAAQ,CAAC,QAAQ,IAAI,MAAM,GAAG;AAAA,IAC9B,OAAO,MAAM;AACX,aAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,QAAQ;AACjC,YAAI,MAAM,GAAG;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,WAAW,UAAU,SAAS;AACrC,QAAM,YAAY,MAAM;AACtB,QAAI,WAAW,OAAO,SAAS,QAAQ;AACrC,aAAO,SAAS,QAAQ,KAAK;AAC/B,QAAI;AACF,aAAO,iBAAiB;AAC1B,WAAO,SAAS,oBAAI,IAAI,CAAC;AAAA,EAC3B;AACA,QAAM,QAAQ,UAAU;AACxB,QAAM,cAAc,IAAI,UAAU,WAAW,OAAO,SAAS,QAAQ,UAAU,QAAQ,OAAO,GAAG,IAAI,IAAI,KAAK,UAAU,IAAI;AAC5H,QAAM,YAAY,CAAC,QAAQ,SAAS;AAClC,UAAM,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC;AAChC,WAAO,MAAM,IAAI,GAAG;AAAA,EACtB;AACA,QAAM,WAAW,IAAI,SAAS,UAAU,YAAY,GAAG,IAAI,GAAG,GAAG,IAAI;AACrE,QAAM,aAAa,IAAI,SAAS;AAC9B,UAAM,OAAO,YAAY,GAAG,IAAI,CAAC;AAAA,EACnC;AACA,QAAM,YAAY,MAAM;AACtB,UAAM,MAAM;AAAA,EACd;AACA,QAAM,WAAW,IAAI,SAAS;AAC5B,UAAM,MAAM,YAAY,GAAG,IAAI;AAC/B,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,MAAM,IAAI,GAAG;AACtB,WAAO,UAAU,KAAK,GAAG,IAAI;AAAA,EAC/B;AACA,WAAS,OAAO;AAChB,WAAS,SAAS;AAClB,WAAS,QAAQ;AACjB,WAAS,cAAc;AACvB,WAAS,QAAQ;AACjB,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,CAAC,GAAG;AAC/B,QAAM,SAAS,IAAI;AACnB,QAAM,cAAc,aAAa,MAAM,OAAO,gBAAgB,eAAe,YAAY,WAAW;AACpG,MAAI,YAAY,OAAO;AACrB,UAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,kBAAc,MAAM;AAClB,aAAO,QAAQ,YAAY;AAAA,IAC7B,GAAG,UAAU,EAAE,WAAW,QAAQ,WAAW,mBAAmB,QAAQ,kBAAkB,CAAC;AAAA,EAC7F;AACA,SAAO,EAAE,aAAa,OAAO;AAC/B;AAEA,SAAS,aAAa;AACpB,QAAM,YAAY,IAAI,KAAK;AAC3B,YAAU,MAAM;AACd,cAAU,QAAQ;AAAA,EACpB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,SAAS,UAAU,CAAC,GAAG;AAC9B,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,mBAAmB;AAAA,IACnB,eAAe,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IAC5B,QAAAF,UAAS;AAAA,IACT;AAAA,EACF,IAAI;AACJ,QAAM,IAAI,IAAI,aAAa,CAAC;AAC5B,QAAM,IAAI,IAAI,aAAa,CAAC;AAC5B,QAAM,aAAa,IAAI,IAAI;AAC3B,QAAM,eAAe,CAAC,UAAU;AAC9B,QAAI,SAAS,QAAQ;AACnB,QAAE,QAAQ,MAAM;AAChB,QAAE,QAAQ,MAAM;AAAA,IAClB,WAAW,SAAS,UAAU;AAC5B,QAAE,QAAQ,MAAM;AAChB,QAAE,QAAQ,MAAM;AAAA,IAClB,WAAW,SAAS,YAAY;AAC9B,QAAE,QAAQ,MAAM;AAChB,QAAE,QAAQ,MAAM;AAAA,IAClB;AACA,eAAW,QAAQ;AAAA,EACrB;AACA,QAAM,QAAQ,MAAM;AAClB,MAAE,QAAQ,aAAa;AACvB,MAAE,QAAQ,aAAa;AAAA,EACzB;AACA,QAAM,eAAe,CAAC,UAAU;AAC9B,QAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,YAAM,SAAS,MAAM,QAAQ,CAAC;AAC9B,UAAI,SAAS,QAAQ;AACnB,UAAE,QAAQ,OAAO;AACjB,UAAE,QAAQ,OAAO;AAAA,MACnB,WAAW,SAAS,UAAU;AAC5B,UAAE,QAAQ,OAAO;AACjB,UAAE,QAAQ,OAAO;AAAA,MACnB;AACA,iBAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,QAAM,sBAAsB,CAAC,UAAU;AACrC,WAAO,gBAAgB,SAAS,aAAa,KAAK,IAAI,YAAY,MAAM,aAAa,KAAK,GAAG,CAAC,CAAC;AAAA,EACjG;AACA,QAAM,sBAAsB,CAAC,UAAU;AACrC,WAAO,gBAAgB,SAAS,aAAa,KAAK,IAAI,YAAY,MAAM,aAAa,KAAK,GAAG,CAAC,CAAC;AAAA,EACjG;AACA,MAAIA,SAAQ;AACV,qBAAiBA,SAAQ,aAAa,qBAAqB,EAAE,SAAS,KAAK,CAAC;AAC5E,qBAAiBA,SAAQ,YAAY,qBAAqB,EAAE,SAAS,KAAK,CAAC;AAC3E,QAAI,SAAS,SAAS,YAAY;AAChC,uBAAiBA,SAAQ,cAAc,qBAAqB,EAAE,SAAS,KAAK,CAAC;AAC7E,uBAAiBA,SAAQ,aAAa,qBAAqB,EAAE,SAAS,KAAK,CAAC;AAC5E,UAAI;AACF,yBAAiBA,SAAQ,YAAY,OAAO,EAAE,SAAS,KAAK,CAAC;AAAA,IACjE;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,QAAQ,UAAU,CAAC,GAAG;AAC/C,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,EAAE,GAAG,GAAG,WAAW,IAAI,SAAS,OAAO;AAC7C,QAAM,YAAY,IAAI,UAAU,OAAO,SAASA,WAAU,OAAO,SAASA,QAAO,SAAS,IAAI;AAC9F,QAAM,WAAW,IAAI,CAAC;AACtB,QAAM,WAAW,IAAI,CAAC;AACtB,QAAM,mBAAmB,IAAI,CAAC;AAC9B,QAAM,mBAAmB,IAAI,CAAC;AAC9B,QAAM,gBAAgB,IAAI,CAAC;AAC3B,QAAM,eAAe,IAAI,CAAC;AAC1B,QAAM,YAAY,IAAI,IAAI;AAC1B,MAAI,OAAO,MAAM;AAAA,EACjB;AACA,MAAIA,SAAQ;AACV,WAAO,MAAM,CAAC,WAAW,GAAG,CAAC,GAAG,MAAM;AACpC,YAAM,KAAK,aAAa,SAAS;AACjC,UAAI,CAAC;AACH;AACF,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,GAAG,sBAAsB;AAC7B,uBAAiB,QAAQ,OAAOA,QAAO;AACvC,uBAAiB,QAAQ,MAAMA,QAAO;AACtC,oBAAc,QAAQ;AACtB,mBAAa,QAAQ;AACrB,YAAM,MAAM,EAAE,QAAQ,iBAAiB;AACvC,YAAM,MAAM,EAAE,QAAQ,iBAAiB;AACvC,gBAAU,QAAQ,UAAU,KAAK,WAAW,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,SAAS,MAAM;AAC5F,UAAI,iBAAiB,CAAC,UAAU,OAAO;AACrC,iBAAS,QAAQ;AACjB,iBAAS,QAAQ;AAAA,MACnB;AAAA,IACF,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,qBAAiB,UAAU,cAAc,MAAM;AAC7C,gBAAU,QAAQ;AAAA,IACpB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,UAAU,CAAC,GAAG;AACrC,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,aAAa,IAAI,IAAI;AAC3B,MAAI,CAACA,SAAQ;AACX,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,CAAC,YAAY,MAAM;AACnC,YAAQ,QAAQ;AAChB,eAAW,QAAQ;AAAA,EACrB;AACA,QAAM,aAAa,MAAM;AACvB,YAAQ,QAAQ;AAChB,eAAW,QAAQ;AAAA,EACrB;AACA,QAAM,SAAS,SAAS,MAAM,aAAa,QAAQ,MAAM,KAAKA,OAAM;AACpE,mBAAiB,QAAQ,aAAa,UAAU,OAAO,GAAG,EAAE,SAAS,KAAK,CAAC;AAC3E,mBAAiBA,SAAQ,cAAc,YAAY,EAAE,SAAS,KAAK,CAAC;AACpE,mBAAiBA,SAAQ,WAAW,YAAY,EAAE,SAAS,KAAK,CAAC;AACjE,MAAI,MAAM;AACR,qBAAiB,QAAQ,aAAa,UAAU,OAAO,GAAG,EAAE,SAAS,KAAK,CAAC;AAC3E,qBAAiBA,SAAQ,QAAQ,YAAY,EAAE,SAAS,KAAK,CAAC;AAC9D,qBAAiBA,SAAQ,WAAW,YAAY,EAAE,SAAS,KAAK,CAAC;AAAA,EACnE;AACA,MAAI,OAAO;AACT,qBAAiB,QAAQ,cAAc,UAAU,OAAO,GAAG,EAAE,SAAS,KAAK,CAAC;AAC5E,qBAAiBA,SAAQ,YAAY,YAAY,EAAE,SAAS,KAAK,CAAC;AAClE,qBAAiBA,SAAQ,eAAe,YAAY,EAAE,SAAS,KAAK,CAAC;AAAA,EACvE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI0C,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,eAAc,CAAC,QAAQ,YAAY;AACrC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAIF,gBAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQD;AACpB,aAAS,QAAQA,uBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAKE,gBAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,SAAS,oBAAoB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC3D,QAAM9C,MAAK,SAAS,EAAE,QAAAE,UAAS,cAAc,IAAIF,KAAI,kBAAkB+C,aAAY/C,KAAI,CAAC,QAAQ,CAAC;AACjG,MAAI;AACJ,QAAM,cAAc,aAAa,MAAME,WAAU,sBAAsBA,OAAM;AAC7E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,YAAY,MAAM,MAAM,aAAa,MAAM,GAAG,CAAC,OAAO;AAC1D,YAAQ;AACR,QAAI,YAAY,SAASA,WAAU,IAAI;AACrC,iBAAW,IAAI,iBAAiB,QAAQ;AACxC,eAAS,QAAQ,IAAI,eAAe;AAAA,IACtC;AAAA,EACF,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,uBAAuB,CAAC,UAAU,CAAC,MAAM;AAC7C,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,QAAM,YAAYA,WAAU,OAAO,SAASA,QAAO;AACnD,QAAM,cAAc,aAAa,MAAM,aAAa,cAAc,SAAS;AAC3E,QAAM,WAAW,IAAI,aAAa,OAAO,SAAS,UAAU,QAAQ;AACpE,mBAAiBA,SAAQ,kBAAkB,MAAM;AAC/C,QAAI;AACF,eAAS,QAAQ,UAAU;AAAA,EAC/B,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,QAAM,YAAYA,WAAU,OAAO,SAASA,QAAO;AACnD,QAAM,cAAc,aAAa,MAAM,aAAa,gBAAgB,SAAS;AAC7E,QAAM,WAAW,IAAI,IAAI;AACzB,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,YAAY,IAAI,MAAM;AAC5B,QAAM,WAAW,IAAI,MAAM;AAC3B,QAAM,WAAW,IAAI,MAAM;AAC3B,QAAM,cAAc,IAAI,MAAM;AAC9B,QAAM,MAAM,IAAI,MAAM;AACtB,QAAM,gBAAgB,IAAI,MAAM;AAChC,QAAM,OAAO,IAAI,SAAS;AAC1B,QAAM,aAAa,YAAY,SAAS,UAAU;AAClD,WAAS,2BAA2B;AAClC,QAAI,CAAC;AACH;AACF,aAAS,QAAQ,UAAU;AAC3B,cAAU,QAAQ,SAAS,QAAQ,SAAS,KAAK,IAAI;AACrD,aAAS,QAAQ,SAAS,QAAQ,KAAK,IAAI,IAAI;AAC/C,QAAI,YAAY;AACd,eAAS,QAAQ,WAAW;AAC5B,kBAAY,QAAQ,WAAW;AAC/B,oBAAc,QAAQ,WAAW;AACjC,UAAI,QAAQ,WAAW;AACvB,eAAS,QAAQ,WAAW;AAC5B,WAAK,QAAQ,WAAW;AAAA,IAC1B;AAAA,EACF;AACA,MAAIA,SAAQ;AACV,qBAAiBA,SAAQ,WAAW,MAAM;AACxC,eAAS,QAAQ;AACjB,gBAAU,QAAQ,KAAK,IAAI;AAAA,IAC7B,CAAC;AACD,qBAAiBA,SAAQ,UAAU,MAAM;AACvC,eAAS,QAAQ;AACjB,eAAS,QAAQ,KAAK,IAAI;AAAA,IAC5B,CAAC;AAAA,EACH;AACA,MAAI;AACF,qBAAiB,YAAY,UAAU,0BAA0B,KAAK;AACxE,2BAAyB;AACzB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI8C,eAAc,OAAO;AACzB,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMJ,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIK,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,OAAO,UAAU,CAAC,GAAG;AAC5B,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B,WAAW;AAAA,EACb,IAAI;AACJ,QAAM5B,OAAM,IAAI,oBAAI,KAAK,CAAC;AAC1B,QAAM,SAAS,MAAMA,KAAI,QAAQ,oBAAI,KAAK;AAC1C,QAAM,WAAW,aAAa,0BAA0B,SAAS,QAAQ,EAAE,WAAW,KAAK,CAAC,IAAI,cAAc,QAAQ,UAAU,EAAE,WAAW,KAAK,CAAC;AACnJ,MAAI,gBAAgB;AAClB,WAAO6B,kBAAiB;AAAA,MACtB,KAAA7B;AAAA,IACF,GAAG,QAAQ;AAAA,EACb,OAAO;AACL,WAAOA;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ;AAC5B,QAAM,MAAM,IAAI;AAChB,QAAM,UAAU,MAAM;AACpB,QAAI,IAAI;AACN,UAAI,gBAAgB,IAAI,KAAK;AAC/B,QAAI,QAAQ;AAAA,EACd;AACA,QAAM,MAAM,MAAM,MAAM,GAAG,CAAC,cAAc;AACxC,YAAQ;AACR,QAAI;AACF,UAAI,QAAQ,IAAI,gBAAgB,SAAS;AAAA,EAC7C,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,oBAAkB,OAAO;AACzB,SAAO,SAAS,GAAG;AACrB;AAEA,SAAS,SAAS,OAAO,KAAK,KAAK;AACjC,MAAI,WAAW,KAAK,KAAK,WAAW,KAAK;AACvC,WAAO,SAAS,MAAM,MAAM,aAAa,KAAK,GAAG,aAAa,GAAG,GAAG,aAAa,GAAG,CAAC,CAAC;AACxF,QAAM,SAAS,IAAI,KAAK;AACxB,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,aAAO,OAAO,QAAQ,MAAM,OAAO,OAAO,aAAa,GAAG,GAAG,aAAa,GAAG,CAAC;AAAA,IAChF;AAAA,IACA,IAAI,QAAQ;AACV,aAAO,QAAQ,MAAM,QAAQ,aAAa,GAAG,GAAG,aAAa,GAAG,CAAC;AAAA,IACnE;AAAA,EACF,CAAC;AACH;AAEA,SAAS,oBAAoB,SAAS;AACpC,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,EACtB,IAAI;AACJ,QAAM,kBAAkB,SAAS,UAAU,GAAG,QAAQ;AACtD,QAAM,YAAY,SAAS,MAAM,KAAK,IAAI,GAAG,KAAK,KAAK,MAAM,KAAK,IAAI,MAAM,eAAe,CAAC,CAAC,CAAC;AAC9F,QAAM,cAAc,SAAS,MAAM,GAAG,SAAS;AAC/C,QAAM,cAAc,SAAS,MAAM,YAAY,UAAU,CAAC;AAC1D,QAAM,aAAa,SAAS,MAAM,YAAY,UAAU,UAAU,KAAK;AACvE,MAAI,MAAM,IAAI;AACZ,YAAQ,MAAM,WAAW;AAC3B,MAAI,MAAM,QAAQ;AAChB,YAAQ,UAAU,eAAe;AACnC,WAAS,OAAO;AACd,gBAAY;AAAA,EACd;AACA,WAAS,OAAO;AACd,gBAAY;AAAA,EACd;AACA,QAAM,cAAc;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,aAAa,MAAM;AACvB,iBAAa,SAAS,WAAW,CAAC;AAAA,EACpC,CAAC;AACD,QAAM,iBAAiB,MAAM;AAC3B,qBAAiB,SAAS,WAAW,CAAC;AAAA,EACxC,CAAC;AACD,QAAM,WAAW,MAAM;AACrB,sBAAkB,SAAS,WAAW,CAAC;AAAA,EACzC,CAAC;AACD,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,CAAC,GAAG;AAC/B,QAAM,EAAE,SAAS,IAAI,WAAW,OAAO;AACvC,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM,EAAE,QAAAtB,UAAS,cAAc,IAAI;AACnC,QAAM,SAAS,IAAI,KAAK;AACxB,QAAM,UAAU,CAAC,UAAU;AACzB,QAAI,CAACA;AACH;AACF,YAAQ,SAASA,QAAO;AACxB,UAAM,OAAO,MAAM,iBAAiB,MAAM;AAC1C,WAAO,QAAQ,CAAC;AAAA,EAClB;AACA,MAAIA,SAAQ;AACV,qBAAiBA,SAAQ,YAAY,SAAS,EAAE,SAAS,KAAK,CAAC;AAC/D,qBAAiBA,QAAO,UAAU,cAAc,SAAS,EAAE,SAAS,KAAK,CAAC;AAC1E,qBAAiBA,QAAO,UAAU,cAAc,SAAS,EAAE,SAAS,KAAK,CAAC;AAAA,EAC5E;AACA,SAAO;AACT;AAEA,SAAS,YAAY,QAAQ,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,8BAA8B,CAAC,MAAM;AAAA,IACrC,8BAA8B,CAAC,MAAM;AAAA,IACrC,kBAAkB,CAAC,MAAM;AAAA,IACzB,kBAAkB,CAAC,MAAM;AAAA,IACzB,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,cAAc,SAAS,qBAAqB,EAAE,QAAAA,QAAO,CAAC,CAAC;AAC7D,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,EACjB,IAAI,kBAAkB,QAAQ,EAAE,eAAe,OAAO,QAAAA,QAAO,CAAC;AAC9D,QAAM,SAAS,SAAS,MAAM;AAC5B,QAAI,YAAY,gBAAgB,YAAY,SAAS,QAAQ,YAAY,UAAU,KAAK,YAAY,SAAS,QAAQ,YAAY,UAAU;AACzI,aAAO;AACT,WAAO;AAAA,EACT,CAAC;AACD,QAAM,OAAO,SAAS,MAAM;AAC1B,QAAI,OAAO,UAAU,qBAAqB;AACxC,YAAM,QAAQ,CAAC,YAAY,OAAO;AAClC,aAAO,4BAA4B,KAAK;AAAA,IAC1C,OAAO;AACL,YAAM,QAAQ,EAAE,EAAE,QAAQ,OAAO,QAAQ,KAAK,OAAO;AACrD,aAAO,gBAAgB,KAAK;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,QAAM,OAAO,SAAS,MAAM;AAC1B,QAAI,OAAO,UAAU,qBAAqB;AACxC,YAAM,QAAQ,YAAY,QAAQ;AAClC,aAAO,4BAA4B,KAAK;AAAA,IAC1C,OAAO;AACL,YAAM,SAAS,EAAE,QAAQ,MAAM,QAAQ,KAAK,MAAM;AAClD,aAAO,gBAAgB,KAAK;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,SAAO,EAAE,MAAM,MAAM,OAAO;AAC9B;AAEA,IAAIoD,eAAc,OAAO;AACzB,IAAIC,gBAAe,OAAO;AAC1B,IAAIC,uBAAsB,OAAO;AACjC,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMN,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIO,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAIE,mBAAkB,CAAC,GAAG,MAAMP,cAAa,GAAGC,qBAAoB,CAAC,CAAC;AACtE,IAAM,eAAe;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,OAAuB,OAAO,KAAK,YAAY;AACrD,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM;AAAA,IACJ,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,QAAQ,IAAI,QAAQ,gBAAgB,CAAC,CAAC;AAC5C,SAAO,OAAO,MAAM,OAAO,cAAc,MAAM,KAAK;AACpD,QAAM,UAAU,CAAC,UAAU;AACzB,aAAS,QAAQ;AACjB,QAAI,QAAQ,gBAAgB,CAAC,QAAQ,aAAa,SAAS,MAAM,WAAW;AAC1E;AACF,UAAM,QAAQ,WAAW,OAAO,MAAM,KAAK;AAAA,EAC7C;AACA,MAAI,QAAQ;AACV,qBAAiB,QAAQ,eAAe,SAAS,EAAE,SAAS,KAAK,CAAC;AAClE,qBAAiB,QAAQ,eAAe,SAAS,EAAE,SAAS,KAAK,CAAC;AAClE,qBAAiB,QAAQ,gBAAgB,MAAM,SAAS,QAAQ,OAAO,EAAE,SAAS,KAAK,CAAC;AAAA,EAC1F;AACA,SAAOM,iBAAgBD,kBAAiB,CAAC,GAAG3C,QAAO,KAAK,CAAC,GAAG;AAAA,IAC1D;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eAAe,QAAQ,UAAU,CAAC,GAAG;AAC5C,QAAM,EAAE,UAAAd,YAAW,iBAAiB,mBAAmB,IAAI;AAC3D,QAAM,cAAc,aAAa,MAAMA,aAAY,wBAAwBA,SAAQ;AACnF,QAAM,UAAU,IAAI;AACpB,QAAM,iBAAiB,IAAI;AAC3B,MAAI;AACJ,MAAI,YAAY,OAAO;AACrB,qBAAiBA,WAAU,qBAAqB,MAAM;AACpD,UAAIJ;AACJ,YAAM,kBAAkBA,MAAKI,UAAS,uBAAuB,OAAOJ,MAAK,QAAQ;AACjF,UAAI,iBAAiB,mBAAmB,eAAe;AACrD,gBAAQ,QAAQI,UAAS;AACzB,YAAI,CAAC,QAAQ;AACX,0BAAgB,eAAe,QAAQ;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,qBAAiBA,WAAU,oBAAoB,MAAM;AACnD,UAAIJ;AACJ,YAAM,kBAAkBA,MAAKI,UAAS,uBAAuB,OAAOJ,MAAK,QAAQ;AACjF,UAAI,iBAAiB,mBAAmB,eAAe;AACrD,cAAM,SAASI,UAAS,qBAAqB,YAAY;AACzD,cAAM,IAAI,MAAM,aAAa,MAAM,gBAAgB;AAAA,MACrD;AAAA,IACF,CAAC;AAAA,EACH;AACA,iBAAe,KAAK,GAAG,UAAU;AAC/B,QAAIJ;AACJ,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,MAAM,oDAAoD;AACtE,mBAAe,QAAQ,aAAa,QAAQ,EAAE,gBAAgB;AAC9D,oBAAgB,aAAa,SAASA,MAAK,aAAa,MAAM,MAAM,OAAOA,MAAK,eAAe,QAAQ,aAAa,CAAC;AACrH,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,2BAA2B;AAC7C,kBAAc,mBAAmB,YAAY,OAAO,WAAW,kBAAkB;AACjF,WAAO,MAAM,MAAM,OAAO,EAAE,KAAK,aAAa;AAAA,EAChD;AACA,iBAAe,SAAS;AACtB,QAAI,CAAC,QAAQ;AACX,aAAO;AACT,IAAAI,UAAS,gBAAgB;AACzB,UAAM,MAAM,OAAO,EAAE,SAAS;AAC9B,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,kBAAgB,IAAI,IAAI;AACxB,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,MAAM,IAAI;AAC1B,kBAAgB,MAAM,IAAI;AAC1B,kBAAgB,MAAM,IAAI;AAC5B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,SAAS,SAAS,QAAQ,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,QAAAF,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,cAAc,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3C,QAAM,YAAY,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACzC,QAAM,QAAQ,SAAS,MAAM,YAAY,IAAI,UAAU,CAAC;AACxD,QAAM,QAAQ,SAAS,MAAM,YAAY,IAAI,UAAU,CAAC;AACxD,QAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAM,sBAAsB,SAAS,MAAM,IAAI,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,KAAK,SAAS;AAC/F,QAAM,YAAY,IAAI,KAAK;AAC3B,QAAM,YAAY,SAAS,MAAM;AAC/B,QAAI,CAAC,oBAAoB;AACvB,aAAO,eAAe;AACxB,QAAI,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG;AACvC,aAAO,MAAM,QAAQ,IAAI,eAAe,OAAO,eAAe;AAAA,IAChE,OAAO;AACL,aAAO,MAAM,QAAQ,IAAI,eAAe,KAAK,eAAe;AAAA,IAC9D;AAAA,EACF,CAAC;AACD,QAAM,sBAAsB,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,OAAO;AAC9E,QAAM,oBAAoB,CAAC,GAAG,MAAM;AAClC,gBAAY,IAAI;AAChB,gBAAY,IAAI;AAAA,EAClB;AACA,QAAM,kBAAkB,CAAC,GAAG,MAAM;AAChC,cAAU,IAAI;AACd,cAAU,IAAI;AAAA,EAChB;AACA,MAAI;AACJ,QAAM,0BAA0B,yBAAyBA,WAAU,OAAO,SAASA,QAAO,QAAQ;AAClG,MAAI,CAAC;AACH,sBAAkB,0BAA0B,EAAE,SAAS,OAAO,SAAS,KAAK,IAAI,EAAE,SAAS,KAAK;AAAA;AAEhG,sBAAkB,0BAA0B,EAAE,SAAS,KAAK,IAAI,EAAE,SAAS,MAAM;AACnF,QAAM,aAAa,CAAC,MAAM;AACxB,QAAI,UAAU;AACZ,oBAAc,OAAO,SAAS,WAAW,GAAG,UAAU,KAAK;AAC7D,cAAU,QAAQ;AAAA,EACpB;AACA,QAAM,QAAQ;AAAA,IACZ,iBAAiB,QAAQ,cAAc,CAAC,MAAM;AAC5C,UAAI,gBAAgB,WAAW,CAAC,gBAAgB;AAC9C,UAAE,eAAe;AACnB,YAAM,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC;AACpC,wBAAkB,GAAG,CAAC;AACtB,sBAAgB,GAAG,CAAC;AACpB,sBAAgB,OAAO,SAAS,aAAa,CAAC;AAAA,IAChD,GAAG,eAAe;AAAA,IAClB,iBAAiB,QAAQ,aAAa,CAAC,MAAM;AAC3C,YAAM,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC;AACpC,sBAAgB,GAAG,CAAC;AACpB,UAAI,CAAC,UAAU,SAAS,oBAAoB;AAC1C,kBAAU,QAAQ;AACpB,UAAI,UAAU;AACZ,mBAAW,OAAO,SAAS,QAAQ,CAAC;AAAA,IACxC,GAAG,eAAe;AAAA,IAClB,iBAAiB,QAAQ,YAAY,YAAY,eAAe;AAAA,IAChE,iBAAiB,QAAQ,eAAe,YAAY,eAAe;AAAA,EACrE;AACA,QAAM,OAAO,MAAM,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC3C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,yBAAyBE,WAAU;AAC1C,MAAI,CAACA;AACH,WAAO;AACT,MAAI,kBAAkB;AACtB,QAAM,eAAe;AAAA,IACnB,IAAI,UAAU;AACZ,wBAAkB;AAClB,aAAO;AAAA,IACT;AAAA,EACF;AACA,EAAAA,UAAS,iBAAiB,KAAK,MAAM,YAAY;AACjD,EAAAA,UAAS,oBAAoB,KAAK,IAAI;AACtC,SAAO;AACT;AAEA,SAAS,gBAAgB,QAAQ,UAAU,CAAC,GAAG;AAC7C,QAAM,YAAY,WAAW,MAAM;AACnC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACxC,QAAM,iBAAiB,CAAC,GAAG,MAAM;AAC/B,aAAS,IAAI;AACb,aAAS,IAAI;AAAA,EACf;AACA,QAAM,SAAS,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACtC,QAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,WAAO,IAAI;AACX,WAAO,IAAI;AAAA,EACb;AACA,QAAM,YAAY,SAAS,MAAM,SAAS,IAAI,OAAO,CAAC;AACtD,QAAM,YAAY,SAAS,MAAM,SAAS,IAAI,OAAO,CAAC;AACtD,QAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAM,sBAAsB,SAAS,MAAM,IAAI,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,CAAC,KAAK,SAAS;AACvG,QAAM,YAAY,IAAI,KAAK;AAC3B,QAAM,gBAAgB,IAAI,KAAK;AAC/B,QAAM,YAAY,SAAS,MAAM;AAC/B,QAAI,CAAC,oBAAoB;AACvB,aAAO,eAAe;AACxB,QAAI,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,GAAG;AAC/C,aAAO,UAAU,QAAQ,IAAI,eAAe,OAAO,eAAe;AAAA,IACpE,OAAO;AACL,aAAO,UAAU,QAAQ,IAAI,eAAe,KAAK,eAAe;AAAA,IAClE;AAAA,EACF,CAAC;AACD,QAAM,iBAAiB,CAAC,MAAM;AAC5B,QAAIJ,KAAI,IAAI;AACZ,UAAM,oBAAoB,EAAE,YAAY;AACxC,UAAM,kBAAkB,EAAE,YAAY;AACtC,YAAQ,MAAM,MAAMA,MAAK,QAAQ,iBAAiB,OAAO,SAASA,IAAG,SAAS,EAAE,WAAW,MAAM,OAAO,KAAK,qBAAqB,oBAAoB,OAAO,KAAK;AAAA,EACpK;AACA,QAAM,QAAQ;AAAA,IACZ,iBAAiB,QAAQ,eAAe,CAAC,MAAM;AAC7C,UAAIA,KAAI;AACR,UAAI,CAAC,eAAe,CAAC;AACnB;AACF,oBAAc,QAAQ;AACtB,OAAC,MAAMA,MAAK,UAAU,UAAU,OAAO,SAASA,IAAG,UAAU,OAAO,SAAS,GAAG,YAAY,gBAAgB,MAAM;AAClH,YAAM,cAAc,EAAE;AACtB,qBAAe,OAAO,SAAS,YAAY,kBAAkB,EAAE,SAAS;AACxE,YAAM,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI;AACnC,qBAAe,GAAG,CAAC;AACnB,mBAAa,GAAG,CAAC;AACjB,sBAAgB,OAAO,SAAS,aAAa,CAAC;AAAA,IAChD,CAAC;AAAA,IACD,iBAAiB,QAAQ,eAAe,CAAC,MAAM;AAC7C,UAAI,CAAC,eAAe,CAAC;AACnB;AACF,UAAI,CAAC,cAAc;AACjB;AACF,YAAM,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI;AACnC,mBAAa,GAAG,CAAC;AACjB,UAAI,CAAC,UAAU,SAAS,oBAAoB;AAC1C,kBAAU,QAAQ;AACpB,UAAI,UAAU;AACZ,mBAAW,OAAO,SAAS,QAAQ,CAAC;AAAA,IACxC,CAAC;AAAA,IACD,iBAAiB,QAAQ,aAAa,CAAC,MAAM;AAC3C,UAAIA,KAAI;AACR,UAAI,CAAC,eAAe,CAAC;AACnB;AACF,UAAI,UAAU;AACZ,sBAAc,OAAO,SAAS,WAAW,GAAG,UAAU,KAAK;AAC7D,oBAAc,QAAQ;AACtB,gBAAU,QAAQ;AAClB,OAAC,MAAMA,MAAK,UAAU,UAAU,OAAO,SAASA,IAAG,UAAU,OAAO,SAAS,GAAG,YAAY,gBAAgB,SAAS;AAAA,IACvH,CAAC;AAAA,EACH;AACA,QAAM,OAAO,MAAM,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC3C,SAAO;AAAA,IACL,WAAW,SAAS,SAAS;AAAA,IAC7B,WAAW,SAAS,SAAS;AAAA,IAC7B,UAAU,SAAS,QAAQ;AAAA,IAC3B,QAAQ,SAAS,MAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,SAAS;AACxC,QAAM,UAAU,cAAc,iCAAiC,OAAO;AACtE,QAAM,SAAS,cAAc,gCAAgC,OAAO;AACpE,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO;AACT,aAAO;AACT,QAAI,QAAQ;AACV,aAAO;AACT,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,qBAAqB,SAAS;AACrC,QAAM,SAAS,cAAc,4BAA4B,OAAO;AAChE,QAAM,SAAS,cAAc,4BAA4B,OAAO;AAChE,QAAM,WAAW,cAAc,8BAA8B,OAAO;AACpE,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO;AACT,aAAO;AACT,QAAI,OAAO;AACT,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,sBAAsB,UAAU,CAAC,GAAG;AAC3C,QAAM,EAAE,QAAAE,UAAS,cAAc,IAAI;AACnC,MAAI,CAACA;AACH,WAAO,IAAI,CAAC,IAAI,CAAC;AACnB,QAAM,YAAYA,QAAO;AACzB,QAAM,QAAQ,IAAI,UAAU,SAAS;AACrC,mBAAiBA,SAAQ,kBAAkB,MAAM;AAC/C,UAAM,QAAQ,UAAU;AAAA,EAC1B,CAAC;AACD,SAAO;AACT;AAEA,SAAS,0BAA0B,SAAS;AAC1C,QAAM,YAAY,cAAc,oCAAoC,OAAO;AAC3E,SAAO,SAAS,MAAM;AACpB,QAAI,UAAU;AACZ,aAAO;AACT,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,YAAY,OAAO,cAAc;AACxC,QAAM,WAAW,WAAW,YAAY;AACxC,QAAM,WAAW,KAAK,GAAG,CAAC,GAAG,aAAa;AACxC,aAAS,QAAQ;AAAA,EACnB,GAAG,EAAE,OAAO,OAAO,CAAC;AACpB,SAAO,SAAS,QAAQ;AAC1B;AAEA,IAAM,uBAAuB,CAAC,UAAU,CAAC,MAAM;AAC7C,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,YAAYA,WAAU,iBAAiBA,QAAO,MAAM;AACrG,QAAM,oBAAoB,YAAY,QAAQA,QAAO,OAAO,cAAc,CAAC;AAC3E,QAAM,cAAc,IAAI,kBAAkB,IAAI;AAC9C,QAAM,QAAQ,IAAI,kBAAkB,SAAS,CAAC;AAC9C,MAAI,YAAY,OAAO;AACrB,qBAAiBA,SAAQ,qBAAqB,MAAM;AAClD,kBAAY,QAAQ,kBAAkB;AACtC,YAAM,QAAQ,kBAAkB;AAAA,IAClC,CAAC;AAAA,EACH;AACA,QAAM,kBAAkB,CAAC,SAAS;AAChC,QAAI,CAAC,YAAY;AACf,aAAO,QAAQ,OAAO,IAAI,MAAM,eAAe,CAAC;AAClD,WAAO,kBAAkB,KAAK,IAAI;AAAA,EACpC;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,YAAY;AACd,wBAAkB,OAAO;AAAA,EAC7B;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,SAAS,oBAAoB;AAC3B,QAAM,MAAM,IAAI,EAAE;AAClB,QAAM,QAAQ,IAAI,EAAE;AACpB,QAAM,SAAS,IAAI,EAAE;AACrB,QAAM,OAAO,IAAI,EAAE;AACnB,MAAI,UAAU;AACZ,UAAM,YAAY,UAAU,UAAU;AACtC,UAAM,cAAc,UAAU,YAAY;AAC1C,UAAM,eAAe,UAAU,aAAa;AAC5C,UAAM,aAAa,UAAU,WAAW;AACxC,cAAU,QAAQ;AAClB,gBAAY,QAAQ;AACpB,iBAAa,QAAQ;AACrB,eAAW,QAAQ;AACnB,WAAO;AACP,qBAAiB,UAAU,cAAc,MAAM,CAAC;AAAA,EAClD;AACA,WAAS,SAAS;AAChB,QAAI,QAAQ,SAAS,UAAU;AAC/B,UAAM,QAAQ,SAAS,YAAY;AACnC,WAAO,QAAQ,SAAS,aAAa;AACrC,SAAK,QAAQ,SAAS,WAAW;AAAA,EACnC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,QAAQ;AAC7E;AAEA,SAAS,aAAa,KAAK,WAAW,MAAM,UAAU,CAAC,GAAG;AACxD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAAE,YAAW;AAAA,IACX,QAAQ,CAAC;AAAA,EACX,IAAI;AACJ,QAAM,YAAY,IAAI,IAAI;AAC1B,MAAI,WAAW;AACf,QAAM,aAAa,CAAC,sBAAsB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACzE,UAAM,qBAAqB,CAAC,QAAQ;AAClC,gBAAU,QAAQ;AAClB,cAAQ,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,CAACA,WAAU;AACb,cAAQ,KAAK;AACb;AAAA,IACF;AACA,QAAI,eAAe;AACnB,QAAI,KAAKA,UAAS,cAAc,eAAe,aAAa,GAAG,CAAC,IAAI;AACpE,QAAI,CAAC,IAAI;AACP,WAAKA,UAAS,cAAc,QAAQ;AACpC,SAAG,OAAO;AACV,SAAG,QAAQ;AACX,SAAG,MAAM,aAAa,GAAG;AACzB,UAAI;AACF,WAAG,QAAQ;AACb,UAAI;AACF,WAAG,cAAc;AACnB,UAAI;AACF,WAAG,WAAW;AAChB,UAAI;AACF,WAAG,iBAAiB;AACtB,aAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM,MAAM,OAAO,SAAS,GAAG,aAAa,MAAM,KAAK,CAAC;AACnG,qBAAe;AAAA,IACjB,WAAW,GAAG,aAAa,aAAa,GAAG;AACzC,yBAAmB,EAAE;AAAA,IACvB;AACA,OAAG,iBAAiB,SAAS,CAAC,UAAU,OAAO,KAAK,CAAC;AACrD,OAAG,iBAAiB,SAAS,CAAC,UAAU,OAAO,KAAK,CAAC;AACrD,OAAG,iBAAiB,QAAQ,MAAM;AAChC,SAAG,aAAa,eAAe,MAAM;AACrC,eAAS,EAAE;AACX,yBAAmB,EAAE;AAAA,IACvB,CAAC;AACD,QAAI;AACF,WAAKA,UAAS,KAAK,YAAY,EAAE;AACnC,QAAI,CAAC;AACH,yBAAmB,EAAE;AAAA,EACzB,CAAC;AACD,QAAM,OAAO,CAAC,oBAAoB,SAAS;AACzC,QAAI,CAAC;AACH,iBAAW,WAAW,iBAAiB;AACzC,WAAO;AAAA,EACT;AACA,QAAM,SAAS,MAAM;AACnB,QAAI,CAACA;AACH;AACF,eAAW;AACX,QAAI,UAAU;AACZ,gBAAU,QAAQ;AACpB,UAAM,KAAKA,UAAS,cAAc,eAAe,aAAa,GAAG,CAAC,IAAI;AACtE,QAAI;AACF,MAAAA,UAAS,KAAK,YAAY,EAAE;AAAA,EAChC;AACA,MAAI,aAAa,CAAC;AAChB,iBAAa,IAAI;AACnB,MAAI,CAAC;AACH,mBAAe,MAAM;AACvB,SAAO,EAAE,WAAW,MAAM,OAAO;AACnC;AAEA,SAAS,oBAAoB,KAAK;AAChC,QAAM,QAAQ,OAAO,iBAAiB,GAAG;AACzC,MAAI,MAAM,cAAc,YAAY,MAAM,cAAc,YAAY,MAAM,cAAc,UAAU,IAAI,eAAe,IAAI,gBAAgB,MAAM,cAAc,UAAU,IAAI,cAAc,IAAI,aAAa;AACxM,WAAO;AAAA,EACT,OAAO;AACL,UAAM,SAAS,IAAI;AACnB,QAAI,CAAC,UAAU,OAAO,YAAY;AAChC,aAAO;AACT,WAAO,oBAAoB,MAAM;AAAA,EACnC;AACF;AACA,SAAS,eAAe,UAAU;AAChC,QAAM,IAAI,YAAY,OAAO;AAC7B,QAAM,UAAU,EAAE;AAClB,MAAI,oBAAoB,OAAO;AAC7B,WAAO;AACT,MAAI,EAAE,QAAQ,SAAS;AACrB,WAAO;AACT,MAAI,EAAE;AACJ,MAAE,eAAe;AACnB,SAAO;AACT;AACA,SAAS,cAAc,SAAS,eAAe,OAAO;AACpD,QAAM,WAAW,IAAI,YAAY;AACjC,MAAI,wBAAwB;AAC5B,MAAI;AACJ,QAAM,WAAW,OAAO,GAAG,CAAC,OAAO;AACjC,QAAI,IAAI;AACN,YAAM,MAAM;AACZ,wBAAkB,IAAI,MAAM;AAC5B,UAAI,SAAS;AACX,YAAI,MAAM,WAAW;AAAA,IACzB;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,EACb,CAAC;AACD,QAAM,OAAO,MAAM;AACjB,UAAM,MAAM,aAAa,OAAO;AAChC,QAAI,CAAC,OAAO,SAAS;AACnB;AACF,QAAI,OAAO;AACT,8BAAwB,iBAAiB,KAAK,aAAa,CAAC,MAAM;AAChE,uBAAe,CAAC;AAAA,MAClB,GAAG,EAAE,SAAS,MAAM,CAAC;AAAA,IACvB;AACA,QAAI,MAAM,WAAW;AACrB,aAAS,QAAQ;AAAA,EACnB;AACA,QAAM,SAAS,MAAM;AACnB,UAAM,MAAM,aAAa,OAAO;AAChC,QAAI,CAAC,OAAO,CAAC,SAAS;AACpB;AACF,cAAU,yBAAyB,OAAO,SAAS,sBAAsB;AACzE,QAAI,MAAM,WAAW;AACrB,aAAS,QAAQ;AAAA,EACnB;AACA,oBAAkB,MAAM;AACxB,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,IAAI,GAAG;AACL,UAAI;AACF,aAAK;AAAA;AAEL,eAAO;AAAA,IACX;AAAA,EACF,CAAC;AACH;AAEA,SAAS,kBAAkB,KAAK,cAAc,UAAU,CAAC,GAAG;AAC1D,QAAM,EAAE,QAAAF,UAAS,cAAc,IAAI;AACnC,SAAO,WAAW,KAAK,cAAcA,WAAU,OAAO,SAASA,QAAO,gBAAgB,OAAO;AAC/F;AAEA,IAAI6D,eAAc,OAAO;AACzB,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMJ,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIK,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,SAAS,eAAe,CAAC,GAAG,UAAU,CAAC,GAAG;AACjD,QAAM,EAAE,YAAY,iBAAiB,IAAI;AACzC,QAAM,aAAa;AACnB,QAAM,cAAc,aAAa,MAAM,cAAc,cAAc,UAAU;AAC7E,QAAM,QAAQ,OAAO,kBAAkB,CAAC,MAAM;AAC5C,QAAI,YAAY,OAAO;AACrB,YAAM,OAAOC,kBAAiBA,kBAAiB,CAAC,GAAG,aAAa,YAAY,CAAC,GAAG,aAAa,eAAe,CAAC;AAC7G,UAAI,UAAU;AACd,UAAI,KAAK,SAAS,WAAW;AAC3B,kBAAU,WAAW,SAAS,EAAE,OAAO,KAAK,MAAM,CAAC;AACrD,UAAI;AACF,eAAO,WAAW,MAAM,IAAI;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,gBAAgB,CAAC,QAAQ,cAAc,OAAO,KAAK,SAAS;AAClE,IAAM,iBAAiB,CAAC,GAAG,MAAM,IAAI;AACrC,SAAS,aAAa,MAAM;AAC1B,MAAIpE,KAAI,IAAI,IAAI;AAChB,QAAM,CAAC,MAAM,IAAI;AACjB,MAAI,YAAY;AAChB,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,WAAW,GAAG;AACrB,QAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,gBAAU,KAAK,CAAC;AAChB,mBAAaA,MAAK,QAAQ,cAAc,OAAOA,MAAK;AAAA,IACtD,OAAO;AACL,mBAAa,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK;AAAA,IAC5C;AAAA,EACF,WAAW,KAAK,SAAS,GAAG;AAC1B,iBAAa,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK;AAC1C,eAAW,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAAA,EAC3C;AACA,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,IAAI;AACJ,MAAI,CAAC;AACH,WAAO,SAAS,MAAM,OAAO,CAAC,GAAG,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC;AAC7D,cAAY,MAAM;AAChB,UAAM,SAAS,OAAO,MAAM,MAAM,GAAG,SAAS;AAC9C,QAAI,MAAM,MAAM;AACd,aAAO,QAAQ;AAAA;AAEf,aAAO,OAAO,GAAG,OAAO,QAAQ,GAAG,MAAM;AAAA,EAC7C,CAAC;AACD,SAAO;AACT;AAEA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,QAAAE,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,OAAO,WAAW,QAAQ,QAAQ,OAAO;AAC/C,QAAM,cAAc,IAAI,KAAK;AAC7B,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,SAAS,IAAI,EAAE;AACrB,QAAM,QAAQ,WAAW,MAAM;AAC/B,QAAM,SAAS,CAAC,QAAQ,CAAC,YAAY,UAAU;AAC7C,gBAAY,QAAQ;AAAA,EACtB;AACA,QAAM,QAAQ,MAAM;AAClB,gBAAY,QAAQ;AAAA,EACtB;AACA,QAAM,OAAO,MAAM;AACjB,gBAAY,QAAQ;AAAA,EACtB;AACA,QAAM,oBAAoBA,YAAWA,QAAO,qBAAqBA,QAAO;AACxE,QAAM,cAAc,aAAa,MAAM,iBAAiB;AACxD,MAAI;AACJ,MAAI,YAAY,OAAO;AACrB,kBAAc,IAAI,kBAAkB;AACpC,gBAAY,aAAa;AACzB,gBAAY,iBAAiB;AAC7B,gBAAY,OAAO,MAAM,IAAI;AAC7B,gBAAY,UAAU,MAAM;AAC1B,cAAQ,QAAQ;AAAA,IAClB;AACA,UAAM,MAAM,CAAC,UAAU;AACrB,UAAI,eAAe,CAAC,YAAY;AAC9B,oBAAY,OAAO;AAAA,IACvB,CAAC;AACD,gBAAY,WAAW,CAAC,UAAU;AAChC,YAAM,aAAa,MAAM,KAAK,MAAM,OAAO,EAAE,IAAI,CAAC,YAAY;AAC5D,gBAAQ,QAAQ,QAAQ;AACxB,eAAO,QAAQ,CAAC;AAAA,MAClB,CAAC,EAAE,IAAI,CAAC,YAAY,QAAQ,UAAU,EAAE,KAAK,EAAE;AAC/C,aAAO,QAAQ;AACf,YAAM,QAAQ;AAAA,IAChB;AACA,gBAAY,UAAU,CAAC,UAAU;AAC/B,YAAM,QAAQ;AAAA,IAChB;AACA,gBAAY,QAAQ,MAAM;AACxB,kBAAY,QAAQ;AACpB,kBAAY,OAAO,MAAM,IAAI;AAAA,IAC/B;AACA,UAAM,aAAa,MAAM;AACvB,UAAI,YAAY;AACd,oBAAY,MAAM;AAAA;AAElB,oBAAY,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AACA,oBAAkB,MAAM;AACtB,gBAAY,QAAQ;AAAA,EACtB,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,MAAM,UAAU,CAAC,GAAG;AAC9C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,QAAQA,WAAUA,QAAO;AAC/B,QAAM,cAAc,aAAa,MAAM,KAAK;AAC5C,QAAM,YAAY,IAAI,KAAK;AAC3B,QAAM,SAAS,IAAI,MAAM;AACzB,QAAM,aAAa,WAAW,QAAQ,EAAE;AACxC,QAAM,OAAO,WAAW,QAAQ,QAAQ,OAAO;AAC/C,QAAM,QAAQ,WAAW,MAAM;AAC/B,QAAM,SAAS,CAAC,QAAQ,CAAC,UAAU,UAAU;AAC3C,cAAU,QAAQ;AAAA,EACpB;AACA,QAAM,yBAAyB,CAAC,eAAe;AAC7C,eAAW,OAAO,MAAM,IAAI;AAC5B,eAAW,QAAQ,MAAM,QAAQ,KAAK,KAAK;AAC3C,eAAW,QAAQ;AACnB,eAAW,OAAO;AAClB,eAAW,SAAS;AACpB,eAAW,UAAU,MAAM;AACzB,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACjB;AACA,eAAW,UAAU,MAAM;AACzB,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACjB;AACA,eAAW,WAAW,MAAM;AAC1B,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACjB;AACA,eAAW,QAAQ,MAAM;AACvB,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACjB;AACA,eAAW,UAAU,CAAC,UAAU;AAC9B,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AACA,QAAM,YAAY,SAAS,MAAM;AAC/B,cAAU,QAAQ;AAClB,WAAO,QAAQ;AACf,UAAM,eAAe,IAAI,yBAAyB,WAAW,KAAK;AAClE,2BAAuB,YAAY;AACnC,WAAO;AAAA,EACT,CAAC;AACD,QAAM,QAAQ,MAAM;AAClB,UAAM,OAAO;AACb,iBAAa,MAAM,MAAM,UAAU,KAAK;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM;AACjB,UAAM,OAAO;AACb,cAAU,QAAQ;AAAA,EACpB;AACA,MAAI,YAAY,OAAO;AACrB,2BAAuB,UAAU,KAAK;AACtC,UAAM,MAAM,CAAC,UAAU;AACrB,UAAI,UAAU,SAAS,CAAC,UAAU;AAChC,kBAAU,MAAM,OAAO;AAAA,IAC3B,CAAC;AACD,QAAI,QAAQ,OAAO;AACjB,YAAM,QAAQ,OAAO,MAAM;AACzB,cAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AACA,UAAM,WAAW,MAAM;AACrB,UAAI,UAAU;AACZ,cAAM,OAAO;AAAA;AAEb,cAAM,MAAM;AAAA,IAChB,CAAC;AAAA,EACH;AACA,oBAAkB,MAAM;AACtB,cAAU,QAAQ;AAAA,EACpB,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,OAAO,aAAa;AACtC,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,YAAY,SAAS,MAAM,MAAM,QAAQ,SAAS,KAAK,IAAI,SAAS,QAAQ,OAAO,KAAK,SAAS,KAAK,CAAC;AAC7G,QAAM,QAAQ,IAAI,UAAU,MAAM,QAAQ,eAAe,OAAO,cAAc,UAAU,MAAM,CAAC,CAAC,CAAC;AACjG,QAAM,UAAU,SAAS,MAAM,GAAG,MAAM,KAAK,CAAC;AAC9C,QAAM,UAAU,SAAS,MAAM,MAAM,UAAU,CAAC;AAChD,QAAM,SAAS,SAAS,MAAM,MAAM,UAAU,UAAU,MAAM,SAAS,CAAC;AACxE,QAAM,OAAO,SAAS,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC,CAAC;AAC5D,QAAM,WAAW,SAAS,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC,CAAC;AAChE,WAAS,GAAG,QAAQ;AAClB,QAAI,MAAM,QAAQ,SAAS,KAAK;AAC9B,aAAO,SAAS,MAAM,MAAM;AAC9B,WAAO,SAAS,MAAM,UAAU,MAAM,MAAM,CAAC;AAAA,EAC/C;AACA,WAASmE,KAAI,MAAM;AACjB,QAAI,CAAC,UAAU,MAAM,SAAS,IAAI;AAChC;AACF,WAAO,GAAG,UAAU,MAAM,QAAQ,IAAI,CAAC;AAAA,EACzC;AACA,WAAS,KAAK,MAAM;AAClB,QAAI,UAAU,MAAM,SAAS,IAAI;AAC/B,YAAM,QAAQ,UAAU,MAAM,QAAQ,IAAI;AAAA,EAC9C;AACA,WAAS,WAAW;AAClB,QAAI,OAAO;AACT;AACF,UAAM;AAAA,EACR;AACA,WAAS,eAAe;AACtB,QAAI,QAAQ;AACV;AACF,UAAM;AAAA,EACR;AACA,WAAS,SAAS,MAAM;AACtB,QAAI,QAAQ,IAAI;AACd,WAAK,IAAI;AAAA,EACb;AACA,WAAS,OAAO,MAAM;AACpB,WAAO,UAAU,MAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ;AAAA,EACzD;AACA,WAAS,WAAW,MAAM;AACxB,WAAO,UAAU,MAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ;AAAA,EACzD;AACA,WAAS,UAAU,MAAM;AACvB,WAAO,UAAU,MAAM,QAAQ,IAAI,MAAM,MAAM;AAAA,EACjD;AACA,WAAS,SAAS,MAAM;AACtB,WAAO,MAAM,QAAQ,UAAU,MAAM,QAAQ,IAAI;AAAA,EACnD;AACA,WAAS,QAAQ,MAAM;AACrB,WAAO,MAAM,QAAQ,UAAU,MAAM,QAAQ,IAAI;AAAA,EACnD;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAIC,eAAc,OAAO;AACzB,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMJ,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIK,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,cAAc,SAAS,UAAU,CAAC,GAAG;AACjE,MAAI1E;AACJ,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,yBAAyB;AAAA,IACzB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB;AAAA,IACA,QAAAE,UAAS;AAAA,IACT;AAAA,IACA,UAAU,CAAC,MAAM;AACf,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa,YAAY;AACzC,QAAM,OAAO,oBAAoB,OAAO;AACxC,QAAM,QAAQ,UAAU,aAAa,KAAK,YAAY;AACtD,QAAM,cAAcF,MAAK,QAAQ,eAAe,OAAOA,MAAK,mBAAmB,IAAI;AACnF,MAAI,CAAC,SAAS;AACZ,QAAI;AACF,gBAAU,cAAc,qBAAqB,MAAM;AACjD,YAAIG;AACJ,gBAAQA,OAAM,kBAAkB,OAAO,SAASA,KAAI;AAAA,MACtD,CAAC,EAAE;AAAA,IACL,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,iBAAe,KAAK,OAAO;AACzB,QAAI,CAAC,WAAW,SAAS,MAAM,QAAQ;AACrC;AACF,QAAI;AACF,YAAM,WAAW,QAAQ,MAAM,WAAW,MAAM,QAAQ,QAAQ,GAAG;AACnE,UAAI,YAAY,MAAM;AACpB,aAAK,QAAQ;AACb,YAAI,iBAAiB,YAAY;AAC/B,gBAAM,QAAQ,QAAQ,KAAK,MAAM,WAAW,MAAM,OAAO,CAAC;AAAA,MAC9D,WAAW,eAAe;AACxB,cAAM,QAAQ,MAAM,WAAW,KAAK,QAAQ;AAC5C,YAAI,WAAW,aAAa;AAC1B,eAAK,QAAQ,cAAc,OAAO,OAAO;AAAA,iBAClC,SAAS,YAAY,CAAC,MAAM,QAAQ,KAAK;AAChD,eAAK,QAAQwE,kBAAiBA,kBAAiB,CAAC,GAAG,OAAO,GAAG,KAAK;AAAA;AAElE,eAAK,QAAQ;AAAA,MACjB,OAAO;AACL,aAAK,QAAQ,MAAM,WAAW,KAAK,QAAQ;AAAA,MAC7C;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,OAAK;AACL,MAAIzE,WAAU;AACZ,qBAAiBA,SAAQ,WAAW,CAAC,MAAM,WAAW,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;AACzE,MAAI,SAAS;AACX,oBAAgB,MAAM,YAAY;AAChC,UAAI;AACF,YAAI,KAAK,SAAS;AAChB,gBAAM,QAAQ,WAAW,GAAG;AAAA;AAE5B,gBAAM,QAAQ,QAAQ,KAAK,MAAM,WAAW,MAAM,KAAK,KAAK,CAAC;AAAA,MACjE,SAAS,GAAG;AACV,gBAAQ,CAAC;AAAA,MACX;AAAA,IACF,GAAG;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,IAAI,MAAM;AACV,SAAS,YAAY,KAAK,UAAU,CAAC,GAAG;AACtC,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM;AAAA,IACJ,UAAAE,YAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,KAAK,mBAAmB,EAAE,GAAG;AAAA,EAC/B,IAAI;AACJ,QAAM,SAAS,IAAI,GAAG;AACtB,MAAI,OAAO,MAAM;AAAA,EACjB;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAACA;AACH;AACF,UAAM,KAAKA,UAAS,eAAe,EAAE,KAAKA,UAAS,cAAc,OAAO;AACxE,QAAI,CAAC,GAAG,aAAa;AACnB,SAAG,OAAO;AACV,SAAG,KAAK;AACR,UAAI,QAAQ;AACV,WAAG,QAAQ,QAAQ;AACrB,MAAAA,UAAS,KAAK,YAAY,EAAE;AAAA,IAC9B;AACA,QAAI,SAAS;AACX;AACF,WAAO,MAAM,QAAQ,CAAC,UAAU;AAC9B,SAAG,cAAc;AAAA,IACnB,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,aAAS,QAAQ;AAAA,EACnB;AACA,QAAM,SAAS,MAAM;AACnB,QAAI,CAACA,aAAY,CAAC,SAAS;AACzB;AACF,SAAK;AACL,IAAAA,UAAS,KAAK,YAAYA,UAAS,eAAe,EAAE,CAAC;AACrD,aAAS,QAAQ;AAAA,EACnB;AACA,MAAI,aAAa,CAAC;AAChB,iBAAa,IAAI;AACnB,MAAI,CAAC;AACH,sBAAkB,MAAM;AAC1B,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA,UAAU,SAAS,QAAQ;AAAA,EAC7B;AACF;AAEA,SAAS,sBAAsB;AAC7B,QAAM,OAAO,IAAI,CAAC,CAAC;AACnB,OAAK,MAAM,MAAM,CAAC,OAAO;AACvB,QAAI;AACF,WAAK,MAAM,KAAK,EAAE;AAAA,EACtB;AACA,iBAAe,MAAM;AACnB,SAAK,MAAM,SAAS;AAAA,EACtB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,UAAAA,YAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,EACjB,IAAI;AACJ,WAASC,YAAW;AAClB,QAAIL,KAAI;AACR,YAAQ,MAAMA,MAAKI,aAAY,OAAO,SAASA,UAAS,cAAc,QAAQ,MAAM,OAAO,SAASJ,IAAG,aAAa,KAAK,MAAM,OAAO,KAAK;AAAA,EAC7I;AACA,QAAM,MAAM,IAAIK,UAAS,CAAC;AAC1B,eAAa,MAAM,IAAI,QAAQA,UAAS,CAAC;AACzC,MAAI,WAAWD,WAAU;AACvB,wBAAoBA,UAAS,cAAc,QAAQ,GAAG,MAAM,IAAI,QAAQC,UAAS,GAAG,EAAE,YAAY,KAAK,CAAC;AAAA,EAC1G;AACA,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,aAAO,IAAI;AAAA,IACb;AAAA,IACA,IAAI,GAAG;AACL,UAAIL,KAAI;AACR,UAAI,QAAQ;AACZ,UAAI,CAACI;AACH;AACF,UAAI,IAAI;AACN,SAACJ,MAAKI,UAAS,cAAc,QAAQ,MAAM,OAAO,SAASJ,IAAG,aAAa,OAAO,IAAI,KAAK;AAAA;AAE3F,SAAC,KAAKI,UAAS,cAAc,QAAQ,MAAM,OAAO,SAAS,GAAG,gBAAgB,KAAK;AAAA,IACvF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,uBAAuB,WAAW;AACzC,MAAIJ;AACJ,QAAM,cAAcA,MAAK,UAAU,eAAe,OAAOA,MAAK;AAC9D,QAAM,SAAS,IAAI,MAAM,UAAU;AACnC,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,UAAM,QAAQ,UAAU,WAAW,CAAC;AACpC,WAAO,CAAC,IAAI;AAAA,EACd;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,QAAAE,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,YAAY,IAAI,IAAI;AAC1B,QAAM,OAAO,SAAS,MAAM;AAC1B,QAAIF,KAAI;AACR,YAAQ,MAAMA,MAAK,UAAU,UAAU,OAAO,SAASA,IAAG,SAAS,MAAM,OAAO,KAAK;AAAA,EACvF,CAAC;AACD,QAAM,SAAS,SAAS,MAAM,UAAU,QAAQ,uBAAuB,UAAU,KAAK,IAAI,CAAC,CAAC;AAC5F,QAAM,QAAQ,SAAS,MAAM,OAAO,MAAM,IAAI,CAAC,UAAU,MAAM,sBAAsB,CAAC,CAAC;AACvF,WAAS,oBAAoB;AAC3B,cAAU,QAAQ;AAClB,QAAIE;AACF,gBAAU,QAAQA,QAAO,aAAa;AAAA,EAC1C;AACA,MAAIA;AACF,qBAAiBA,QAAO,UAAU,mBAAmB,iBAAiB;AACxE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,SAAS;AACpC,QAAM,WAAW,IAAI,WAAW,OAAO,SAAS,QAAQ,OAAO;AAC/D,QAAM,QAAQ,IAAI,WAAW,OAAO,SAAS,QAAQ,KAAK;AAC1D,WAAS,gBAAgB;AACvB,QAAIF,KAAI;AACR,QAAI,CAAC,SAAS;AACZ;AACF,aAAS,MAAM,MAAM,SAAS;AAC9B,aAAS,MAAM,MAAM,SAAS,IAAIA,MAAK,SAAS,UAAU,OAAO,SAASA,IAAG,YAAY;AACzF,KAAC,KAAK,WAAW,OAAO,SAAS,QAAQ,aAAa,OAAO,SAAS,GAAG,KAAK,OAAO;AAAA,EACvF;AACA,QAAM,CAAC,OAAO,QAAQ,GAAG,eAAe,EAAE,WAAW,KAAK,CAAC;AAC3D,oBAAkB,UAAU,MAAM,cAAc,CAAC;AACjD,MAAI,WAAW,OAAO,SAAS,QAAQ;AACrC,UAAM,QAAQ,OAAO,eAAe,EAAE,WAAW,MAAM,MAAM,KAAK,CAAC;AACrE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI4E,eAAc,OAAO;AACzB,IAAIC,cAAa,OAAO;AACxB,IAAIC,qBAAoB,OAAO;AAC/B,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMN,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIO,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAIE,iBAAgB,CAAC,GAAG,MAAMP,YAAW,GAAGC,mBAAkB,CAAC,CAAC;AAChE,SAAS,uBAAuB,QAAQ,UAAU,CAAC,GAAG;AACpD,QAAM,EAAE,WAAW,KAAK,WAAW,KAAK,IAAI;AAC5C,QAAM,SAAS,eAAe,UAAU,QAAQ;AAChD,QAAM,UAAU,cAAc,QAAQM,eAAcD,kBAAiB,CAAC,GAAG,OAAO,GAAG,EAAE,aAAa,OAAO,CAAC,CAAC;AAC3G,SAAOA,kBAAiB,CAAC,GAAG,OAAO;AACrC;AAEA,IAAIE,eAAc,OAAO;AACzB,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMJ,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIK,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAIE,aAAY,CAAC,QAAQ,YAAY;AACnC,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ;AACf,QAAIJ,gBAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAC/D,aAAO,IAAI,IAAI,OAAO,IAAI;AAC9B,MAAI,UAAU,QAAQD;AACpB,aAAS,QAAQA,uBAAsB,MAAM,GAAG;AAC9C,UAAI,QAAQ,QAAQ,IAAI,IAAI,KAAKE,gBAAe,KAAK,QAAQ,IAAI;AAC/D,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC9B;AACF,SAAO;AACT;AACA,IAAM,gBAAgB;AAAA,EACpB,EAAE,KAAK,KAAK,OAAO,KAAK,MAAM,SAAS;AAAA,EACvC,EAAE,KAAK,OAAO,OAAO,KAAK,MAAM,SAAS;AAAA,EACzC,EAAE,KAAK,MAAM,OAAO,MAAM,MAAM,OAAO;AAAA,EACvC,EAAE,KAAK,QAAQ,OAAO,OAAO,MAAM,MAAM;AAAA,EACzC,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,OAAO;AAAA,EAC5C,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,QAAQ;AAAA,EAC7C,EAAE,KAAK,UAAU,OAAO,SAAS,MAAM,OAAO;AAChD;AACA,IAAM,mBAAmB;AAAA,EACvB,SAAS;AAAA,EACT,MAAM,CAAC,MAAM,EAAE,MAAM,IAAI,IAAI,GAAG,CAAC,SAAS;AAAA,EAC1C,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK;AAAA,EAC3C,OAAO,CAAC,GAAG,SAAS,MAAM,IAAI,OAAO,eAAe,eAAe,GAAG,CAAC,SAAS,IAAI,IAAI,MAAM,EAAE;AAAA,EAChG,MAAM,CAAC,GAAG,SAAS,MAAM,IAAI,OAAO,cAAc,cAAc,GAAG,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;AAAA,EAC5F,KAAK,CAAC,GAAG,SAAS,MAAM,IAAI,OAAO,cAAc,aAAa,GAAG,CAAC,OAAO,IAAI,IAAI,MAAM,EAAE;AAAA,EACzF,MAAM,CAAC,GAAG,SAAS,MAAM,IAAI,OAAO,cAAc,cAAc,GAAG,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;AAAA,EAC5F,MAAM,CAAC,MAAM,GAAG,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;AAAA,EACzC,QAAQ,CAAC,MAAM,GAAG,CAAC,UAAU,IAAI,IAAI,MAAM,EAAE;AAAA,EAC7C,QAAQ,CAAC,MAAM,GAAG,CAAC,UAAU,IAAI,IAAI,MAAM,EAAE;AAAA,EAC7C,SAAS;AACX;AACA,IAAM,oBAAoB,CAAC,SAAS,KAAK,YAAY,EAAE,MAAM,GAAG,EAAE;AAClE,SAAS,WAAW,MAAM,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAMxF,MAAK,OAAO,EAAE,UAAU,gBAAgB,UAAU,KAAK,CAAC,GAAG,EAAE,KAAAwB,KAAI,IAAIxB,KAAI,WAAW2F,WAAU3F,KAAI,CAAC,KAAK,CAAC;AAC/G,QAAM,UAAU,SAAS,MAAM,cAAc,IAAI,KAAK,aAAa,IAAI,CAAC,GAAG,SAAS,MAAMwB,KAAI,KAAK,CAAC,CAAC;AACrG,MAAI,gBAAgB;AAClB,WAAOkE,kBAAiB;AAAA,MACtB;AAAA,IACF,GAAG,QAAQ;AAAA,EACb,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,MAAM,UAAU,CAAC,GAAGlE,OAAM,KAAK,IAAI,GAAG;AAC3D,MAAIxB;AACJ,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,EACb,IAAI;AACJ,QAAM,UAAU,OAAO,aAAa,WAAW,CAAC,MAAM,CAAC,EAAE,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAC1F,QAAM,OAAO,CAACwB,OAAM,CAAC;AACrB,QAAM,UAAU,KAAK,IAAI,IAAI;AAC7B,WAASnB,UAAS,OAAO,MAAM;AAC7B,WAAO,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,EAC7C;AACA,WAAS,OAAO,OAAO,MAAM;AAC3B,UAAM,MAAMA,UAAS,OAAO,IAAI;AAChC,UAAM,OAAO,QAAQ;AACrB,UAAM,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI;AAC5C,WAAO,YAAY,OAAO,SAAS,UAAU,KAAK,IAAI;AAAA,EACxD;AACA,WAAS,YAAY,MAAM,KAAK,QAAQ;AACtC,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,OAAO,cAAc;AACvB,aAAO,UAAU,KAAK,MAAM;AAC9B,WAAO,UAAU,QAAQ,OAAO,IAAI,SAAS,CAAC;AAAA,EAChD;AACA,MAAI,UAAU,OAAO,CAAC;AACpB,WAAO,SAAS;AAClB,MAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,WAAO,kBAAkB,IAAI,KAAK,IAAI,CAAC;AACzC,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,WAAWL,MAAK,MAAM,KAAK,CAAC,MAAM,EAAE,SAAS,GAAG,MAAM,OAAO,SAASA,IAAG;AAC/E,QAAI,WAAW,UAAU;AACvB,aAAO,kBAAkB,IAAI,KAAK,IAAI,CAAC;AAAA,EAC3C;AACA,aAAW,CAAC,KAAK,IAAI,KAAK,MAAM,QAAQ,GAAG;AACzC,UAAM,MAAMK,UAAS,MAAM,IAAI;AAC/B,QAAI,OAAO,KAAK,MAAM,MAAM,CAAC;AAC3B,aAAO,OAAO,MAAM,MAAM,MAAM,CAAC,CAAC;AACpC,QAAI,UAAU,KAAK;AACjB,aAAO,OAAO,MAAM,IAAI;AAAA,EAC5B;AACA,SAAO,SAAS;AAClB;AAEA,SAAS,eAAe,IAAI,UAAU,oBAAoB;AACxD,QAAM,EAAE,MAAM,IAAI,aAAa,MAAM,QAAQ;AAC7C,QAAM,WAAW,IAAI,KAAK;AAC1B,iBAAe,OAAO;AACpB,QAAI,CAAC,SAAS;AACZ;AACF,UAAM,GAAG;AACT,UAAM;AAAA,EACR;AACA,WAAS,SAAS;AAChB,QAAI,CAAC,SAAS,OAAO;AACnB,eAAS,QAAQ;AACjB,WAAK;AAAA,IACP;AAAA,EACF;AACA,WAAS,QAAQ;AACf,aAAS,QAAQ;AAAA,EACnB;AACA,MAAI,sBAAsB,OAAO,SAAS,mBAAmB;AAC3D,WAAO;AACT,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAIuF,eAAc,OAAO;AACzB,IAAIC,yBAAwB,OAAO;AACnC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,kBAAiB,OAAO,UAAU;AACtC,IAAIC,qBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMJ,aAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAIK,oBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,gBAAe,KAAK,GAAG,IAAI;AAC7B,MAAAE,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAIH;AACF,aAAS,QAAQA,uBAAsB,CAAC,GAAG;AACzC,UAAIE,gBAAe,KAAK,GAAG,IAAI;AAC7B,QAAAC,mBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,WAAW;AAAA,IACX;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,IAAI,UAAU,IAAI,MAAM;AACnC,QAAM,SAAS,MAAM,GAAG,QAAQ,UAAU,IAAI;AAC9C,QAAM,KAAK,WAAW,MAAM;AAC1B,WAAO;AACP,aAAS,GAAG,KAAK;AAAA,EACnB,IAAI;AACJ,QAAM,WAAW,aAAa,0BAA0B,SAAS,IAAI,EAAE,UAAU,CAAC,IAAI,cAAc,IAAI,UAAU,EAAE,UAAU,CAAC;AAC/H,MAAI,gBAAgB;AAClB,WAAOC,kBAAiB;AAAA,MACtB,WAAW;AAAA,IACb,GAAG,QAAQ;AAAA,EACb,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,SAAS,WAAW,MAAM,UAAU,CAAC,GAAG;AAC/C,MAAIjG,KAAI;AACR,QAAM;AAAA,IACJ,UAAAI,YAAW;AAAA,EACb,IAAI;AACJ,QAAM,QAAQ,YAAYJ,MAAK,YAAY,OAAO,WAAWI,aAAY,OAAO,SAASA,UAAS,UAAU,OAAOJ,MAAK,IAAI;AAC5H,QAAMkG,cAAa,YAAY,WAAW,QAAQ;AAClD,WAAS,OAAO,GAAG;AACjB,QAAI,EAAE,mBAAmB;AACvB,aAAO;AACT,UAAM,WAAW,QAAQ,iBAAiB;AAC1C,WAAO,WAAW,QAAQ,IAAI,SAAS,CAAC,IAAI,MAAM,QAAQ,EAAE,QAAQ,OAAO,CAAC;AAAA,EAC9E;AACA,QAAM,OAAO,CAAC,GAAG,MAAM;AACrB,QAAI,MAAM,KAAK9F;AACb,MAAAA,UAAS,QAAQ,OAAO,SAAS,CAAC,IAAI,IAAI,EAAE;AAAA,EAChD,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,MAAI,QAAQ,WAAW,CAAC,QAAQ,iBAAiBA,aAAY,CAAC8F,aAAY;AACxE,yBAAqB,KAAK9F,UAAS,SAAS,OAAO,SAAS,GAAG,cAAc,OAAO,GAAG,MAAM;AAC3F,UAAIA,aAAYA,UAAS,UAAU,MAAM;AACvC,cAAM,QAAQ,OAAOA,UAAS,KAAK;AAAA,IACvC,GAAG,EAAE,WAAW,KAAK,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AAEA,IAAI+F,aAAY,OAAO;AACvB,IAAIC,uBAAsB,OAAO;AACjC,IAAIC,gBAAe,OAAO,UAAU;AACpC,IAAIC,gBAAe,OAAO,UAAU;AACpC,IAAIC,mBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAMJ,WAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAIK,kBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAIH,cAAa,KAAK,GAAG,IAAI;AAC3B,MAAAE,iBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AACpC,MAAIH;AACF,aAAS,QAAQA,qBAAoB,CAAC,GAAG;AACvC,UAAIE,cAAa,KAAK,GAAG,IAAI;AAC3B,QAAAC,iBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACpC;AACF,SAAO;AACT;AACA,IAAM,qBAAqB;AAAA,EACzB,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC7B,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC5B,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC9B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC5B,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,GAAG,IAAI;AAAA,EAC7B,aAAa,CAAC,GAAG,MAAM,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,MAAM,KAAK;AAAA,EACjC,aAAa,CAAC,MAAM,MAAM,MAAM,CAAC;AAAA,EACjC,eAAe,CAAC,MAAM,MAAM,MAAM,GAAG;AACvC;AACA,IAAM,oBAAoBC,gBAAe;AAAA,EACvC,QAAQ;AACV,GAAG,kBAAkB;AACrB,SAAS,qBAAqB,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG;AAC9C,QAAM,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,KAAK,IAAI;AACvC,QAAM,IAAI,CAAC,IAAI,OAAO,IAAI,KAAK,IAAI;AACnC,QAAM,IAAI,CAAC,OAAO,IAAI;AACtB,QAAM,aAAa,CAAC,GAAG,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,KAAK;AAC9E,QAAM,WAAW,CAAC,GAAG,IAAI,OAAO,IAAI,EAAE,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE;AAChF,QAAM,WAAW,CAAC,MAAM;AACtB,QAAI,UAAU;AACd,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAM,eAAe,SAAS,SAAS,IAAI,EAAE;AAC7C,UAAI,iBAAiB;AACnB,eAAO;AACT,YAAM,WAAW,WAAW,SAAS,IAAI,EAAE,IAAI;AAC/C,iBAAW,WAAW;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,SAAO,CAAC,MAAM,OAAO,MAAM,OAAO,KAAK,IAAI,WAAW,SAAS,CAAC,GAAG,IAAI,EAAE;AAC3E;AACA,SAAS,cAAc,QAAQ,UAAU,CAAC,GAAG;AAC3C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,EACf,IAAI;AACJ,QAAM,oBAAoB,SAAS,MAAM;AACvC,UAAM,IAAI,MAAM,UAAU;AAC1B,WAAO,WAAW,CAAC,IAAI,IAAI,qBAAqB,CAAC;AAAA,EACnD,CAAC;AACD,QAAM,cAAc,SAAS,MAAM;AACjC,UAAM,IAAI,MAAM,MAAM;AACtB,WAAO,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,KAAK;AAAA,EACtC,CAAC;AACD,QAAM,eAAe,SAAS,MAAM,SAAS,YAAY,KAAK,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK;AACzG,QAAM,eAAe,IAAI,aAAa,MAAM,MAAM,CAAC,CAAC;AACpD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,EAAE,QAAQ,MAAM,IAAI,SAAS,MAAM;AACvC,UAAMhF,OAAM,KAAK,IAAI;AACrB,UAAM,WAAW,MAAM,KAAK,QAAQA,QAAO,iBAAiB,GAAG,CAAC;AAChE,iBAAa,QAAQ,YAAY,IAAI,CAAC,KAAK,MAAM;AAC/C,UAAIxB;AACJ,aAAO,QAAQA,MAAK,WAAW,CAAC,MAAM,OAAOA,MAAK,KAAK,kBAAkB,MAAM,QAAQ;AAAA,IACzF,CAAC;AACD,QAAI,YAAY,GAAG;AACjB,YAAM;AACN,iBAAW;AAAA,IACb;AAAA,EACF,GAAG,EAAE,WAAW,MAAM,CAAC;AACvB,QAAM,QAAQ,MAAM;AAClB,UAAM;AACN,sBAAkB,MAAM,QAAQ;AAChC,iBAAa,aAAa,MAAM,IAAI,CAAC,GAAG,MAAM;AAC5C,UAAIA,KAAI;AACR,eAASA,MAAK,aAAa,MAAM,CAAC,MAAM,OAAOA,MAAK,OAAO,KAAK,aAAa,MAAM,CAAC,MAAM,OAAO,KAAK;AAAA,IACxG,CAAC;AACD,kBAAc,aAAa,MAAM,MAAM,CAAC;AACxC,cAAU,KAAK,IAAI;AACnB,YAAQ,UAAU;AAClB,WAAO;AACP,cAAU;AAAA,EACZ;AACA,QAAM,UAAU,aAAa,OAAO,OAAO,EAAE,WAAW,MAAM,CAAC;AAC/D,QAAM,cAAc,MAAM;AACxB,QAAI,MAAM,QAAQ;AAChB;AACF,QAAI,MAAM,KAAK,KAAK;AAClB,YAAM;AAAA;AAEN,cAAQ,MAAM;AAAA,EAClB,GAAG,EAAE,MAAM,KAAK,CAAC;AACjB,QAAM,MAAM,MAAM,QAAQ,GAAG,CAAC,MAAM;AAClC,QAAI,GAAG;AACL,mBAAa,QAAQ,aAAa,MAAM,MAAM,CAAC;AAC/C,YAAM;AAAA,IACR;AAAA,EACF,CAAC;AACD,SAAO,SAAS,MAAM;AACpB,UAAM,eAAe,MAAM,QAAQ,IAAI,eAAe;AACtD,WAAO,SAAS,YAAY,KAAK,IAAI,aAAa,MAAM,CAAC,IAAI,aAAa;AAAA,EAC5E,CAAC;AACH;AAEA,SAAS,mBAAmB,OAAO,WAAW,UAAU,CAAC,GAAG;AAC1D,QAAM;AAAA,IACJ,eAAe,CAAC;AAAA,IAChB,sBAAsB;AAAA,IACtB,oBAAoB;AAAA,IACpB,OAAO,cAAc;AAAA,IACrB,QAAAE,UAAS;AAAA,EACX,IAAI;AACJ,MAAI,CAACA;AACH,WAAO,SAAS,YAAY;AAC9B,QAAM,QAAQ,SAAS,CAAC,CAAC;AACzB,WAAS,eAAe;AACtB,QAAI,SAAS,WAAW;AACtB,aAAOA,QAAO,SAAS,UAAU;AAAA,IACnC,WAAW,SAAS,QAAQ;AAC1B,YAAM,OAAOA,QAAO,SAAS,QAAQ;AACrC,YAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,aAAO,QAAQ,IAAI,KAAK,MAAM,KAAK,IAAI;AAAA,IACzC,OAAO;AACL,cAAQA,QAAO,SAAS,QAAQ,IAAI,QAAQ,MAAM,EAAE;AAAA,IACtD;AAAA,EACF;AACA,WAAS,eAAe,QAAQ;AAC9B,UAAM,cAAc,OAAO,SAAS;AACpC,QAAI,SAAS;AACX,aAAO,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE,GAAGA,QAAO,SAAS,QAAQ,EAAE;AAC7E,QAAI,SAAS;AACX,aAAO,GAAGA,QAAO,SAAS,UAAU,EAAE,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE;AAC/E,UAAM,OAAOA,QAAO,SAAS,QAAQ;AACrC,UAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,QAAI,QAAQ;AACV,aAAO,GAAG,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE;AACvE,WAAO,GAAG,IAAI,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE;AAAA,EACvD;AACA,WAAS,OAAO;AACd,WAAO,IAAI,gBAAgB,aAAa,CAAC;AAAA,EAC3C;AACA,WAAS,YAAY,QAAQ;AAC3B,UAAM,aAAa,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC;AAC7C,eAAW,OAAO,OAAO,KAAK,GAAG;AAC/B,YAAM,eAAe,OAAO,OAAO,GAAG;AACtC,YAAM,GAAG,IAAI,aAAa,SAAS,IAAI,eAAe,OAAO,IAAI,GAAG,KAAK;AACzE,iBAAW,OAAO,GAAG;AAAA,IACvB;AACA,UAAM,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,EAC3D;AACA,QAAM,EAAE,OAAO,OAAO,IAAI,cAAc,OAAO,MAAM;AACnD,UAAM,SAAS,IAAI,gBAAgB,EAAE;AACrC,WAAO,KAAK,KAAK,EAAE,QAAQ,CAAC,QAAQ;AAClC,YAAM,WAAW,MAAM,GAAG;AAC1B,UAAI,MAAM,QAAQ,QAAQ;AACxB,iBAAS,QAAQ,CAAC,UAAU,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,eAC9C,uBAAuB,YAAY;AAC1C,eAAO,OAAO,GAAG;AAAA,eACV,qBAAqB,CAAC;AAC7B,eAAO,OAAO,GAAG;AAAA;AAEjB,eAAO,IAAI,KAAK,QAAQ;AAAA,IAC5B,CAAC;AACD,UAAM,MAAM;AAAA,EACd,GAAG,EAAE,MAAM,KAAK,CAAC;AACjB,WAAS,MAAM,QAAQ,cAAc;AACnC,UAAM;AACN,QAAI;AACF,kBAAY,MAAM;AACpB,IAAAA,QAAO,QAAQ,aAAaA,QAAO,QAAQ,OAAOA,QAAO,SAAS,OAAOA,QAAO,SAAS,WAAW,eAAe,MAAM,CAAC;AAC1H,WAAO;AAAA,EACT;AACA,WAAS,YAAY;AACnB,QAAI,CAAC;AACH;AACF,UAAM,KAAK,GAAG,IAAI;AAAA,EACpB;AACA,mBAAiBA,SAAQ,YAAY,WAAW,KAAK;AACrD,MAAI,SAAS;AACX,qBAAiBA,SAAQ,cAAc,WAAW,KAAK;AACzD,QAAM,UAAU,KAAK;AACrB,MAAI,QAAQ,KAAK,EAAE,KAAK,EAAE;AACxB,gBAAY,OAAO;AAAA;AAEnB,WAAO,OAAO,OAAO,YAAY;AACnC,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,MAAIF,KAAI;AACR,QAAM,UAAU,KAAKA,MAAK,QAAQ,YAAY,OAAOA,MAAK,KAAK;AAC/D,QAAM,aAAa,KAAK,KAAK,QAAQ,eAAe,OAAO,KAAK,IAAI;AACpE,QAAM,gBAAgB,IAAI,QAAQ,aAAa;AAC/C,QAAM,gBAAgB,IAAI,QAAQ,aAAa;AAC/C,QAAM,EAAE,YAAY,iBAAiB,IAAI;AACzC,QAAM,cAAc,aAAa,MAAM;AACrC,QAAIG;AACJ,YAAQA,OAAM,aAAa,OAAO,SAAS,UAAU,iBAAiB,OAAO,SAASA,KAAI;AAAA,EAC5F,CAAC;AACD,QAAM,SAAS,WAAW;AAC1B,WAAS,iBAAiB,QAAQ;AAChC,QAAI,OAAO,UAAU,UAAU,OAAO,UAAU;AAC9C,aAAO;AACT,QAAI,OAAO,SAAS;AAClB,aAAO;AACT,WAAO;AAAA,MACL,UAAU,OAAO;AAAA,IACnB;AAAA,EACF;AACA,iBAAe,SAAS;AACtB,QAAI,CAAC,YAAY,SAAS,OAAO;AAC/B;AACF,WAAO,QAAQ,MAAM,UAAU,aAAa,aAAa;AAAA,MACvD,OAAO,iBAAiB,aAAa;AAAA,MACrC,OAAO,iBAAiB,aAAa;AAAA,IACvC,CAAC;AACD,WAAO,OAAO;AAAA,EAChB;AACA,iBAAe,QAAQ;AACrB,QAAIA;AACJ,KAACA,OAAM,OAAO,UAAU,OAAO,SAASA,KAAI,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;AAC/E,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,OAAO;AACd,UAAM;AACN,YAAQ,QAAQ;AAAA,EAClB;AACA,iBAAe,QAAQ;AACrB,UAAM,OAAO;AACb,QAAI,OAAO;AACT,cAAQ,QAAQ;AAClB,WAAO,OAAO;AAAA,EAChB;AACA,iBAAe,UAAU;AACvB,UAAM;AACN,WAAO,MAAM,MAAM;AAAA,EACrB;AACA,QAAM,SAAS,CAAC,MAAM;AACpB,QAAI;AACF,aAAO;AAAA;AAEP,YAAM;AAAA,EACV,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,QAAM,CAAC,eAAe,aAAa,GAAG,MAAM;AAC1C,QAAI,WAAW,SAAS,OAAO;AAC7B,cAAQ;AAAA,EACZ,GAAG,EAAE,WAAW,KAAK,CAAC;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,UAAU,OAAO,KAAK,MAAM,UAAU,CAAC,GAAG;AACjD,MAAIH,KAAI,IAAI,IAAI,IAAI;AACpB,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,mBAAmB;AAC9B,QAAM,QAAQ,SAAS,MAAM,OAAO,SAAS,GAAG,WAAWA,MAAK,MAAM,OAAO,SAAS,GAAG,UAAU,OAAO,SAASA,IAAG,KAAK,EAAE,QAAQ,MAAM,KAAK,MAAM,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,KAAK,MAAM,OAAO,SAAS,GAAG,KAAK;AACtQ,MAAI,QAAQ;AACZ,MAAI,CAAC,KAAK;AACR,QAAI,QAAQ;AACV,YAAM,gBAAgB,MAAM,KAAK,MAAM,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,aAAa,OAAO,SAAS,GAAG;AACvH,aAAO,gBAAgB,OAAO,SAAS,aAAa,UAAU;AAC9D,UAAI,CAAC;AACH,iBAAS,gBAAgB,OAAO,SAAS,aAAa,UAAU;AAAA,IACpE,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AACA,UAAQ,aAAa,SAAS,UAAU,IAAI,SAAS,CAAC;AACtD,QAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,MAAM,WAAW,KAAK,IAAI,MAAM,GAAG,IAAI,YAAY,GAAG;AACxF,QAAMK,YAAW,MAAM,MAAM,MAAM,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG,CAAC,IAAI;AACjE,MAAI,SAAS;AACX,UAAM,eAAeA,UAAS;AAC9B,UAAM,QAAQ,IAAI,YAAY;AAC9B,UAAM,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,MAAM,QAAQ,QAAQ,CAAC,CAAC;AACvD,UAAM,OAAO,CAAC,MAAM;AAClB,UAAI,MAAM,MAAM,GAAG,KAAK;AACtB,cAAM,OAAO,CAAC;AAAA,IAClB,GAAG,EAAE,KAAK,CAAC;AACX,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS;AAAA,MACd,MAAM;AACJ,eAAOA,UAAS;AAAA,MAClB;AAAA,MACA,IAAI,OAAO;AACT,cAAM,OAAO,KAAK;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,WAAW,OAAO,MAAM,UAAU,CAAC,GAAG;AAC7C,QAAM,MAAM,CAAC;AACb,aAAW,OAAO;AAChB,QAAI,GAAG,IAAI,UAAU,OAAO,KAAK,MAAM,OAAO;AAChD,SAAO;AACT;AAEA,SAAS,WAAW,SAAS;AAC3B,QAAM;AAAA,IACJ,UAAU,CAAC;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,EACd,IAAI,WAAW,CAAC;AAChB,QAAM,cAAc,aAAa,MAAM,OAAO,cAAc,eAAe,aAAa,SAAS;AACjG,QAAM,aAAa,WAAW,OAAO;AACrC,MAAI;AACJ,QAAM,UAAU,CAAC,WAAW,WAAW,UAAU;AAC/C,QAAI,YAAY;AACd,gBAAU,QAAQ,QAAQ;AAAA,EAC9B;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,YAAY;AACd,gBAAU,QAAQ,CAAC;AACrB,wBAAoB,OAAO,SAAS,iBAAiB,MAAM;AAAA,EAC7D;AACA,MAAI,WAAW,GAAG;AAChB,uBAAmB,cAAc,SAAS,UAAU;AAAA,MAClD,WAAW;AAAA,MACX,mBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAe,MAAM,SAAS;AACrC,QAAM,EAAE,gBAAgB,cAAc,UAAU,gBAAgB,aAAa,aAAa,IAAI,gBAAgB,UAAU,uBAAuB,SAAS,IAAI,IAAI,yBAAyB,SAAS,IAAI;AACtM,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,gBAAgB;AAAA,MACd,KAAK;AAAA,MACL,UAAU,MAAM;AACd,uBAAe;AAAA,MACjB;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,wBAAwB,MAAM;AACrC,QAAM,eAAe,IAAI,IAAI;AAC7B,QAAM,OAAO,eAAe,YAAY;AACxC,QAAM,cAAc,IAAI,CAAC,CAAC;AAC1B,QAAM,SAAS,WAAW,IAAI;AAC9B,QAAM,QAAQ,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG,CAAC;AACvC,SAAO,EAAE,OAAO,QAAQ,aAAa,MAAM,aAAa;AAC1D;AACA,SAAS,sBAAsB,OAAO,QAAQ,UAAU;AACtD,SAAO,CAAC,kBAAkB;AACxB,QAAI,OAAO,aAAa;AACtB,aAAO,KAAK,KAAK,gBAAgB,QAAQ;AAC3C,UAAM,EAAE,QAAQ,EAAE,IAAI,MAAM;AAC5B,QAAI,MAAM;AACV,QAAI,WAAW;AACf,aAAS,IAAI,OAAO,IAAI,OAAO,MAAM,QAAQ,KAAK;AAChD,YAAM,OAAO,SAAS,CAAC;AACvB,aAAO;AACP,iBAAW;AACX,UAAI,MAAM;AACR;AAAA,IACJ;AACA,WAAO,WAAW;AAAA,EACpB;AACF;AACA,SAAS,gBAAgB,QAAQ,UAAU;AACzC,SAAO,CAAC,oBAAoB;AAC1B,QAAI,OAAO,aAAa;AACtB,aAAO,KAAK,MAAM,kBAAkB,QAAQ,IAAI;AAClD,QAAI,MAAM;AACV,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,OAAO,MAAM,QAAQ,KAAK;AAC5C,YAAM,OAAO,SAAS,CAAC;AACvB,aAAO;AACP,UAAI,OAAO,iBAAiB;AAC1B,iBAAS;AACT;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS;AAAA,EAClB;AACF;AACA,SAAS,qBAAqB,MAAM,UAAU,WAAW,iBAAiB,EAAE,cAAc,OAAO,aAAa,OAAO,GAAG;AACtH,SAAO,MAAM;AACX,UAAM,UAAU,aAAa;AAC7B,QAAI,SAAS;AACX,YAAM,SAAS,UAAU,SAAS,aAAa,QAAQ,YAAY,QAAQ,UAAU;AACrF,YAAM,eAAe,gBAAgB,SAAS,aAAa,QAAQ,eAAe,QAAQ,WAAW;AACrG,YAAM,OAAO,SAAS;AACtB,YAAM,KAAK,SAAS,eAAe;AACnC,YAAM,QAAQ;AAAA,QACZ,OAAO,OAAO,IAAI,IAAI;AAAA,QACtB,KAAK,KAAK,OAAO,MAAM,SAAS,OAAO,MAAM,SAAS;AAAA,MACxD;AACA,kBAAY,QAAQ,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,KAAK,WAAW;AAAA,QAC9F,MAAM;AAAA,QACN,OAAO,QAAQ,MAAM,MAAM;AAAA,MAC7B,EAAE;AAAA,IACJ;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,UAAU,QAAQ;AAC3C,SAAO,CAAC,UAAU;AAChB,QAAI,OAAO,aAAa,UAAU;AAChC,YAAM,QAAQ,QAAQ;AACtB,aAAO;AAAA,IACT;AACA,UAAM,OAAO,OAAO,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,MAAM,MAAM,SAAS,CAAC,GAAG,CAAC;AACpF,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,MAAM,MAAM,gBAAgB;AACpD,QAAM,CAAC,KAAK,OAAO,KAAK,QAAQ,IAAI,GAAG,MAAM;AAC3C,mBAAe;AAAA,EACjB,CAAC;AACH;AACA,SAAS,wBAAwB,UAAU,QAAQ;AACjD,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO,aAAa;AACtB,aAAO,OAAO,MAAM,SAAS;AAC/B,WAAO,OAAO,MAAM,OAAO,CAAC,KAAK,GAAG,UAAU,MAAM,SAAS,KAAK,GAAG,CAAC;AAAA,EACxE,CAAC;AACH;AACA,IAAM,wCAAwC;AAAA,EAC5C,YAAY;AAAA,EACZ,UAAU;AACZ;AACA,SAAS,eAAe,MAAM,gBAAgB,aAAa,cAAc;AACvE,SAAO,CAAC,UAAU;AAChB,QAAI,aAAa,OAAO;AACtB,mBAAa,MAAM,sCAAsC,IAAI,CAAC,IAAI,YAAY,KAAK;AACnF,qBAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,SAAS,yBAAyB,SAAS,MAAM;AAC/C,QAAM,YAAY,wBAAwB,IAAI;AAC9C,QAAM,EAAE,OAAO,QAAQ,aAAa,MAAM,aAAa,IAAI;AAC3D,QAAM,iBAAiB,EAAE,WAAW,OAAO;AAC3C,QAAM,EAAE,WAAW,WAAW,EAAE,IAAI;AACpC,QAAM,kBAAkB,sBAAsB,OAAO,QAAQ,SAAS;AACtE,QAAM,YAAY,gBAAgB,QAAQ,SAAS;AACnD,QAAM,iBAAiB,qBAAqB,cAAc,UAAU,WAAW,iBAAiB,SAAS;AACzG,QAAM,kBAAkB,kBAAkB,WAAW,MAAM;AAC3D,QAAM,aAAa,SAAS,MAAM,gBAAgB,MAAM,MAAM,KAAK,CAAC;AACpE,QAAM,aAAa,wBAAwB,WAAW,MAAM;AAC5D,mBAAiB,MAAM,MAAM,cAAc;AAC3C,QAAM,WAAW,eAAe,cAAc,gBAAgB,iBAAiB,YAAY;AAC3F,QAAM,eAAe,SAAS,MAAM;AAClC,WAAO;AAAA,MACL,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,OAAO,GAAG,WAAW,QAAQ,WAAW,KAAK;AAAA,QAC7C,YAAY,GAAG,WAAW,KAAK;AAAA,QAC/B,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,uBAAuB,SAAS,MAAM;AAC7C,QAAM,YAAY,wBAAwB,IAAI;AAC9C,QAAM,EAAE,OAAO,QAAQ,aAAa,MAAM,aAAa,IAAI;AAC3D,QAAM,iBAAiB,EAAE,WAAW,OAAO;AAC3C,QAAM,EAAE,YAAY,WAAW,EAAE,IAAI;AACrC,QAAM,kBAAkB,sBAAsB,OAAO,QAAQ,UAAU;AACvE,QAAM,YAAY,gBAAgB,QAAQ,UAAU;AACpD,QAAM,iBAAiB,qBAAqB,YAAY,UAAU,WAAW,iBAAiB,SAAS;AACvG,QAAM,iBAAiB,kBAAkB,YAAY,MAAM;AAC3D,QAAM,YAAY,SAAS,MAAM,eAAe,MAAM,MAAM,KAAK,CAAC;AAClE,QAAM,cAAc,wBAAwB,YAAY,MAAM;AAC9D,mBAAiB,MAAM,MAAM,cAAc;AAC3C,QAAM,WAAW,eAAe,YAAY,gBAAgB,gBAAgB,YAAY;AACxF,QAAM,eAAe,SAAS,MAAM;AAClC,WAAO;AAAA,MACL,OAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ,GAAG,YAAY,QAAQ,UAAU,KAAK;AAAA,QAC9C,WAAW,GAAG,UAAU,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,cAAc,CAAC,UAAU,CAAC,MAAM;AACpC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,UAAAD,YAAW;AAAA,EACb,IAAI;AACJ,MAAI;AACJ,QAAM,cAAc,aAAa,MAAM,aAAa,cAAc,SAAS;AAC3E,QAAM,WAAW,IAAI,KAAK;AAC1B,iBAAe,qBAAqB;AAClC,QAAI,CAAC,YAAY,SAAS,CAAC;AACzB;AACF,QAAIA,aAAYA,UAAS,oBAAoB;AAC3C,iBAAW,MAAM,UAAU,SAAS,QAAQ,QAAQ;AACtD,aAAS,QAAQ,CAAC,SAAS;AAAA,EAC7B;AACA,MAAIA;AACF,qBAAiBA,WAAU,oBAAoB,oBAAoB,EAAE,SAAS,KAAK,CAAC;AACtF,iBAAe,QAAQ,MAAM;AAC3B,QAAI,CAAC,YAAY;AACf;AACF,eAAW,MAAM,UAAU,SAAS,QAAQ,IAAI;AAChD,aAAS,QAAQ,CAAC,SAAS;AAAA,EAC7B;AACA,iBAAe,UAAU;AACvB,QAAI,CAAC,YAAY,SAAS,CAAC;AACzB;AACF,UAAM,SAAS,QAAQ;AACvB,aAAS,QAAQ,CAAC,SAAS;AAC3B,eAAW;AAAA,EACb;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,CAACqG,kBAAiB,CAAC,MAAM;AAClD,QAAM;AAAA,IACJ,QAAAvG,UAAS;AAAA,EACX,IAAIuG;AACJ,QAAM,cAAc,aAAa,MAAM,CAAC,CAACvG,WAAU,kBAAkBA,OAAM;AAC3E,QAAM,eAAe,IAAI,IAAI;AAC7B,QAAM,oBAAoB,YAAY;AACpC,QAAI,CAAC,YAAY;AACf;AACF,QAAI,gBAAgB,gBAAgB,aAAa,eAAe;AAC9D,YAAM,aAAa,kBAAkB;AAAA,EACzC;AACA,QAAM,UAAU,gBAAgB;AAChC,QAAM,SAAS,gBAAgB;AAC/B,QAAM,UAAU,gBAAgB;AAChC,QAAM,UAAU,gBAAgB;AAChC,QAAM,OAAO,OAAO,cAAc;AAChC,QAAI,CAAC,YAAY;AACf;AACF,UAAM,kBAAkB;AACxB,UAAM,UAAU,OAAO,OAAO,CAAC,GAAGuG,iBAAgB,SAAS;AAC3D,iBAAa,QAAQ,IAAI,aAAa,QAAQ,SAAS,IAAI,OAAO;AAClE,iBAAa,MAAM,UAAU,CAAC,UAAU,QAAQ,QAAQ,KAAK;AAC7D,iBAAa,MAAM,SAAS,CAAC,UAAU,OAAO,QAAQ,KAAK;AAC3D,iBAAa,MAAM,UAAU,CAAC,UAAU,QAAQ,QAAQ,KAAK;AAC7D,iBAAa,MAAM,UAAU,CAAC,UAAU,QAAQ,QAAQ,KAAK;AAC7D,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,aAAa;AACf,mBAAa,MAAM,MAAM;AAC3B,iBAAa,QAAQ;AAAA,EACvB;AACA,eAAa,YAAY;AACvB,QAAI,YAAY;AACd,YAAM,kBAAkB;AAAA,EAC5B,CAAC;AACD,oBAAkB,KAAK;AACvB,MAAI,YAAY,SAASvG,SAAQ;AAC/B,UAAME,YAAWF,QAAO;AACxB,qBAAiBE,WAAU,oBAAoB,CAAC,MAAM;AACpD,QAAE,eAAe;AACjB,UAAIA,UAAS,oBAAoB,WAAW;AAC1C,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,uBAAuB;AAC7B,SAAS,qBAAqB,SAAS;AACrC,MAAI,YAAY;AACd,WAAO,CAAC;AACV,SAAO;AACT;AACA,SAAS,aAAa,KAAK,UAAU,CAAC,GAAG;AACvC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY,CAAC;AAAA,EACf,IAAI;AACJ,QAAM,OAAO,IAAI,IAAI;AACrB,QAAM,SAAS,IAAI,QAAQ;AAC3B,QAAM,QAAQ,IAAI;AAClB,QAAM,SAAS,WAAW,GAAG;AAC7B,MAAI;AACJ,MAAI;AACJ,MAAI,mBAAmB;AACvB,MAAI,UAAU;AACd,MAAI,eAAe,CAAC;AACpB,MAAI;AACJ,QAAM,QAAQ,CAAC,OAAO,KAAK,WAAW;AACpC,QAAI,CAAC,MAAM;AACT;AACF,uBAAmB;AACnB,sBAAkB,OAAO,SAAS,eAAe;AACjD,UAAM,MAAM,MAAM,MAAM,MAAM;AAAA,EAChC;AACA,QAAM,cAAc,MAAM;AACxB,QAAI,aAAa,UAAU,MAAM,SAAS,OAAO,UAAU,QAAQ;AACjE,iBAAW,UAAU;AACnB,cAAM,MAAM,KAAK,MAAM;AACzB,qBAAe,CAAC;AAAA,IAClB;AAAA,EACF;AACA,QAAM,iBAAiB,MAAM;AAC3B,iBAAa,eAAe;AAC5B,sBAAkB;AAAA,EACpB;AACA,QAAM,OAAO,CAAC,OAAO,YAAY,SAAS;AACxC,QAAI,CAAC,MAAM,SAAS,OAAO,UAAU,QAAQ;AAC3C,UAAI;AACF,qBAAa,KAAK,KAAK;AACzB,aAAO;AAAA,IACT;AACA,gBAAY;AACZ,UAAM,MAAM,KAAK,KAAK;AACtB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,oBAAoB,OAAO,OAAO,UAAU;AAC9C;AACF,UAAM,KAAK,IAAI,UAAU,OAAO,OAAO,SAAS;AAChD,UAAM,QAAQ;AACd,WAAO,QAAQ;AACf,OAAG,SAAS,MAAM;AAChB,aAAO,QAAQ;AACf,qBAAe,OAAO,SAAS,YAAY,EAAE;AAC7C,yBAAmB,OAAO,SAAS,gBAAgB;AACnD,kBAAY;AAAA,IACd;AACA,OAAG,UAAU,CAAC,OAAO;AACnB,aAAO,QAAQ;AACf,YAAM,QAAQ;AACd,wBAAkB,OAAO,SAAS,eAAe,IAAI,EAAE;AACvD,UAAI,CAAC,oBAAoB,QAAQ,eAAe;AAC9C,cAAM;AAAA,UACJ,UAAU;AAAA,UACV,QAAQ;AAAA,UACR;AAAA,QACF,IAAI,qBAAqB,QAAQ,aAAa;AAC9C,mBAAW;AACX,YAAI,OAAO,YAAY,aAAa,UAAU,KAAK,UAAU;AAC3D,qBAAW,OAAO,KAAK;AAAA,iBAChB,OAAO,YAAY,cAAc,QAAQ;AAChD,qBAAW,OAAO,KAAK;AAAA;AAEvB,sBAAY,OAAO,SAAS,SAAS;AAAA,MACzC;AAAA,IACF;AACA,OAAG,UAAU,CAAC,MAAM;AAClB,iBAAW,OAAO,SAAS,QAAQ,IAAI,CAAC;AAAA,IAC1C;AACA,OAAG,YAAY,CAAC,MAAM;AACpB,UAAI,QAAQ,WAAW;AACrB,uBAAe;AACf,cAAM;AAAA,UACJ,UAAU;AAAA,QACZ,IAAI,qBAAqB,QAAQ,SAAS;AAC1C,YAAI,EAAE,SAAS;AACb;AAAA,MACJ;AACA,WAAK,QAAQ,EAAE;AACf,mBAAa,OAAO,SAAS,UAAU,IAAI,CAAC;AAAA,IAC9C;AAAA,EACF;AACA,MAAI,QAAQ,WAAW;AACrB,UAAM;AAAA,MACJ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,IAChB,IAAI,qBAAqB,QAAQ,SAAS;AAC1C,UAAM,EAAE,OAAO,OAAO,IAAI,cAAc,MAAM;AAC5C,WAAK,SAAS,KAAK;AACnB,UAAI,mBAAmB;AACrB;AACF,wBAAkB,WAAW,MAAM;AACjC,cAAM;AAAA,MACR,GAAG,WAAW;AAAA,IAChB,GAAG,UAAU,EAAE,WAAW,MAAM,CAAC;AACjC,qBAAiB;AACjB,sBAAkB;AAAA,EACpB;AACA,MAAI,WAAW;AACb,qBAAiB,QAAQ,gBAAgB,MAAM,MAAM,CAAC;AACtD,sBAAkB,KAAK;AAAA,EACzB;AACA,QAAM,OAAO,MAAM;AACjB,UAAM;AACN,uBAAmB;AACnB,cAAU;AACV,UAAM;AAAA,EACR;AACA,MAAI;AACF,UAAM,QAAQ,MAAM,EAAE,WAAW,KAAK,CAAC;AACzC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI;AAAA,EACN;AACF;AAEA,SAAS,aAAa,MAAM,eAAe,SAAS;AAClD,QAAM;AAAA,IACJ,QAAAF,UAAS;AAAA,EACX,IAAI,WAAW,OAAO,UAAU,CAAC;AACjC,QAAM,OAAO,IAAI,IAAI;AACrB,QAAM,SAAS,WAAW;AAC1B,QAAM,OAAO,SAAS,MAAM,KAAK;AAC/B,QAAI,CAAC,OAAO;AACV;AACF,WAAO,MAAM,YAAY,GAAG;AAAA,EAC9B;AACA,QAAM,YAAY,SAAS,aAAa;AACtC,QAAI,CAAC,OAAO;AACV;AACF,WAAO,MAAM,UAAU;AAAA,EACzB;AACA,MAAIA,SAAQ;AACV,QAAI,SAAS,IAAI;AACf,aAAO,QAAQ,IAAI,OAAO,MAAM,aAAa;AAAA,aACtC,WAAW,IAAI;AACtB,aAAO,QAAQ,KAAK;AAAA;AAEpB,aAAO,QAAQ;AACjB,WAAO,MAAM,YAAY,CAAC,MAAM;AAC9B,WAAK,QAAQ,EAAE;AAAA,IACjB;AACA,sBAAkB,MAAM;AACtB,UAAI,OAAO;AACT,eAAO,MAAM,UAAU;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAY,CAAC,aAAa,CAAC,MAAM;AACrC,QAAM,eAAe,EAAE,KAAK,CAAC;AAC7B,SAAO,QAAQ,QAAQ,SAAS,MAAM,QAAQ,YAAY,CAAC,EAAE,KAAK,CAAC,WAAW;AAC5E,gBAAY,CAAC,WAAW,MAAM,CAAC;AAAA,EACjC,CAAC,EAAE,MAAM,CAAC,UAAU;AAClB,gBAAY,CAAC,SAAS,KAAK,CAAC;AAAA,EAC9B,CAAC;AACH;AAEA,IAAM,aAAa,CAAC,SAAS;AAC3B,MAAI,KAAK,WAAW;AAClB,WAAO;AACT,QAAM,aAAa,KAAK,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,EAAE,SAAS;AAC1D,SAAO,iBAAiB,UAAU;AACpC;AAEA,IAAM,sBAAsB,CAAC,IAAI,SAAS;AACxC,QAAM,WAAW,GAAG,WAAW,IAAI,CAAC,gBAAgB,SAAS,KAAK,EAAE;AACpE,QAAM,OAAO,IAAI,KAAK,CAAC,QAAQ,GAAG,EAAE,MAAM,kBAAkB,CAAC;AAC7D,QAAM,MAAM,IAAI,gBAAgB,IAAI;AACpC,SAAO;AACT;AAEA,IAAM,iBAAiB,CAAC,IAAI,UAAU,CAAC,MAAM;AAC3C,QAAM;AAAA,IACJ,eAAe,CAAC;AAAA,IAChB;AAAA,IACA,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,SAAS,IAAI;AACnB,QAAM,eAAe,IAAI,SAAS;AAClC,QAAM,UAAU,IAAI,CAAC,CAAC;AACtB,QAAM,YAAY,IAAI;AACtB,QAAM,kBAAkB,CAAC,SAAS,cAAc;AAC9C,QAAI,OAAO,SAAS,OAAO,MAAM,QAAQA,SAAQ;AAC/C,aAAO,MAAM,UAAU;AACvB,UAAI,gBAAgB,OAAO,MAAM,IAAI;AACrC,cAAQ,QAAQ,CAAC;AACjB,aAAO,QAAQ;AACf,MAAAA,QAAO,aAAa,UAAU,KAAK;AACnC,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,kBAAgB;AAChB,oBAAkB,eAAe;AACjC,QAAM,iBAAiB,MAAM;AAC3B,UAAM,UAAU,oBAAoB,IAAI,YAAY;AACpD,UAAM,YAAY,IAAI,OAAO,OAAO;AACpC,cAAU,OAAO;AACjB,cAAU,YAAY,CAAC,MAAM;AAC3B,YAAM,EAAE,UAAU,MAAM;AAAA,MACxB,GAAG,SAAS,MAAM;AAAA,MAClB,EAAE,IAAI,QAAQ;AACd,YAAM,CAAC,QAAQ,MAAM,IAAI,EAAE;AAC3B,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,kBAAQ,MAAM;AACd,0BAAgB,MAAM;AACtB;AAAA,QACF;AACE,iBAAO,MAAM;AACb,0BAAgB,OAAO;AACvB;AAAA,MACJ;AAAA,IACF;AACA,cAAU,UAAU,CAAC,MAAM;AACzB,YAAM,EAAE,SAAS,MAAM;AAAA,MACvB,EAAE,IAAI,QAAQ;AACd,aAAO,CAAC;AACR,sBAAgB,OAAO;AAAA,IACzB;AACA,QAAI,SAAS;AACX,gBAAU,QAAQ,WAAW,MAAM,gBAAgB,iBAAiB,GAAG,OAAO;AAAA,IAChF;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,IAAI,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjE,YAAQ,QAAQ;AAAA,MACd;AAAA,MACA;AAAA,IACF;AACA,WAAO,SAAS,OAAO,MAAM,YAAY,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AACtD,iBAAa,QAAQ;AAAA,EACvB,CAAC;AACD,QAAM,WAAW,IAAI,WAAW;AAC9B,QAAI,aAAa,UAAU,WAAW;AACpC,cAAQ,MAAM,yEAAyE;AACvF,aAAO,QAAQ,OAAO;AAAA,IACxB;AACA,WAAO,QAAQ,eAAe;AAC9B,WAAO,WAAW,GAAG,MAAM;AAAA,EAC7B;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAe,EAAE,QAAAA,UAAS,cAAc,IAAI,CAAC,GAAG;AACvD,MAAI,CAACA;AACH,WAAO,IAAI,KAAK;AAClB,QAAM,UAAU,IAAIA,QAAO,SAAS,SAAS,CAAC;AAC9C,mBAAiBA,SAAQ,QAAQ,MAAM;AACrC,YAAQ,QAAQ;AAAA,EAClB,CAAC;AACD,mBAAiBA,SAAQ,SAAS,MAAM;AACtC,YAAQ,QAAQ;AAAA,EAClB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,gBAAgB,EAAE,QAAAA,UAAS,cAAc,IAAI,CAAC,GAAG;AACxD,MAAI,CAACA,SAAQ;AACX,WAAO;AAAA,MACL,GAAG,IAAI,CAAC;AAAA,MACR,GAAG,IAAI,CAAC;AAAA,IACV;AAAA,EACF;AACA,QAAM,IAAI,IAAIA,QAAO,OAAO;AAC5B,QAAM,IAAI,IAAIA,QAAO,OAAO;AAC5B,mBAAiBA,SAAQ,UAAU,MAAM;AACvC,MAAE,QAAQA,QAAO;AACjB,MAAE,QAAQA,QAAO;AAAA,EACnB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,SAAS;AAAA,EACX,CAAC;AACD,SAAO,EAAE,GAAG,EAAE;AAChB;AAEA,SAAS,cAAc,UAAU,CAAC,GAAG;AACnC,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,IACT,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,EACrB,IAAI;AACJ,QAAM,QAAQ,IAAI,YAAY;AAC9B,QAAM,SAAS,IAAI,aAAa;AAChC,QAAM,SAAS,MAAM;AACnB,QAAIA,SAAQ;AACV,UAAI,kBAAkB;AACpB,cAAM,QAAQA,QAAO;AACrB,eAAO,QAAQA,QAAO;AAAA,MACxB,OAAO;AACL,cAAM,QAAQA,QAAO,SAAS,gBAAgB;AAC9C,eAAO,QAAQA,QAAO,SAAS,gBAAgB;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACP,eAAa,MAAM;AACnB,mBAAiB,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACpD,MAAI;AACF,qBAAiB,qBAAqB,QAAQ,EAAE,SAAS,KAAK,CAAC;AACjE,SAAO,EAAE,OAAO,OAAO;AACzB;", "names": ["_a", "invoke", "keys", "get", "set", "ref", "toRefs", "_a", "events", "window", "_a2", "document", "getValue", "defaults", "set", "__defProps$6", "__getOwnPropDescs$6", "__spreadProps$6", "__defProps$5", "__getOwnPropDescs$5", "__spreadProps$5", "onUpdated", "__defProps$4", "__getOwnPropDescs$4", "__spreadProps$4", "toRefs", "__objRest$2", "timestamp", "__defProps$3", "__getOwnPropDescs$3", "__spreadProps$3", "now", "__getOwnPropSymbols$b", "__hasOwnProp$b", "__propIsEnum$b", "__defProp$9", "__defProps$2", "__getOwnPropDescs$2", "__getOwnPropSymbols$a", "__hasOwnProp$a", "__propIsEnum$a", "__defNormalProp$9", "__spreadValues$9", "__spreadProps$2", "keys", "__defProp$8", "__getOwnPropSymbols$9", "__hasOwnProp$9", "__propIsEnum$9", "__defNormalProp$8", "__spreadValues$8", "__getOwnPropSymbols$8", "__hasOwnProp$8", "__propIsEnum$8", "__objRest$1", "__defProp$7", "__getOwnPropSymbols$7", "__hasOwnProp$7", "__propIsEnum$7", "__defNormalProp$7", "__spreadValues$7", "__defProp$6", "__defProps$1", "__getOwnPropDescs$1", "__getOwnPropSymbols$6", "__hasOwnProp$6", "__propIsEnum$6", "__defNormalProp$6", "__spreadValues$6", "__spreadProps$1", "__defProp$5", "__getOwnPropSymbols$5", "__hasOwnProp$5", "__propIsEnum$5", "__defNormalProp$5", "__spreadValues$5", "get", "__defProp$4", "__getOwnPropSymbols$4", "__hasOwnProp$4", "__propIsEnum$4", "__defNormalProp$4", "__spreadValues$4", "__defProp$3", "__defProps", "__getOwnPropDescs", "__getOwnPropSymbols$3", "__hasOwnProp$3", "__propIsEnum$3", "__defNormalProp$3", "__spreadValues$3", "__spreadProps", "__defProp$2", "__getOwnPropSymbols$2", "__hasOwnProp$2", "__propIsEnum$2", "__defNormalProp$2", "__spreadValues$2", "__objRest", "__defProp$1", "__getOwnPropSymbols$1", "__hasOwnProp$1", "__propIsEnum$1", "__defNormalProp$1", "__spreadValues$1", "is<PERSON><PERSON><PERSON>ly", "__defProp", "__getOwnPropSymbols", "__hasOwnProp", "__propIsEnum", "__defNormalProp", "__spreadValues", "defaultOptions"]}